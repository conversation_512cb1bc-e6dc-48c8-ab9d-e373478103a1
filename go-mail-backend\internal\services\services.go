package services

import (
	"fmt"
	"go-mail/internal/auth"
	"go-mail/internal/database"
	"go-mail/internal/manager"
	"go-mail/internal/services/activation"
	"go-mail/internal/services/config"
	"go-mail/internal/services/mailbox"
	"go-mail/internal/services/monitor"
	"go-mail/internal/services/task_log"
)

// Services 业务服务集合
type Services struct {
	Auth              *AuthService
	Activation        *activation.Service
	Mailbox           *mailbox.Service
	Monitor           *monitor.Service
	Config            *config.Service
	MailboxManagement *MailboxManagementService
	TaskLog           *task_log.TaskLogService
}

// NewServices 创建业务服务集合
func NewServices(mailManager *manager.MailManager, db *database.Database, authService *auth.Auth) *Services {
	// 创建详细日志服务（使用系统临时目录）
	detailLogService := task_log.NewDetailLogService("")

	// 创建任务日志服务
	taskLogService := task_log.NewTaskLogService(db, detailLogService)

	// 为邮件管理器设置日志服务
	mailManager.SetLogServices(taskLogService, detailLogService)

	return &Services{
		Auth:              NewAuthService(db, authService),
		Activation:        activation.NewService(db, authService),
		Mailbox:           mailbox.NewService(mailManager, db),
		Monitor:           monitor.NewService(mailManager, db),
		Config:            config.NewService(db),
		MailboxManagement: NewMailboxManagementService(db, mailManager, authService, taskLogService),
		TaskLog:           taskLogService,
	}
}

// AuthService 认证服务
type AuthService struct {
	db   *database.Database
	auth *auth.Auth
}

// NewAuthService 创建认证服务
func NewAuthService(db *database.Database, authService *auth.Auth) *AuthService {
	return &AuthService{
		db:   db,
		auth: authService,
	}
}

// Login 管理员登录
func (s *AuthService) Login(username, password string) (*database.TokenResponse, error) {
	// 查询管理员用户
	var user database.AdminUser
	err := s.db.GetDB().QueryRow(`
		SELECT id, username, password, role, status, last_login
		FROM admin_users
		WHERE username = ? AND status = 'active'`,
		username).Scan(&user.ID, &user.Username, &user.Password, &user.Role, &user.Status, &user.LastLogin)

	if err != nil {
		return nil, fmt.Errorf("用户不存在或已禁用")
	}

	// 验证密码
	if !s.auth.VerifyPassword(password, user.Password) {
		return nil, fmt.Errorf("密码错误")
	}

	// 生成令牌对
	tokenInfo, err := s.auth.GenerateTokenPair(user.ID, user.Username, user.Role)
	if err != nil {
		return nil, err
	}

	// 更新最后登录时间
	_, err = s.db.GetDB().Exec(`
		UPDATE admin_users
		SET last_login = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
		WHERE id = ?`, user.ID)
	if err != nil {
		// 记录日志但不影响登录
		fmt.Printf("更新最后登录时间失败: %v\n", err)
	}

	return &database.TokenResponse{
		Token:        tokenInfo.AccessToken,
		RefreshToken: tokenInfo.RefreshToken,
		ExpiresIn:    tokenInfo.ExpiresIn,
		User:         user,
	}, nil
}

// RefreshToken 刷新令牌
func (s *AuthService) RefreshToken(refreshToken string) (*database.TokenResponse, error) {
	// 验证刷新令牌
	claims, err := s.auth.ValidateJWT(refreshToken)
	if err != nil {
		return nil, fmt.Errorf("无效的刷新令牌")
	}

	// 检查是否是刷新令牌
	if claims.Role != "refresh" {
		return nil, fmt.Errorf("无效的刷新令牌类型")
	}

	// 查询用户信息
	var user database.AdminUser
	err = s.db.GetDB().QueryRow(`
		SELECT id, username, password, role, status, last_login
		FROM admin_users
		WHERE id = ? AND status = 'active'`,
		claims.UserID).Scan(&user.ID, &user.Username, &user.Password, &user.Role, &user.Status, &user.LastLogin)

	if err != nil {
		return nil, fmt.Errorf("用户不存在或已禁用")
	}

	// 生成新的令牌对
	tokenInfo, err := s.auth.GenerateTokenPair(user.ID, user.Username, user.Role)
	if err != nil {
		return nil, err
	}

	return &database.TokenResponse{
		Token:        tokenInfo.AccessToken,
		RefreshToken: tokenInfo.RefreshToken,
		ExpiresIn:    tokenInfo.ExpiresIn,
		User:         user,
	}, nil
}

// Logout 登出
func (s *AuthService) Logout(token string) error {
	// TODO: 实现令牌黑名单机制
	// 这里可以将令牌加入黑名单，防止被重复使用
	return nil
}

// ValidateUser 验证用户
func (s *AuthService) ValidateUser(userID int) (*database.AdminUser, error) {
	var user database.AdminUser
	err := s.db.GetDB().QueryRow(`
		SELECT id, username, role, status, last_login, created_at, updated_at
		FROM admin_users
		WHERE id = ? AND status = 'active'`,
		userID).Scan(&user.ID, &user.Username, &user.Role, &user.Status,
		&user.LastLogin, &user.CreatedAt, &user.UpdatedAt)

	if err != nil {
		return nil, fmt.Errorf("用户不存在或已禁用")
	}

	return &user, nil
}

// CreateUser 创建用户
func (s *AuthService) CreateUser(username, password, role string) (*database.AdminUser, error) {
	// 检查用户名是否已存在
	var count int
	err := s.db.GetDB().QueryRow("SELECT COUNT(*) FROM admin_users WHERE username = ?", username).Scan(&count)
	if err != nil {
		return nil, err
	}

	if count > 0 {
		return nil, fmt.Errorf("用户名已存在")
	}

	// 哈希密码
	hashedPassword := s.auth.HashPassword(password)

	// 插入新用户
	result, err := s.db.GetDB().Exec(`
		INSERT INTO admin_users (username, password, role, status)
		VALUES (?, ?, ?, 'active')`,
		username, hashedPassword, role)
	if err != nil {
		return nil, err
	}

	// 获取新用户ID
	userID, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	// 查询并返回新用户信息
	var user database.AdminUser
	err = s.db.GetDB().QueryRow(`
		SELECT id, username, role, status, created_at, updated_at
		FROM admin_users WHERE id = ?`,
		userID).Scan(&user.ID, &user.Username, &user.Role, &user.Status,
		&user.CreatedAt, &user.UpdatedAt)

	if err != nil {
		return nil, err
	}

	return &user, nil
}

// UpdateUserPassword 更新用户密码
func (s *AuthService) UpdateUserPassword(userID int, oldPassword, newPassword string) error {
	// 查询当前密码
	var currentPassword string
	err := s.db.GetDB().QueryRow("SELECT password FROM admin_users WHERE id = ?", userID).Scan(&currentPassword)
	if err != nil {
		return fmt.Errorf("用户不存在")
	}

	// 验证旧密码
	if !s.auth.VerifyPassword(oldPassword, currentPassword) {
		return fmt.Errorf("旧密码错误")
	}

	// 哈希新密码
	hashedNewPassword := s.auth.HashPassword(newPassword)

	// 更新密码
	_, err = s.db.GetDB().Exec(`
		UPDATE admin_users
		SET password = ?, updated_at = CURRENT_TIMESTAMP
		WHERE id = ?`,
		hashedNewPassword, userID)

	return err
}

// DisableUser 禁用用户
func (s *AuthService) DisableUser(userID int) error {
	_, err := s.db.GetDB().Exec(`
		UPDATE admin_users
		SET status = 'disabled', updated_at = CURRENT_TIMESTAMP
		WHERE id = ?`,
		userID)
	return err
}

// EnableUser 启用用户
func (s *AuthService) EnableUser(userID int) error {
	_, err := s.db.GetDB().Exec(`
		UPDATE admin_users
		SET status = 'active', updated_at = CURRENT_TIMESTAMP
		WHERE id = ?`,
		userID)
	return err
}

// ListUsers 获取用户列表
func (s *AuthService) ListUsers() ([]database.AdminUser, error) {
	rows, err := s.db.GetDB().Query(`
		SELECT id, username, role, status, last_login, created_at, updated_at
		FROM admin_users
		ORDER BY created_at DESC`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var users []database.AdminUser
	for rows.Next() {
		var user database.AdminUser
		err := rows.Scan(&user.ID, &user.Username, &user.Role, &user.Status,
			&user.LastLogin, &user.CreatedAt, &user.UpdatedAt)
		if err != nil {
			return nil, err
		}
		users = append(users, user)
	}

	return users, nil
}
