<template>
  <div class="mailbox-management">
    <n-card title="邮箱管理" class="mb-4">
      <template #header-extra>
        <n-space>
          <n-button type="primary" @click="showBatchImportModal = true">
            <template #icon>
              <n-icon><CloudUploadOutline /></n-icon>
            </template>
            批量导入
          </n-button>
          <n-tooltip trigger="hover" placement="bottom" class="proxy-status-tooltip-wrapper">
            <template #trigger>
              <n-button @click="showProxyConfigModal = true" class="proxy-config-button">
                <template #icon>
                  <n-icon><ServerOutline /></n-icon>
                </template>
                代理配置
                <!-- 代理状态指示器 - 内嵌在按钮中 -->
                <div
                  class="proxy-status-indicator"
                  :class="{
                    'status-success': proxyStatus.status === 'success',
                    'status-error': proxyStatus.status === 'error',
                    'status-loading': proxyStatus.status === 'loading',
                    'status-disabled': proxyStatus.status === 'disabled',
                  }"
                >
                  <n-icon size="12">
                    <WifiOutline v-if="proxyStatus.status === 'success'" />
                    <CloseCircleOutline v-else-if="proxyStatus.status === 'error'" />
                    <SyncOutline v-else-if="proxyStatus.status === 'loading'" />
                    <EllipseOutline v-else />
                  </n-icon>
                </div>
              </n-button>
            </template>
            <div class="proxy-status-tooltip">
              <div class="status-line">
                <strong>代理状态：</strong>
                <span>{{ proxyStatus.statusText }}</span>
              </div>
              <div class="status-line" v-if="proxyStatus.latency !== null">
                <strong>连接延迟：</strong>
                <span>{{
                  proxyStatus.latency === -1 ? '连接失败' : `${proxyStatus.latency}ms`
                }}</span>
              </div>
              <div class="status-line" v-if="proxyStatus.proxyInfo">
                <strong>代理信息：</strong>
                <span>{{ proxyStatus.proxyInfo }}</span>
              </div>
              <div class="status-line" v-if="proxyStatus.location">
                <strong>IP位置：</strong>
                <span>{{ proxyStatus.location }}</span>
              </div>
              <div class="status-line" v-if="proxyStatus.error">
                <strong>错误信息：</strong>
                <span class="error-text">{{ proxyStatus.error }}</span>
              </div>
            </div>
          </n-tooltip>
          <n-button @click="refreshData">
            <template #icon>
              <n-icon><RefreshOutline /></n-icon>
            </template>
            刷新
          </n-button>
        </n-space>
      </template>

      <!-- 筛选器 -->
      <div class="filter-section mb-4">
        <n-space>
          <n-select
            v-model:value="filters.status"
            :options="statusOptions"
            placeholder="登录状态"
            clearable
            multiple
            style="width: 150px"
          />
          <n-select
            v-model:value="filters.verification_status"
            :options="verificationStatusOptions"
            placeholder="验证状态"
            clearable
            multiple
            style="width: 150px"
          />
          <n-select
            v-model:value="filters.import_source"
            :options="importSourceOptions"
            placeholder="导入来源"
            clearable
            multiple
            style="width: 150px"
          />
          <n-button @click="applyFilters" type="primary">筛选</n-button>
          <n-button @click="resetFilters">重置</n-button>
        </n-space>
      </div>

      <!-- 统计信息 -->
      <div class="statistics-section mb-4">
        <n-grid :cols="6" :x-gap="12">
          <n-grid-item>
            <n-statistic label="总账户数" :value="statistics.total_accounts" />
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="活跃账户" :value="statistics.active_accounts" />
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="已验证" :value="statistics.verified_accounts" />
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="验证失败" :value="statistics.failed_accounts" />
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="已禁用" :value="statistics.disabled_accounts" />
          </n-grid-item>
          <n-grid-item>
            <n-statistic
              label="验证成功率"
              :value="`${statistics.verification_success_rate.toFixed(1)}%`"
            />
          </n-grid-item>
        </n-grid>
      </div>

      <!-- 批量操作区域 -->
      <div class="batch-operations mb-4">
        <n-space>
          <n-button
            type="primary"
            :disabled="selectedAccountIds.length === 0"
            @click="handleBatchVerification"
          >
            <template #icon>
              <n-icon><CheckmarkCircleOutline /></n-icon>
            </template>
            批量验证 ({{ selectedAccountIds.length }})
          </n-button>
          <n-button
            type="warning"
            :disabled="selectedAccountIds.length === 0"
            @click="handleBatchDisable"
          >
            <template #icon>
              <n-icon><BanOutline /></n-icon>
            </template>
            批量禁用
          </n-button>
          <n-button
            type="error"
            :disabled="selectedAccountIds.length === 0"
            @click="handleBatchDelete"
          >
            <template #icon>
              <n-icon><TrashOutline /></n-icon>
            </template>
            批量删除
          </n-button>
        </n-space>
      </div>

      <!-- 账户列表 -->
      <n-data-table
        :columns="columns"
        :data="accounts"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: ExtendedAccount) => row.id"
        :checked-row-keys="selectedAccountIds"
        @update:checked-row-keys="handleSelectionChange"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>

    <!-- 任务控制面板 -->
    <TaskControlPanel v-model:visible="showTaskPanel" @refresh="refreshTaskStatus" />

    <!-- 批量导入模态框 -->
    <BatchImportModal v-model:visible="showBatchImportModal" @success="handleImportSuccess" />

    <!-- {{ AURA-X: Add - 添加代理配置模态框. Principle: SOLID. Approval: 寸止(ID:proxy-config). }} -->
    <!-- 代理配置模态框 -->
    <ProxyConfigModal v-model:visible="showProxyConfigModal" @success="handleProxyConfigSuccess" />

    <!-- 验证任务模态框 -->
    <VerificationTaskModal
      v-model:visible="showVerificationModal"
      :selected-accounts="selectedAccounts"
      @success="handleVerificationSuccess"
    />

    <!-- 任务日志组件 -->
    <TaskLogPanel class="mt-4" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, h } from 'vue'
import {
  NCard,
  NButton,
  NSpace,
  NIcon,
  NSelect,
  NGrid,
  NGridItem,
  NStatistic,
  NDataTable,
  NTag,
  NTooltip,
  useMessage,
} from 'naive-ui'
import {
  CloudUploadOutline,
  RefreshOutline,
  CheckmarkCircleOutline,
  BanOutline,
  TrashOutline,
  ServerOutline,
  WifiOutline,
  CloseCircleOutline,
  SyncOutline,
  EllipseOutline,
} from '@vicons/ionicons5'
import { mailboxApi, type ExtendedAccount, type MailboxStatistics } from '@/api/mailbox'
import BatchImportModal from './components/BatchImportModal.vue'
import ProxyConfigModal from './components/ProxyConfigModal.vue'
import VerificationTaskModal from './components/VerificationTaskModal.vue'
import TaskControlPanel from './components/TaskControlPanel.vue'
import TaskLogPanel from './components/TaskLogPanel.vue'

const message = useMessage()

// 响应式数据
const loading = ref(false)
const accounts = ref<ExtendedAccount[]>([])
const selectedAccounts = ref<ExtendedAccount[]>([])
const selectedAccountIds = ref<number[]>([])
const showBatchImportModal = ref(false)
const showProxyConfigModal = ref(false)
const showVerificationModal = ref(false)
const showTaskPanel = ref(false)

// 代理状态数据
interface ProxyStatus {
  status: 'success' | 'error' | 'loading' | 'disabled'
  statusText: string
  latency: number | null
  proxyInfo: string | null
  location: string | null
  error: string | null
}

const proxyStatus = ref<ProxyStatus>({
  status: 'loading',
  statusText: '检测中...',
  latency: null,
  proxyInfo: null,
  location: null,
  error: null,
})

// 定时器引用
let proxyStatusTimer: NodeJS.Timeout | null = null

// 统计信息
const statistics = ref<MailboxStatistics>({
  id: 0,
  stat_date: '',
  total_accounts: 0,
  active_accounts: 0,
  verified_accounts: 0,
  failed_accounts: 0,
  disabled_accounts: 0,
  new_imports_today: 0,
  verification_success_rate: 0,
  avg_verification_time_ms: 0,
  created_at: '',
})

// 筛选器
const filters = reactive({
  status: [] as string[],
  verification_status: [] as string[],
  import_source: [] as string[],
  tags: [] as string[],
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
})

// 选项数据
const statusOptions = mailboxApi.getStatusOptions()
const verificationStatusOptions = mailboxApi.getVerificationStatusOptions()
const importSourceOptions = mailboxApi.getImportSourceOptions()

// 表格列定义
const columns = computed(() => [
  {
    type: 'selection' as const,
    multiple: true,
  },
  {
    title: '邮箱地址',
    key: 'email',
    width: 200,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '登录状态',
    key: 'login_status',
    width: 100,
    render: (row: ExtendedAccount) => {
      const status = mailboxApi.formatStatus(row.login_status)
      return h(NTag, { type: status.type }, { default: () => status.text })
    },
  },
  {
    title: '验证状态',
    key: 'verification_status',
    width: 100,
    render: (row: ExtendedAccount) => {
      const status = mailboxApi.formatStatus(row.verification_status)
      return h(NTag, { type: status.type }, { default: () => status.text })
    },
  },
  {
    title: '导入来源',
    key: 'import_source',
    width: 100,
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 150,
    render: (row: ExtendedAccount) => {
      return new Date(row.created_at).toLocaleString()
    },
  },
  {
    title: '最后验证',
    key: 'last_verification_time',
    width: 150,
    render: (row: ExtendedAccount) => {
      return row.last_verification_time
        ? new Date(row.last_verification_time).toLocaleString()
        : '-'
    },
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render: (row: ExtendedAccount) => {
      return h(
        NSpace,
        {},
        {
          default: () => [
            h(
              NButton,
              {
                size: 'small',
                type: row.is_disabled ? 'success' : 'warning',
                onClick: () => toggleAccountStatus(row),
              },
              {
                default: () => {
                  return row.is_disabled ? '启用' : '禁用'
                },
              }
            ),
            h(
              NButton,
              {
                size: 'small',
                type: 'error',
                onClick: () => deleteAccount(row),
              },
              { default: () => '删除' }
            ),
          ],
        }
      )
    },
  },
])

// 加载账户列表
const loadAccounts = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      status: filters.status.length > 0 ? filters.status : undefined,
      verification_status:
        filters.verification_status.length > 0 ? filters.verification_status : undefined,
      import_source: filters.import_source.length > 0 ? filters.import_source : undefined,
      tags: filters.tags.length > 0 ? filters.tags : undefined,
    }

    const response = await mailboxApi.getAccountsList(params)
    console.log('加载账户列表响应:', JSON.stringify(response))

    // 处理Axios响应包装 - 实际数据在response.data.data中
    const apiResponse = (response as any).data || response
    if (apiResponse.success && apiResponse.data) {
      // 后端返回的字段名是大写的 Items 和 Total
      accounts.value = apiResponse.data.Items || apiResponse.data.items || []
      pagination.itemCount = apiResponse.data.Total || apiResponse.data.total || 0
    } else {
      // 如果响应不成功，设置为空数组
      accounts.value = []
      pagination.itemCount = 0
    }
  } catch (error) {
    message.error('加载账户列表失败')
    console.error(error)
    // 确保在错误情况下也设置默认值
    accounts.value = []
    pagination.itemCount = 0
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const response = await mailboxApi.getMailboxStatistics()

    // 处理Axios响应包装
    const apiResponse = (response as any).data || response
    if (apiResponse.success && apiResponse.data) {
      statistics.value = apiResponse.data
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const refreshData = () => {
  loadAccounts()
  loadStatistics()
}

const applyFilters = () => {
  pagination.page = 1
  loadAccounts()
}

const resetFilters = () => {
  filters.status = []
  filters.verification_status = []
  filters.import_source = []
  filters.tags = []
  pagination.page = 1
  loadAccounts()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  loadAccounts()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadAccounts()
}

const handleImportSuccess = () => {
  message.success('批量导入任务已创建')
  refreshData()
}

// {{ AURA-X: Add - 添加代理配置成功处理方法. Principle: SOLID. Approval: 寸止(ID:proxy-config). }}
const handleProxyConfigSuccess = () => {
  message.success('代理配置已保存')
  // 配置更新后立即重新检测状态
  checkProxyStatus()
}

const handleVerificationSuccess = () => {
  message.success('验证任务已创建')
  refreshData()
}

// 选择变化处理
const handleSelectionChange = (keys: number[]) => {
  selectedAccountIds.value = keys
  // 确保 accounts.value 不为 null 或 undefined
  selectedAccounts.value = (accounts.value || []).filter(account => keys.includes(account.id))
}

// 批量验证
const handleBatchVerification = () => {
  if (selectedAccounts.value.length === 0) {
    message.warning('请先选择要验证的账户')
    return
  }
  showVerificationModal.value = true
}

// 批量禁用
const handleBatchDisable = async () => {
  if (selectedAccountIds.value.length === 0) {
    message.warning('请先选择要禁用的账户')
    return
  }

  try {
    await mailboxApi.batchDisableAccounts(selectedAccountIds.value)
    message.success(`成功禁用 ${selectedAccountIds.value.length} 个账户`)
    selectedAccountIds.value = []
    selectedAccounts.value = []
    refreshData()
  } catch (error) {
    message.error('批量禁用失败')
    console.error(error)
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedAccountIds.value.length === 0) {
    message.warning('请先选择要删除的账户')
    return
  }

  try {
    await mailboxApi.batchDeleteAccounts(selectedAccountIds.value)
    message.success(`成功删除 ${selectedAccountIds.value.length} 个账户`)
    selectedAccountIds.value = []
    selectedAccounts.value = []
    refreshData()
  } catch (error) {
    message.error('批量删除失败')
    console.error(error)
  }
}

// 单个账户删除
const deleteAccount = async (account: ExtendedAccount) => {
  try {
    await mailboxApi.deleteAccount(account.id)
    message.success(`成功删除账户 ${account.email}`)
    refreshData()
  } catch (error) {
    message.error(`删除账户 ${account.email} 失败`)
    console.error(error)
  }
}

const toggleAccountStatus = async (account: ExtendedAccount) => {
  try {
    const newStatus = !account.is_disabled
    await mailboxApi.toggleAccountStatus(account.id, newStatus)
    message.success(`成功${newStatus ? '禁用' : '启用'}账户 ${account.email}`)
    refreshData()
  } catch (error) {
    message.error(`${account.is_disabled ? '启用' : '禁用'}账户失败`)
    console.error(error)
  }
}

const refreshTaskStatus = () => {
  // 刷新任务状态
  loadStatistics()
}

// 代理状态检测相关方法
const checkProxyStatus = async () => {
  try {
    // 设置加载状态
    proxyStatus.value = {
      status: 'loading',
      statusText: '检测中...',
      latency: null,
      proxyInfo: null,
      location: null,
      error: null,
    }

    // 获取当前代理配置
    const configResponse = await mailboxApi.getProxyConfig()
    const configData = (configResponse as any).data?.data || (configResponse as any).data

    if (!configData) {
      proxyStatus.value = {
        status: 'disabled',
        statusText: '未启用',
        latency: null,
        proxyInfo: null,
        location: null,
        error: null,
      }
      return
    }

    // 测试代理连接
    const testResponse = await mailboxApi.testProxyConfig(configData)
    const testData = (testResponse as any).data?.data || (testResponse as any).data

    if (testData) {
      const isSuccess = testData.success
      const latency = testData.latency_ms || -1

      // 构建代理信息字符串（脱敏处理）
      let proxyInfo = null
      if (configData.enabled && configData.proxy_url) {
        try {
          const url = new URL(configData.proxy_url)
          const host = url.hostname
          const port = url.port
          const maskedHost =
            host.length > 6
              ? `${host.substring(0, 3)}***${host.substring(host.length - 3)}`
              : `${host.substring(0, 1)}***`
          proxyInfo = `${configData.proxy_type.toUpperCase()} ${maskedHost}:${port}`
        } catch {
          proxyInfo = `${configData.proxy_type.toUpperCase()} 代理`
        }
      }

      // 构建位置信息
      let location = null
      if (testData.country) {
        location = `${testData.country}`
        if (testData.region) location += ` ${testData.region}`
        if (testData.city) location += ` ${testData.city}`
      }

      proxyStatus.value = {
        status: isSuccess ? (latency < 5000 ? 'success' : 'error') : 'error',
        statusText: configData.enabled ? (isSuccess ? '已启用' : '连接异常') : '未启用',
        latency: isSuccess ? latency : -1,
        proxyInfo,
        location,
        error: isSuccess ? null : testData.error,
      }
    } else {
      proxyStatus.value = {
        status: 'error',
        statusText: '检测失败',
        latency: null,
        proxyInfo: null,
        location: null,
        error: '无法获取测试结果',
      }
    }
  } catch (error) {
    console.error('代理状态检测失败:', error)
    proxyStatus.value = {
      status: 'disabled',
      statusText: '检测失败',
      latency: null,
      proxyInfo: null,
      location: null,
      error: '网络请求失败',
    }
  }
}

// 启动定时检测
const startProxyStatusTimer = () => {
  // 清除现有定时器
  if (proxyStatusTimer) {
    clearInterval(proxyStatusTimer)
  }

  // 设置新的定时器，每360秒检测一次
  proxyStatusTimer = setInterval(() => {
    // 只有在代理启用时才定时检测
    if (proxyStatus.value.status !== 'disabled') {
      checkProxyStatus()
    }
  }, 360000) // 360秒 = 6分钟
}

// 停止定时检测
const stopProxyStatusTimer = () => {
  if (proxyStatusTimer) {
    clearInterval(proxyStatusTimer)
    proxyStatusTimer = null
  }
}

// 生命周期
onMounted(() => {
  refreshData()
  // 初始化代理状态检测
  checkProxyStatus()
  startProxyStatusTimer()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopProxyStatusTimer()
})
</script>

<style scoped>
.mailbox-management {
  padding: 16px;
}

.filter-section {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.statistics-section {
  padding: 16px;
  background: #f5f5f5;
  border-radius: 6px;
}

.batch-operations {
  padding: 12px 16px;
  background: #f0f8ff;
  border-radius: 6px;
  border: 1px solid #e1f3ff;
}

.mt-4 {
  margin-top: 16px;
}

/* 代理配置按钮样式 */
.proxy-config-button {
  position: relative;
  min-width: 100px; /* 增加最小宽度，确保icon和文字都能完整显示 */
  padding: 0 16px 0 12px; /* 调整内边距，右侧留出更多空间给状态指示器 */
}

/* 代理状态指示器样式 */
.proxy-status-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  z-index: 1;
}

/* 成功状态 - 绿色呼吸灯 */
.proxy-status-indicator.status-success {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid #10b981;
  animation: breathe-success 2s ease-in-out infinite;
}

.proxy-status-indicator.status-success .n-icon {
  color: #10b981;
}

@keyframes breathe-success {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.3);
    transform: scale(1.05);
  }
}

/* 错误状态 - 红色呼吸灯 */
.proxy-status-indicator.status-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid #ef4444;
  animation: breathe-error 2s ease-in-out infinite;
}

.proxy-status-indicator.status-error .n-icon {
  color: #ef4444;
}

@keyframes breathe-error {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.3);
    transform: scale(1.05);
  }
}

/* 加载状态 - 蓝色呼吸灯 */
.proxy-status-indicator.status-loading {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid #3b82f6;
  animation: breathe-loading 1.5s ease-in-out infinite;
}

.proxy-status-indicator.status-loading .n-icon {
  color: #3b82f6;
  animation: rotate 1s linear infinite;
}

@keyframes breathe-loading {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
    transform: scale(1.05);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 禁用状态 - 灰色静态 */
.proxy-status-indicator.status-disabled {
  background: rgba(156, 163, 175, 0.1);
  border: 1px solid #9ca3af;
}

.proxy-status-indicator.status-disabled .n-icon {
  color: #9ca3af;
}

/* Tooltip包装器样式 - 自定义tooltip背景 */
.proxy-status-tooltip-wrapper :deep(.n-tooltip__content) {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.1),
    0 4px 6px rgba(0, 0, 0, 0.05) !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
}

.proxy-status-tooltip-wrapper :deep(.n-tooltip__arrow) {
  border-top-color: #ffffff !important;
}

/* Tooltip内容样式 */
.proxy-status-tooltip {
  max-width: 300px;
  font-size: 13px;
  line-height: 1.5;
  font-weight: 500; /* 设置整体文字为中等粗细 */
}

.status-line {
  margin-bottom: 6px;
  display: flex;
  align-items: flex-start;
}

.status-line:last-child {
  margin-bottom: 0;
}

.status-line strong {
  min-width: 70px;
  margin-right: 8px;
  color: #1f2937;
  font-weight: 700; /* 标签文字加粗 */
  font-size: 13px;
}

.status-line span {
  color: #374151;
  word-break: break-all;
  font-weight: 600; /* 内容文字也设置为较粗 */
  font-size: 13px;
}

.error-text {
  color: #dc2626 !important;
  font-weight: 700 !important; /* 错误文字特别加粗 */
}
</style>
