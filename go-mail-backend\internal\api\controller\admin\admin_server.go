package admin

import (
	"go-mail/internal/auth"
	"go-mail/internal/database"
	"go-mail/internal/scheduler"
	"go-mail/internal/services"
	"log/slog"

	"github.com/gin-gonic/gin"
)

// AdminAPI 管理后台API控制器
type AdminAPI struct {
	services  *services.Services
	auth      *auth.Auth
	database  *database.Database
	scheduler *scheduler.Scheduler
	logger    *slog.Logger
}

// NewAdminAPI 创建管理后台API控制器
func NewAdminAPI(services *services.Services, auth *auth.Auth, database *database.Database, scheduler *scheduler.Scheduler, logger *slog.Logger) *AdminAPI {
	return &AdminAPI{
		services:  services,
		auth:      auth,
		database:  database,
		scheduler: scheduler,
		logger:    logger,
	}
}

// RegisterRoutes 注册管理后台路由
func (a *AdminAPI) RegisterRoutes(v1 *gin.RouterGroup) {
	// 创建各个功能模块的控制器
	authController := NewAuthController(a)
	userController := NewUserController(a)
	activationController := NewActivationController(a)
	mailboxController := NewMailboxController(a)
	monitorController := NewMonitorController(a)
	configController := NewConfigController(a)
	systemController := NewSystemController(a)

	// 注册各模块路由
	authController.RegisterRoutes(v1)
	userController.RegisterRoutes(v1)
	activationController.RegisterRoutes(v1)
	mailboxController.RegisterRoutes(v1)
	monitorController.RegisterRoutes(v1)
	configController.RegisterRoutes(v1)
	systemController.RegisterRoutes(v1)
}
