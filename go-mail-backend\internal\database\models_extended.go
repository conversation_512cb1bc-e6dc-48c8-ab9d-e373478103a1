package database

import (
	"time"
)

// ActivationCode 激活码数据库记录结构
type ActivationCode struct {
	ID                int        `db:"id" json:"id"`
	Code              string     `db:"code" json:"code"`                             // 激活码
	DeviceFingerprint *string    `db:"device_fingerprint" json:"device_fingerprint"` // 设备指纹（可为空）
	MacAddress        *string    `db:"mac_address" json:"mac_address"`               // MAC地址（可为空）
	Status            string     `db:"status" json:"status"`                         // unused/used/expired
	ExpiresAt         time.Time  `db:"expires_at" json:"expires_at"`                 // 过期时间
	UsedAt            *time.Time `db:"used_at" json:"used_at"`                       // 使用时间（可为空）
	CreatedAt         time.Time  `db:"created_at" json:"created_at"`                 // 创建时间
	Description       *string    `db:"description" json:"description"`               // 描述信息（可为空）
	BatchID           *string    `db:"batch_id" json:"batch_id"`                     // 批次ID（可为空）
}

// TempMailbox 临时邮箱分配记录结构
type TempMailbox struct {
	ID                int        `db:"id" json:"id"`
	MailboxAddress    string     `db:"mailbox_address" json:"mailbox_address"`       // 临时邮箱地址
	AccountEmail      string     `db:"account_email" json:"account_email"`           // 关联的Mail.com账户
	DeviceFingerprint string     `db:"device_fingerprint" json:"device_fingerprint"` // 使用设备指纹
	ActivationCode    string     `db:"activation_code" json:"activation_code"`       // 关联的激活码
	Status            string     `db:"status" json:"status"`                         // active/released/expired
	AllocatedAt       time.Time  `db:"allocated_at" json:"allocated_at"`             // 分配时间
	LastCheckAt       *time.Time `db:"last_check_at" json:"last_check_at"`           // 最后检查时间
	ExpiresAt         time.Time  `db:"expires_at" json:"expires_at"`                 // 过期时间
	AutoRelease       bool       `db:"auto_release" json:"auto_release"`             // 是否自动释放
	ReleaseReason     string     `db:"release_reason" json:"release_reason"`         // 释放原因
}

// MailRecord 邮件记录结构
type MailRecord struct {
	ID             int       `db:"id" json:"id"`
	MailboxID      int       `db:"mailbox_id" json:"mailbox_id"`           // 关联的临时邮箱ID
	MailID         string    `db:"mail_id" json:"mail_id"`                 // 邮件ID
	Subject        string    `db:"subject" json:"subject"`                 // 邮件主题
	Sender         string    `db:"sender" json:"sender"`                   // 发件人
	ReceivedAt     time.Time `db:"received_at" json:"received_at"`         // 接收时间
	ContentFetched bool      `db:"content_fetched" json:"content_fetched"` // 是否已获取内容
	ContentText    string    `db:"content_text" json:"content_text"`       // 纯文本内容
	ContentHTML    string    `db:"content_html" json:"content_html"`       // HTML内容
	CreatedAt      time.Time `db:"created_at" json:"created_at"`           // 创建时间
}

// AdminUser 管理员用户结构
type AdminUser struct {
	ID        int        `db:"id" json:"id"`
	Username  string     `db:"username" json:"username"`     // 用户名
	Password  string     `db:"password" json:"-"`            // 密码哈希
	Role      string     `db:"role" json:"role"`             // 角色：admin/operator
	Status    string     `db:"status" json:"status"`         // 状态：active/disabled
	LastLogin *time.Time `db:"last_login" json:"last_login"` // 最后登录时间
	CreatedAt time.Time  `db:"created_at" json:"created_at"` // 创建时间
	UpdatedAt time.Time  `db:"updated_at" json:"updated_at"` // 更新时间
}

// SystemConfig 系统配置结构
type SystemConfig struct {
	ID        int       `db:"id" json:"id"`
	Key       string    `db:"key" json:"key"`               // 配置键
	Value     string    `db:"value" json:"value"`           // 配置值
	Type      string    `db:"type" json:"type"`             // 配置类型：string/int/bool/json
	Category  string    `db:"category" json:"category"`     // 配置分类
	UpdatedAt time.Time `db:"updated_at" json:"updated_at"` // 更新时间
}

// 状态常量定义
const (
	// 激活码状态
	ActivationCodeStatusUnused  = "unused"
	ActivationCodeStatusUsed    = "used"
	ActivationCodeStatusExpired = "expired"

	// 临时邮箱状态
	TempMailboxStatusActive   = "active"
	TempMailboxStatusReleased = "released"
	TempMailboxStatusExpired  = "expired"

	// 管理员状态
	AdminUserStatusActive   = "active"
	AdminUserStatusDisabled = "disabled"

	// 管理员角色
	AdminRoleAdmin    = "admin"
	AdminRoleOperator = "operator"

	// 释放原因
	ReleaseReasonManual      = "manual_release"
	ReleaseReasonAutoTimeout = "auto_timeout"
	ReleaseReasonMailFetched = "mail_fetched"
	ReleaseReasonExpired     = "expired"

	// 配置类型
	ConfigTypeString = "string"
	ConfigTypeInt    = "int"
	ConfigTypeBool   = "bool"
	ConfigTypeJSON   = "json"

	// 配置分类
	ConfigCategorySystem   = "system"
	ConfigCategoryMailbox  = "mailbox"
	ConfigCategorySecurity = "security"
)

// DeviceInfo 设备信息结构（用于JSON序列化）
type DeviceInfo struct {
	SystemUUID   string `json:"system_uuid"`
	Username     string `json:"username"`
	ComputerName string `json:"computer_name"`
	Platform     string `json:"platform"`
	MacAddress   string `json:"mac_address"`
}

// {{ AURA-X: Add - 添加代理配置相关数据模型. Principle: SOLID. Approval: 寸止(ID:proxy-config). }}
// ProxyConfigData 代理配置数据结构（用于API响应）
type ProxyConfigData struct {
	Enabled   bool   `json:"enabled"`
	ProxyURL  string `json:"proxy_url"`
	ProxyType string `json:"proxy_type"`
}

// ProxyTestResult 代理测试结果结构
type ProxyTestResult struct {
	Success   bool   `json:"success"`
	IP        string `json:"ip,omitempty"`
	Country   string `json:"country,omitempty"`
	City      string `json:"city,omitempty"`
	Region    string `json:"region,omitempty"`
	ISP       string `json:"isp,omitempty"`
	Error     string `json:"error,omitempty"`
	LatencyMs int64  `json:"latency_ms,omitempty"`
}

// MailboxAllocationRequest 邮箱分配请求结构
type MailboxAllocationRequest struct {
	Preferences struct {
		DomainSuffix       string `json:"domain_suffix"`
		AutoReleaseMinutes int    `json:"auto_release_minutes"`
	} `json:"preferences"`
}

// MailQueryRequest 邮件查询请求结构
type MailQueryRequest struct {
	MailboxID int    `json:"mailbox_id"`
	Since     string `json:"since"`
}

// MailDetailRequest 邮件详情请求结构
type MailDetailRequest struct {
	MailboxID int    `json:"mailbox_id"`
	MailID    string `json:"mail_id"`
}

// MailboxReleaseRequest 邮箱释放请求结构
type MailboxReleaseRequest struct {
	MailboxID int    `json:"mailbox_id"`
	Reason    string `json:"reason"`
}

// MailboxStatusRequest 邮箱状态请求结构
type MailboxStatusRequest struct {
	MailboxID int `json:"mailbox_id"`
}

// ActivationCodeGenerateRequest 激活码生成请求结构
type ActivationCodeGenerateRequest struct {
	Count       int    `json:"count"`
	ExpiryDays  int    `json:"expiry_days"`
	Prefix      string `json:"prefix"`
	Description string `json:"description"`
}

// LoginRequest 登录请求结构
type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// APIResponse 通用API响应结构
type APIResponse struct {
	Code      int         `json:"code"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	Error     string      `json:"error,omitempty"`
	Timestamp string      `json:"timestamp"`
	RequestID string      `json:"request_id"`
}

// MailboxAllocationResponse 邮箱分配响应结构
type MailboxAllocationResponse struct {
	MailboxID   int       `json:"mailbox_id"`
	Address     string    `json:"address"`
	Status      string    `json:"status"`
	AllocatedAt time.Time `json:"allocated_at"`
	ExpiresAt   time.Time `json:"expires_at"`
	AutoRelease bool      `json:"auto_release"`
}

// MailQueryResponse 邮件查询响应结构
type MailQueryResponse struct {
	MailboxID int    `json:"mailbox_id"`
	Address   string `json:"address"`
	Mails     []struct {
		ID         string    `json:"id"`
		Subject    string    `json:"subject"`
		Sender     string    `json:"sender"`
		ReceivedAt time.Time `json:"received_at"`
		Preview    string    `json:"preview"`
		HasContent bool      `json:"has_content"`
	} `json:"mails"`
	TotalCount int       `json:"total_count"`
	LastCheck  time.Time `json:"last_check"`
}

// MailDetailResponse 邮件详情响应结构
type MailDetailResponse struct {
	ID         string    `json:"id"`
	Subject    string    `json:"subject"`
	Sender     string    `json:"sender"`
	ReceivedAt time.Time `json:"received_at"`
	Content    struct {
		Text string `json:"text"`
		HTML string `json:"html"`
	} `json:"content"`
	Attachments []interface{} `json:"attachments"`
}

// MailboxStatusResponse 邮箱状态响应结构
type MailboxStatusResponse struct {
	MailboxID   int       `json:"mailbox_id"`
	Address     string    `json:"address"`
	Status      string    `json:"status"`
	AllocatedAt time.Time `json:"allocated_at"`
	ExpiresAt   time.Time `json:"expires_at"`
	LastCheck   time.Time `json:"last_check"`
	MailCount   int       `json:"mail_count"`
}

// TokenResponse JWT令牌响应结构
type TokenResponse struct {
	Token        string    `json:"token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresIn    int       `json:"expires_in"`
	User         AdminUser `json:"user"`
}

// ActivationCodeBatchResponse 激活码批量生成响应结构
type ActivationCodeBatchResponse struct {
	BatchID   string    `json:"batch_id"`
	Codes     []string  `json:"codes"`
	Count     int       `json:"count"`
	ExpiresAt time.Time `json:"expires_at"`
}
