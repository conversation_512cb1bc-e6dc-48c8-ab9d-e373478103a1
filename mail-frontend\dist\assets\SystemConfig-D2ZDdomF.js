import{s as b}from"./system-D5OTSIk8.js";import{u as R,v as _,H as E,i as $,j as f,K as x,O as S,J as y,B as d,P as j,l as F,Q as q}from"./ui-B428bLoF.js";import{k as T,r as m,e as V,o as D,W as K,X as o,S as l,R as t,K as a,Q,j as r}from"./vendor-RHijBMdK.js";import{_ as G}from"./index-BVF_NGWt.js";const H={class:"system-config"},J={class:"operations-grid"},W={class:"operation-item"},X={class:"operation-item"},z={class:"operation-item"},Y=T({__name:"SystemConfig",setup(Z){const u=R(),C=m(!1),M=m(!1),w=m(!1),v=m(!1),g=m(!1),p=m([]),k=m(null),i=V({mailboxExpirationMinutes:30,maxMailboxesPerCode:5,cleanupIntervalMinutes:60,enableAutoCleanup:!0,maxMailsPerMailbox:100,enableEmailNotifications:!1}),P={mailboxExpirationMinutes:[{required:!0,type:"number",message:"请输入邮箱过期时间",trigger:["input","blur"]},{type:"number",min:1,max:1440,message:"过期时间应在1-1440分钟之间",trigger:["input","blur"]}],maxMailboxesPerCode:[{required:!0,type:"number",message:"请输入最大邮箱数",trigger:["input","blur"]},{type:"number",min:1,max:100,message:"最大邮箱数应在1-100之间",trigger:["input","blur"]}],cleanupIntervalMinutes:[{required:!0,type:"number",message:"请输入清理间隔",trigger:["input","blur"]},{type:"number",min:1,max:1440,message:"清理间隔应在1-1440分钟之间",trigger:["input","blur"]}],maxMailsPerMailbox:[{required:!0,type:"number",message:"请输入最大邮件数",trigger:["input","blur"]},{type:"number",min:1,max:1e3,message:"最大邮件数应在1-1000之间",trigger:["input","blur"]}]},N=async()=>{try{const s=await b.getSystemConfig();s.success&&s.data&&Object.assign(i,s.data)}catch(s){console.error("Failed to fetch system config:",s),u.error("获取系统配置失败")}},U=async()=>{if(k.value)try{await k.value.validate(),C.value=!0;for(const[s,e]of Object.entries(i))await b.updateSystemConfig({key:s,value:e,description:`更新${s}配置`});u.success("配置保存成功")}catch(s){console.error("Failed to save config:",s),u.error("配置保存失败")}finally{C.value=!1}},I=()=>{N(),u.info("配置已重置")},O=async()=>{try{await b.resetSystemConfig(),await N(),u.success("已恢复默认配置")}catch(s){console.error("Failed to reset config:",s),u.error("恢复默认配置失败")}},A=async()=>{if(p.value.length===0){u.warning("请选择要清理的数据类型");return}try{w.value=!0;const s={cleanExpiredMailboxes:p.value.includes("cleanExpiredMailboxes"),cleanOldLogs:p.value.includes("cleanOldLogs"),cleanTempFiles:p.value.includes("cleanTempFiles")};await b.cleanupSystem(s),u.success("数据清理完成"),v.value=!1,p.value=[]}catch(s){console.error("Failed to cleanup system:",s),u.error("数据清理失败")}finally{w.value=!1}},L=async()=>{try{M.value=!0;const s=await b.backupSystem();s.success&&s.data&&(u.success("备份创建成功"),s.data.downloadUrl&&window.open(s.data.downloadUrl,"_blank"))}catch(s){console.error("Failed to backup system:",s),u.error("备份创建失败")}finally{M.value=!1}},B=()=>{u.warning("系统重启功能暂未实现"),g.value=!1};return D(()=>{N()}),(s,e)=>(Q(),K("div",H,[e[40]||(e[40]=o("div",{class:"page-header"},[o("h1",null,"系统配置"),o("p",null,"管理系统的各项配置参数")],-1)),l(a(_),{title:"基础配置"},{default:t(()=>[l(a($),{ref_key:"configFormRef",ref:k,model:i,rules:P,"label-placement":"left","label-width":"200px"},{default:t(()=>[l(a(f),{label:"邮箱过期时间(分钟)",path:"mailboxExpirationMinutes"},{default:t(()=>[l(a(x),{value:i.mailboxExpirationMinutes,"onUpdate:value":e[0]||(e[0]=n=>i.mailboxExpirationMinutes=n),min:1,max:1440,placeholder:"请输入邮箱过期时间",style:{width:"200px"}},null,8,["value"]),e[13]||(e[13]=o("span",{class:"form-help"},"邮箱分配后的有效时间，超时后自动释放",-1))]),_:1,__:[13]}),l(a(f),{label:"单码最大邮箱数",path:"maxMailboxesPerCode"},{default:t(()=>[l(a(x),{value:i.maxMailboxesPerCode,"onUpdate:value":e[1]||(e[1]=n=>i.maxMailboxesPerCode=n),min:1,max:100,placeholder:"请输入最大邮箱数",style:{width:"200px"}},null,8,["value"]),e[14]||(e[14]=o("span",{class:"form-help"},"每个激活码最多可以分配的邮箱数量",-1))]),_:1,__:[14]}),l(a(f),{label:"清理间隔(分钟)",path:"cleanupIntervalMinutes"},{default:t(()=>[l(a(x),{value:i.cleanupIntervalMinutes,"onUpdate:value":e[2]||(e[2]=n=>i.cleanupIntervalMinutes=n),min:1,max:1440,placeholder:"请输入清理间隔",style:{width:"200px"}},null,8,["value"]),e[15]||(e[15]=o("span",{class:"form-help"},"系统自动清理过期数据的时间间隔",-1))]),_:1,__:[15]}),l(a(f),{label:"启用自动清理",path:"enableAutoCleanup"},{default:t(()=>[l(a(S),{value:i.enableAutoCleanup,"onUpdate:value":e[3]||(e[3]=n=>i.enableAutoCleanup=n)},null,8,["value"]),e[16]||(e[16]=o("span",{class:"form-help"},"是否启用定时清理过期邮箱和数据",-1))]),_:1,__:[16]}),l(a(f),{label:"单箱最大邮件数",path:"maxMailsPerMailbox"},{default:t(()=>[l(a(x),{value:i.maxMailsPerMailbox,"onUpdate:value":e[4]||(e[4]=n=>i.maxMailsPerMailbox=n),min:1,max:1e3,placeholder:"请输入最大邮件数",style:{width:"200px"}},null,8,["value"]),e[17]||(e[17]=o("span",{class:"form-help"},"每个邮箱最多保存的邮件数量",-1))]),_:1,__:[17]}),l(a(f),{label:"启用邮件通知",path:"enableEmailNotifications"},{default:t(()=>[l(a(S),{value:i.enableEmailNotifications,"onUpdate:value":e[5]||(e[5]=n=>i.enableEmailNotifications=n)},null,8,["value"]),e[18]||(e[18]=o("span",{class:"form-help"},"是否启用系统邮件通知功能",-1))]),_:1,__:[18]}),l(a(f),null,{default:t(()=>[l(a(y),null,{default:t(()=>[l(a(d),{type:"primary",loading:C.value,onClick:U},{default:t(()=>e[19]||(e[19]=[r(" 保存配置 ")])),_:1,__:[19]},8,["loading"]),l(a(d),{onClick:I},{default:t(()=>e[20]||(e[20]=[r("重置")])),_:1,__:[20]}),l(a(d),{onClick:O},{default:t(()=>e[21]||(e[21]=[r("恢复默认")])),_:1,__:[21]})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),l(a(_),{title:"系统操作",class:"operations-card"},{default:t(()=>[o("div",J,[o("div",W,[e[23]||(e[23]=o("h3",null,"数据清理",-1)),e[24]||(e[24]=o("p",null,"清理过期的邮箱和邮件数据",-1)),l(a(d),{type:"warning",onClick:e[6]||(e[6]=n=>v.value=!0)},{default:t(()=>e[22]||(e[22]=[r(" 执行清理 ")])),_:1,__:[22]})]),o("div",X,[e[26]||(e[26]=o("h3",null,"数据备份",-1)),e[27]||(e[27]=o("p",null,"备份系统数据到本地文件",-1)),l(a(d),{type:"info",loading:M.value,onClick:L},{default:t(()=>e[25]||(e[25]=[r(" 创建备份 ")])),_:1,__:[25]},8,["loading"])]),o("div",z,[e[29]||(e[29]=o("h3",null,"系统重启",-1)),e[30]||(e[30]=o("p",null,"重启系统服务（谨慎操作）",-1)),l(a(d),{type:"error",onClick:e[7]||(e[7]=n=>g.value=!0)},{default:t(()=>e[28]||(e[28]=[r(" 重启系统 ")])),_:1,__:[28]})])])]),_:1}),l(a(E),{show:v.value,"onUpdate:show":e[10]||(e[10]=n=>v.value=n),preset:"dialog",title:"数据清理"},{default:t(()=>[e[34]||(e[34]=o("p",null,"请选择要清理的数据类型：",-1)),l(a(j),{value:p.value,"onUpdate:value":e[8]||(e[8]=n=>p.value=n)},{default:t(()=>[l(a(y),{vertical:""},{default:t(()=>[l(a(F),{value:"cleanExpiredMailboxes"},{default:t(()=>e[31]||(e[31]=[r("清理过期邮箱")])),_:1,__:[31]}),l(a(F),{value:"cleanOldLogs"},{default:t(()=>e[32]||(e[32]=[r("清理旧日志")])),_:1,__:[32]}),l(a(F),{value:"cleanTempFiles"},{default:t(()=>e[33]||(e[33]=[r("清理临时文件")])),_:1,__:[33]})]),_:1})]),_:1},8,["value"])]),action:t(()=>[l(a(y),null,{default:t(()=>[l(a(d),{onClick:e[9]||(e[9]=n=>v.value=!1)},{default:t(()=>e[35]||(e[35]=[r("取消")])),_:1,__:[35]}),l(a(d),{type:"warning",loading:w.value,onClick:A},{default:t(()=>e[36]||(e[36]=[r(" 确认清理 ")])),_:1,__:[36]},8,["loading"])]),_:1})]),_:1},8,["show"]),l(a(E),{show:g.value,"onUpdate:show":e[12]||(e[12]=n=>g.value=n),preset:"dialog",title:"系统重启"},{default:t(()=>[l(a(q),{type:"warning",title:"警告"},{default:t(()=>e[37]||(e[37]=[r(" 系统重启将中断所有正在进行的操作，请确认是否继续？ ")])),_:1,__:[37]})]),action:t(()=>[l(a(y),null,{default:t(()=>[l(a(d),{onClick:e[11]||(e[11]=n=>g.value=!1)},{default:t(()=>e[38]||(e[38]=[r("取消")])),_:1,__:[38]}),l(a(d),{type:"error",onClick:B},{default:t(()=>e[39]||(e[39]=[r(" 确认重启 ")])),_:1,__:[39]})]),_:1})]),_:1},8,["show"])]))}}),ae=G(Y,[["__scopeId","data-v-0dafb1ed"]]);export{ae as default};
