import{k as h,W as b,Q as x,X as r,r as g,e as $,o as B,Y as L,Z as D,S as o,R as a,K as s,_ as R,$ as w,a0 as M,j as z}from"./vendor-RHijBMdK.js";import{u as S,g as H,_ as q}from"./index-BVF_NGWt.js";import{u as K,h as p,i as P,j as f,k as I,l as V,B as F}from"./ui-B428bLoF.js";import{M as U}from"./Mail-DFX8DOvB.js";import{P as j}from"./Person-Cj8AC7xs.js";const A={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},E=r("path",{d:"M16 352a48.05 48.05 0 0 0 48 48h133.88l-4 32H144a16 16 0 0 0 0 32h224a16 16 0 0 0 0-32h-49.88l-4-32H448a48.05 48.05 0 0 0 48-48v-48H16zm240-16a16 16 0 1 1-16 16a16 16 0 0 1 16-16z",fill:"currentColor"},null,-1),G=r("path",{d:"M496 96a48.05 48.05 0 0 0-48-48H64a48.05 48.05 0 0 0-48 48v192h480z",fill:"currentColor"},null,-1),Q=[E,G],T=h({name:"Desktop",render:function(d,u){return x(),b("svg",A,Q)}}),W={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},X=r("path",{d:"M368 192h-16v-80a96 96 0 1 0-192 0v80h-16a64.07 64.07 0 0 0-64 64v176a64.07 64.07 0 0 0 64 64h224a64.07 64.07 0 0 0 64-64V256a64.07 64.07 0 0 0-64-64zm-48 0H192v-80a64 64 0 1 1 128 0z",fill:"currentColor"},null,-1),Y=[X],Z=h({name:"LockClosed",render:function(d,u){return x(),b("svg",W,Y)}}),J={class:"login-container"},O={class:"login-card"},ee={class:"login-header"},se={class:"logo"},oe={class:"login-footer"},re={class:"device-info"},ae={class:"version-info"},te=h({__name:"Login",setup(k){const d=D(),u=L(),c=K(),v=S(),_=g(null),t=$({username:"",password:"",rememberMe:!1}),C={username:[{required:!0,message:"请输入用户名",trigger:["input","blur"]},{min:3,max:20,message:"用户名长度应为3-20个字符",trigger:["input","blur"]}],password:[{required:!0,message:"请输入密码",trigger:["input","blur"]},{min:6,message:"密码长度不能少于6个字符",trigger:["input","blur"]}]},l=g(!1),y=g(H()),N="1.0.0",m=async()=>{if(_.value)try{await _.value.validate(),l.value=!0;const n=await v.login({username:t.username,password:t.password});if(n.success){c.success("登录成功");const e=u.query.redirect||"/";await d.push(e)}else c.error(n.message)}catch(n){console.error("Login error:",n),n.message?c.error(n.message):c.error("登录失败，请检查用户名和密码")}finally{l.value=!1}};return B(async()=>{if(v.isAuthenticated){const n=u.query.redirect||"/";await d.push(n);return}await v.initializeDeviceFingerprint()}),(n,e)=>(x(),b("div",J,[r("div",O,[r("div",ee,[r("div",se,[o(s(p),{size:"48",color:"#18a058"},{default:a(()=>[o(s(U))]),_:1}),e[3]||(e[3]=r("h1",null,"Go-Mail",-1))]),e[4]||(e[4]=r("p",{class:"subtitle"},"临时邮箱服务管理后台",-1))]),o(s(P),{ref_key:"formRef",ref:_,model:t,rules:C,size:"large",onSubmit:R(m,["prevent"])},{default:a(()=>[o(s(f),{path:"username",label:"用户名"},{default:a(()=>[o(s(I),{value:t.username,"onUpdate:value":e[0]||(e[0]=i=>t.username=i),placeholder:"请输入用户名",disabled:l.value,onKeydown:M(m,["enter"])},{prefix:a(()=>[o(s(p),null,{default:a(()=>[o(s(j))]),_:1})]),_:1},8,["value","disabled"])]),_:1}),o(s(f),{path:"password",label:"密码"},{default:a(()=>[o(s(I),{value:t.password,"onUpdate:value":e[1]||(e[1]=i=>t.password=i),type:"password",placeholder:"请输入密码","show-password-on":"mousedown",disabled:l.value,onKeydown:M(m,["enter"])},{prefix:a(()=>[o(s(p),null,{default:a(()=>[o(s(Z))]),_:1})]),_:1},8,["value","disabled"])]),_:1}),o(s(f),null,{default:a(()=>[o(s(V),{checked:t.rememberMe,"onUpdate:checked":e[2]||(e[2]=i=>t.rememberMe=i),disabled:l.value},{default:a(()=>e[5]||(e[5]=[z(" 记住我 ")])),_:1,__:[5]},8,["checked","disabled"])]),_:1}),o(s(f),null,{default:a(()=>[o(s(F),{type:"primary",size:"large",loading:l.value,disabled:!t.username||!t.password,block:"",onClick:m},{default:a(()=>e[6]||(e[6]=[z(" 登录 ")])),_:1,__:[6]},8,["loading","disabled"])]),_:1})]),_:1},8,["model"]),r("div",oe,[r("div",re,[o(s(p),{size:"14"},{default:a(()=>[o(s(T))]),_:1}),r("span",null,w(y.value.browser)+" · "+w(y.value.platform),1)]),r("div",ae," v"+w(s(N)),1)])]),e[7]||(e[7]=r("div",{class:"background-decoration"},[r("div",{class:"decoration-circle circle-1"}),r("div",{class:"decoration-circle circle-2"}),r("div",{class:"decoration-circle circle-3"})],-1))]))}}),ce=q(te,[["__scopeId","data-v-c19fc767"]]);export{ce as default};
