import{k as Jy,m as jy,x as hs,i as Wa,I as tm,c as _i,g as em,w as Ua,y as gd,o as rm,b as im,n as nm,J as am,K as om}from"./vendor-RHijBMdK.js";/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var Pl=function(r,t){return Pl=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])},Pl(r,t)};function N(r,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Pl(r,t);function e(){this.constructor=r}r.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}var sm=function(){function r(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}return r}(),lm=function(){function r(){this.browser=new sm,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow=typeof window<"u"}return r}(),$=new lm;typeof wx=="object"&&typeof wx.getSystemInfoSync=="function"?($.wxa=!0,$.touchEventsSupported=!0):typeof document>"u"&&typeof self<"u"?$.worker=!0:!$.hasGlobalWindow||"Deno"in window?($.node=!0,$.svgSupported=!0):um(navigator.userAgent,$);function um(r,t){var e=t.browser,i=r.match(/Firefox\/([\d.]+)/),n=r.match(/MSIE\s([\d.]+)/)||r.match(/Trident\/.+?rv:(([\d.]+))/),a=r.match(/Edge?\/([\d.]+)/),o=/micromessenger/i.test(r);i&&(e.firefox=!0,e.version=i[1]),n&&(e.ie=!0,e.version=n[1]),a&&(e.edge=!0,e.version=a[1],e.newEdge=+a[1].split(".")[0]>18),o&&(e.weChat=!0),t.svgSupported=typeof SVGRect<"u",t.touchEventsSupported="ontouchstart"in window&&!e.ie&&!e.edge,t.pointerEventsSupported="onpointerdown"in window&&(e.edge||e.ie&&+e.version>=11),t.domSupported=typeof document<"u";var s=document.documentElement.style;t.transform3dSupported=(e.ie&&"transition"in s||e.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),t.transformSupported=t.transform3dSupported||e.ie&&+e.version>=9}var Nu=12,fm="sans-serif",Kr=Nu+"px "+fm,hm=20,vm=100,cm="007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N";function dm(r){var t={};if(typeof JSON>"u")return t;for(var e=0;e<r.length;e++){var i=String.fromCharCode(e+32),n=(r.charCodeAt(e)-hm)/vm;t[i]=n}return t}var pm=dm(cm),Fi={createCanvas:function(){return typeof document<"u"&&document.createElement("canvas")},measureText:function(){var r,t;return function(e,i){if(!r){var n=Fi.createCanvas();r=n&&n.getContext("2d")}if(r)return t!==i&&(t=r.font=i||Kr),r.measureText(e);e=e||"",i=i||Kr;var a=/((?:\d+)?\.?\d*)px/.exec(i),o=a&&+a[1]||Nu,s=0;if(i.indexOf("mono")>=0)s=o*e.length;else for(var l=0;l<e.length;l++){var u=pm[e[l]];s+=u==null?o:u*o}return{width:s}}}(),loadImage:function(r,t,e){var i=new Image;return i.onload=t,i.onerror=e,i.src=r,i}},yd=gr(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(r,t){return r["[object "+t+"]"]=!0,r},{}),md=gr(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(r,t){return r["[object "+t+"Array]"]=!0,r},{}),Jn=Object.prototype.toString,Eo=Array.prototype,gm=Eo.forEach,ym=Eo.filter,Fu=Eo.slice,mm=Eo.map,Vf=(function(){}).constructor,na=Vf?Vf.prototype:null,zu="__proto__",_m=2311;function _d(){return _m++}function Hu(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];typeof console<"u"&&console.error.apply(console,r)}function j(r){if(r==null||typeof r!="object")return r;var t=r,e=Jn.call(r);if(e==="[object Array]"){if(!xn(r)){t=[];for(var i=0,n=r.length;i<n;i++)t[i]=j(r[i])}}else if(md[e]){if(!xn(r)){var a=r.constructor;if(a.from)t=a.from(r);else{t=new a(r.length);for(var i=0,n=r.length;i<n;i++)t[i]=r[i]}}}else if(!yd[e]&&!xn(r)&&!Nn(r)){t={};for(var o in r)r.hasOwnProperty(o)&&o!==zu&&(t[o]=j(r[o]))}return t}function it(r,t,e){if(!W(t)||!W(r))return e?j(t):r;for(var i in t)if(t.hasOwnProperty(i)&&i!==zu){var n=r[i],a=t[i];W(a)&&W(n)&&!F(a)&&!F(n)&&!Nn(a)&&!Nn(n)&&!Wf(a)&&!Wf(n)&&!xn(a)&&!xn(n)?it(n,a,e):(e||!(i in r))&&(r[i]=j(t[i]))}return r}function O(r,t){if(Object.assign)Object.assign(r,t);else for(var e in t)t.hasOwnProperty(e)&&e!==zu&&(r[e]=t[e]);return r}function at(r,t,e){for(var i=dt(t),n=0,a=i.length;n<a;n++){var o=i[n];r[o]==null&&(r[o]=t[o])}return r}function lt(r,t){if(r){if(r.indexOf)return r.indexOf(t);for(var e=0,i=r.length;e<i;e++)if(r[e]===t)return e}return-1}function Sm(r,t){var e=r.prototype;function i(){}i.prototype=t.prototype,r.prototype=new i;for(var n in e)e.hasOwnProperty(n)&&(r.prototype[n]=e[n]);r.prototype.constructor=r,r.superClass=t}function Ee(r,t,e){if(r="prototype"in r?r.prototype:r,t="prototype"in t?t.prototype:t,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(t),n=0;n<i.length;n++){var a=i[n];a!=="constructor"&&r[a]==null&&(r[a]=t[a])}else at(r,t)}function Xt(r){return!r||typeof r=="string"?!1:typeof r.length=="number"}function M(r,t,e){if(r&&t)if(r.forEach&&r.forEach===gm)r.forEach(t,e);else if(r.length===+r.length)for(var i=0,n=r.length;i<n;i++)t.call(e,r[i],i,r);else for(var a in r)r.hasOwnProperty(a)&&t.call(e,r[a],a,r)}function V(r,t,e){if(!r)return[];if(!t)return Gu(r);if(r.map&&r.map===mm)return r.map(t,e);for(var i=[],n=0,a=r.length;n<a;n++)i.push(t.call(e,r[n],n,r));return i}function gr(r,t,e,i){if(r&&t){for(var n=0,a=r.length;n<a;n++)e=t.call(i,e,r[n],n,r);return e}}function Mt(r,t,e){if(!r)return[];if(!t)return Gu(r);if(r.filter&&r.filter===ym)return r.filter(t,e);for(var i=[],n=0,a=r.length;n<a;n++)t.call(e,r[n],n,r)&&i.push(r[n]);return i}function dt(r){if(!r)return[];if(Object.keys)return Object.keys(r);var t=[];for(var e in r)r.hasOwnProperty(e)&&t.push(e);return t}function wm(r,t){for(var e=[],i=2;i<arguments.length;i++)e[i-2]=arguments[i];return function(){return r.apply(t,e.concat(Fu.call(arguments)))}}var ht=na&&q(na.bind)?na.call.bind(na.bind):wm;function St(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return function(){return r.apply(this,t.concat(Fu.call(arguments)))}}function F(r){return Array.isArray?Array.isArray(r):Jn.call(r)==="[object Array]"}function q(r){return typeof r=="function"}function G(r){return typeof r=="string"}function Rl(r){return Jn.call(r)==="[object String]"}function vt(r){return typeof r=="number"}function W(r){var t=typeof r;return t==="function"||!!r&&t==="object"}function Wf(r){return!!yd[Jn.call(r)]}function $t(r){return!!md[Jn.call(r)]}function Nn(r){return typeof r=="object"&&typeof r.nodeType=="number"&&typeof r.ownerDocument=="object"}function Oo(r){return r.colorStops!=null}function bm(r){return r.image!=null}function ao(r){return r!==r}function Fn(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];for(var e=0,i=r.length;e<i;e++)if(r[e]!=null)return r[e]}function K(r,t){return r??t}function bn(r,t,e){return r??t??e}function Gu(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return Fu.apply(r,t)}function Sd(r){if(typeof r=="number")return[r,r,r,r];var t=r.length;return t===2?[r[0],r[1],r[0],r[1]]:t===3?[r[0],r[1],r[2],r[1]]:r}function Ye(r,t){if(!r)throw new Error(t)}function Le(r){return r==null?null:typeof r.trim=="function"?r.trim():r.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var wd="__ec_primitive__";function El(r){r[wd]=!0}function xn(r){return r[wd]}var xm=function(){function r(){this.data={}}return r.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},r.prototype.has=function(t){return this.data.hasOwnProperty(t)},r.prototype.get=function(t){return this.data[t]},r.prototype.set=function(t,e){return this.data[t]=e,this},r.prototype.keys=function(){return dt(this.data)},r.prototype.forEach=function(t){var e=this.data;for(var i in e)e.hasOwnProperty(i)&&t(e[i],i)},r}(),bd=typeof Map=="function";function Tm(){return bd?new Map:new xm}var Cm=function(){function r(t){var e=F(t);this.data=Tm();var i=this;t instanceof r?t.each(n):t&&M(t,n);function n(a,o){e?i.set(a,o):i.set(o,a)}}return r.prototype.hasKey=function(t){return this.data.has(t)},r.prototype.get=function(t){return this.data.get(t)},r.prototype.set=function(t,e){return this.data.set(t,e),e},r.prototype.each=function(t,e){this.data.forEach(function(i,n){t.call(e,i,n)})},r.prototype.keys=function(){var t=this.data.keys();return bd?Array.from(t):t},r.prototype.removeKey=function(t){this.data.delete(t)},r}();function Q(r){return new Cm(r)}function Dm(r,t){for(var e=new r.constructor(r.length+t.length),i=0;i<r.length;i++)e[i]=r[i];for(var n=r.length,i=0;i<t.length;i++)e[i+n]=t[i];return e}function ko(r,t){var e;if(Object.create)e=Object.create(r);else{var i=function(){};i.prototype=r,e=new i}return t&&O(e,t),e}function xd(r){var t=r.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function Qr(r,t){return r.hasOwnProperty(t)}function Yt(){}var Mm=180/Math.PI;function zi(r,t){return r==null&&(r=0),t==null&&(t=0),[r,t]}function Am(r){return[r[0],r[1]]}function Uf(r,t,e){return r[0]=t[0]+e[0],r[1]=t[1]+e[1],r}function Lm(r,t,e){return r[0]=t[0]-e[0],r[1]=t[1]-e[1],r}function Im(r){return Math.sqrt(Pm(r))}function Pm(r){return r[0]*r[0]+r[1]*r[1]}function vs(r,t,e){return r[0]=t[0]*e,r[1]=t[1]*e,r}function Rm(r,t){var e=Im(t);return e===0?(r[0]=0,r[1]=0):(r[0]=t[0]/e,r[1]=t[1]/e),r}function Ol(r,t){return Math.sqrt((r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1]))}var kl=Ol;function Em(r,t){return(r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1])}var Mi=Em;function cs(r,t,e,i){return r[0]=t[0]+i*(e[0]-t[0]),r[1]=t[1]+i*(e[1]-t[1]),r}function ue(r,t,e){var i=t[0],n=t[1];return r[0]=e[0]*i+e[2]*n+e[4],r[1]=e[1]*i+e[3]*n+e[5],r}function bi(r,t,e){return r[0]=Math.min(t[0],e[0]),r[1]=Math.min(t[1],e[1]),r}function xi(r,t,e){return r[0]=Math.max(t[0],e[0]),r[1]=Math.max(t[1],e[1]),r}var ni=function(){function r(t,e){this.target=t,this.topTarget=e&&e.topTarget}return r}(),Om=function(){function r(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return r.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new ni(e,t),"dragstart",t.event))},r.prototype._drag=function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,a=i-this._x,o=n-this._y;this._x=i,this._y=n,e.drift(a,o,t),this.handler.dispatchToElement(new ni(e,t),"drag",t.event);var s=this.handler.findHover(i,n,e).target,l=this._dropTarget;this._dropTarget=s,e!==s&&(l&&s!==l&&this.handler.dispatchToElement(new ni(l,t),"dragleave",t.event),s&&s!==l&&this.handler.dispatchToElement(new ni(s,t),"dragenter",t.event))}},r.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new ni(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new ni(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},r}(),Oe=function(){function r(t){t&&(this._$eventProcessor=t)}return r.prototype.on=function(t,e,i,n){this._$handlers||(this._$handlers={});var a=this._$handlers;if(typeof e=="function"&&(n=i,i=e,e=null),!i||!t)return this;var o=this._$eventProcessor;e!=null&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),a[t]||(a[t]=[]);for(var s=0;s<a[t].length;s++)if(a[t][s].h===i)return this;var l={h:i,query:e,ctx:n||this,callAtLast:i.zrEventfulCallAtLast},u=a[t].length-1,f=a[t][u];return f&&f.callAtLast?a[t].splice(u,0,l):a[t].push(l),this},r.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},r.prototype.off=function(t,e){var i=this._$handlers;if(!i)return this;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var n=[],a=0,o=i[t].length;a<o;a++)i[t][a].h!==e&&n.push(i[t][a]);i[t]=n}i[t]&&i[t].length===0&&delete i[t]}else delete i[t];return this},r.prototype.trigger=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var o=e.length,s=n.length,l=0;l<s;l++){var u=n[l];if(!(a&&a.filter&&u.query!=null&&!a.filter(t,u.query)))switch(o){case 0:u.h.call(u.ctx);break;case 1:u.h.call(u.ctx,e[0]);break;case 2:u.h.call(u.ctx,e[0],e[1]);break;default:u.h.apply(u.ctx,e);break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r.prototype.triggerWithContext=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var o=e.length,s=e[o-1],l=n.length,u=0;u<l;u++){var f=n[u];if(!(a&&a.filter&&f.query!=null&&!a.filter(t,f.query)))switch(o){case 0:f.h.call(s);break;case 1:f.h.call(s,e[0]);break;case 2:f.h.call(s,e[0],e[1]);break;default:f.h.apply(s,e.slice(1,o-1));break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r}(),km=Math.log(2);function Bl(r,t,e,i,n,a){var o=i+"-"+n,s=r.length;if(a.hasOwnProperty(o))return a[o];if(t===1){var l=Math.round(Math.log((1<<s)-1&~n)/km);return r[e][l]}for(var u=i|1<<e,f=e+1;i&1<<f;)f++;for(var h=0,c=0,v=0;c<s;c++){var d=1<<c;d&n||(h+=(v%2?-1:1)*r[e][c]*Bl(r,t-1,f,u,n|d,a),v++)}return a[o]=h,h}function Yf(r,t){var e=[[r[0],r[1],1,0,0,0,-t[0]*r[0],-t[0]*r[1]],[0,0,0,r[0],r[1],1,-t[1]*r[0],-t[1]*r[1]],[r[2],r[3],1,0,0,0,-t[2]*r[2],-t[2]*r[3]],[0,0,0,r[2],r[3],1,-t[3]*r[2],-t[3]*r[3]],[r[4],r[5],1,0,0,0,-t[4]*r[4],-t[4]*r[5]],[0,0,0,r[4],r[5],1,-t[5]*r[4],-t[5]*r[5]],[r[6],r[7],1,0,0,0,-t[6]*r[6],-t[6]*r[7]],[0,0,0,r[6],r[7],1,-t[7]*r[6],-t[7]*r[7]]],i={},n=Bl(e,8,0,0,0,i);if(n!==0){for(var a=[],o=0;o<8;o++)for(var s=0;s<8;s++)a[s]==null&&(a[s]=0),a[s]+=((o+s)%2?-1:1)*Bl(e,7,o===0?1:0,1<<o,1<<s,i)/n*t[o];return function(l,u,f){var h=u*a[6]+f*a[7]+1;l[0]=(u*a[0]+f*a[1]+a[2])/h,l[1]=(u*a[3]+f*a[4]+a[5])/h}}}var Xf="___zrEVENTSAVED",ds=[];function Bm(r,t,e,i,n){return Nl(ds,t,i,n,!0)&&Nl(r,e,ds[0],ds[1])}function Nl(r,t,e,i,n){if(t.getBoundingClientRect&&$.domSupported&&!Td(t)){var a=t[Xf]||(t[Xf]={}),o=Nm(t,a),s=Fm(o,a,n);if(s)return s(r,e,i),!0}return!1}function Nm(r,t){var e=t.markers;if(e)return e;e=t.markers=[];for(var i=["left","right"],n=["top","bottom"],a=0;a<4;a++){var o=document.createElement("div"),s=o.style,l=a%2,u=(a>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[l]+":0",n[u]+":0",i[1-l]+":auto",n[1-u]+":auto",""].join("!important;"),r.appendChild(o),e.push(o)}return e}function Fm(r,t,e){for(var i=e?"invTrans":"trans",n=t[i],a=t.srcCoords,o=[],s=[],l=!0,u=0;u<4;u++){var f=r[u].getBoundingClientRect(),h=2*u,c=f.left,v=f.top;o.push(c,v),l=l&&a&&c===a[h]&&v===a[h+1],s.push(r[u].offsetLeft,r[u].offsetTop)}return l&&n?n:(t.srcCoords=o,t[i]=e?Yf(s,o):Yf(o,s))}function Td(r){return r.nodeName.toUpperCase()==="CANVAS"}var zm=/([&<>"'])/g,Hm={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Gt(r){return r==null?"":(r+"").replace(zm,function(t,e){return Hm[e]})}var Gm=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,ps=[],Vm=$.browser.firefox&&+$.browser.version.split(".")[0]<39;function Fl(r,t,e,i){return e=e||{},i?$f(r,t,e):Vm&&t.layerX!=null&&t.layerX!==t.offsetX?(e.zrX=t.layerX,e.zrY=t.layerY):t.offsetX!=null?(e.zrX=t.offsetX,e.zrY=t.offsetY):$f(r,t,e),e}function $f(r,t,e){if($.domSupported&&r.getBoundingClientRect){var i=t.clientX,n=t.clientY;if(Td(r)){var a=r.getBoundingClientRect();e.zrX=i-a.left,e.zrY=n-a.top;return}else if(Nl(ps,r,i,n)){e.zrX=ps[0],e.zrY=ps[1];return}}e.zrX=e.zrY=0}function Vu(r){return r||window.event}function re(r,t,e){if(t=Vu(t),t.zrX!=null)return t;var i=t.type,n=i&&i.indexOf("touch")>=0;if(n){var o=i!=="touchend"?t.targetTouches[0]:t.changedTouches[0];o&&Fl(r,o,t,e)}else{Fl(r,t,t,e);var a=Wm(t);t.zrDelta=a?a/120:-(t.detail||0)/3}var s=t.button;return t.which==null&&s!==void 0&&Gm.test(t.type)&&(t.which=s&1?1:s&2?3:s&4?2:0),t}function Wm(r){var t=r.wheelDelta;if(t)return t;var e=r.deltaX,i=r.deltaY;if(e==null||i==null)return t;var n=Math.abs(i!==0?i:e),a=i>0?-1:i<0?1:e>0?-1:1;return 3*n*a}function Um(r,t,e,i){r.addEventListener(t,e,i)}function Ym(r,t,e,i){r.removeEventListener(t,e,i)}var Cd=function(r){r.preventDefault(),r.stopPropagation(),r.cancelBubble=!0},Xm=function(){function r(){this._track=[]}return r.prototype.recognize=function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},r.prototype.clear=function(){return this._track.length=0,this},r.prototype._doTrack=function(t,e,i){var n=t.touches;if(n){for(var a={points:[],touches:[],target:e,event:t},o=0,s=n.length;o<s;o++){var l=n[o],u=Fl(i,l,{});a.points.push([u.zrX,u.zrY]),a.touches.push(l)}this._track.push(a)}},r.prototype._recognize=function(t){for(var e in gs)if(gs.hasOwnProperty(e)){var i=gs[e](this._track,t);if(i)return i}},r}();function Zf(r){var t=r[1][0]-r[0][0],e=r[1][1]-r[0][1];return Math.sqrt(t*t+e*e)}function $m(r){return[(r[0][0]+r[1][0])/2,(r[0][1]+r[1][1])/2]}var gs={pinch:function(r,t){var e=r.length;if(e){var i=(r[e-1]||{}).points,n=(r[e-2]||{}).points||i;if(n&&n.length>1&&i&&i.length>1){var a=Zf(i)/Zf(n);!isFinite(a)&&(a=1),t.pinchScale=a;var o=$m(i);return t.pinchX=o[0],t.pinchY=o[1],{type:"pinch",target:r[0].target,event:t}}}}};function Ai(){return[1,0,0,1,0,0]}function Wu(r){return r[0]=1,r[1]=0,r[2]=0,r[3]=1,r[4]=0,r[5]=0,r}function Zm(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4],r[5]=t[5],r}function Li(r,t,e){var i=t[0]*e[0]+t[2]*e[1],n=t[1]*e[0]+t[3]*e[1],a=t[0]*e[2]+t[2]*e[3],o=t[1]*e[2]+t[3]*e[3],s=t[0]*e[4]+t[2]*e[5]+t[4],l=t[1]*e[4]+t[3]*e[5]+t[5];return r[0]=i,r[1]=n,r[2]=a,r[3]=o,r[4]=s,r[5]=l,r}function zl(r,t,e){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4]+e[0],r[5]=t[5]+e[1],r}function Uu(r,t,e,i){i===void 0&&(i=[0,0]);var n=t[0],a=t[2],o=t[4],s=t[1],l=t[3],u=t[5],f=Math.sin(e),h=Math.cos(e);return r[0]=n*h+s*f,r[1]=-n*f+s*h,r[2]=a*h+l*f,r[3]=-a*f+h*l,r[4]=h*(o-i[0])+f*(u-i[1])+i[0],r[5]=h*(u-i[1])-f*(o-i[0])+i[1],r}function qm(r,t,e){var i=e[0],n=e[1];return r[0]=t[0]*i,r[1]=t[1]*n,r[2]=t[2]*i,r[3]=t[3]*n,r[4]=t[4]*i,r[5]=t[5]*n,r}function Yu(r,t){var e=t[0],i=t[2],n=t[4],a=t[1],o=t[3],s=t[5],l=e*o-a*i;return l?(l=1/l,r[0]=o*l,r[1]=-a*l,r[2]=-i*l,r[3]=e*l,r[4]=(i*s-o*n)*l,r[5]=(a*n-e*s)*l,r):null}var Z=function(){function r(t,e){this.x=t||0,this.y=e||0}return r.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},r.prototype.clone=function(){return new r(this.x,this.y)},r.prototype.set=function(t,e){return this.x=t,this.y=e,this},r.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},r.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},r.prototype.scale=function(t){this.x*=t,this.y*=t},r.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},r.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},r.prototype.dot=function(t){return this.x*t.x+this.y*t.y},r.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},r.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},r.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},r.prototype.distance=function(t){var e=this.x-t.x,i=this.y-t.y;return Math.sqrt(e*e+i*i)},r.prototype.distanceSquare=function(t){var e=this.x-t.x,i=this.y-t.y;return e*e+i*i},r.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},r.prototype.transform=function(t){if(t){var e=this.x,i=this.y;return this.x=t[0]*e+t[2]*i+t[4],this.y=t[1]*e+t[3]*i+t[5],this}},r.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},r.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},r.set=function(t,e,i){t.x=e,t.y=i},r.copy=function(t,e){t.x=e.x,t.y=e.y},r.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},r.lenSquare=function(t){return t.x*t.x+t.y*t.y},r.dot=function(t,e){return t.x*e.x+t.y*e.y},r.add=function(t,e,i){t.x=e.x+i.x,t.y=e.y+i.y},r.sub=function(t,e,i){t.x=e.x-i.x,t.y=e.y-i.y},r.scale=function(t,e,i){t.x=e.x*i,t.y=e.y*i},r.scaleAndAdd=function(t,e,i,n){t.x=e.x+i.x*n,t.y=e.y+i.y*n},r.lerp=function(t,e,i,n){var a=1-n;t.x=a*e.x+n*i.x,t.y=a*e.y+n*i.y},r}(),aa=Math.min,oa=Math.max,wr=new Z,br=new Z,xr=new Z,Tr=new Z,$i=new Z,Zi=new Z,nt=function(){function r(t,e,i,n){i<0&&(t=t+i,i=-i),n<0&&(e=e+n,n=-n),this.x=t,this.y=e,this.width=i,this.height=n}return r.prototype.union=function(t){var e=aa(t.x,this.x),i=aa(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=oa(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=oa(t.y+t.height,this.y+this.height)-i:this.height=t.height,this.x=e,this.y=i},r.prototype.applyTransform=function(t){r.applyTransform(this,this,t)},r.prototype.calculateTransform=function(t){var e=this,i=t.width/e.width,n=t.height/e.height,a=Ai();return zl(a,a,[-e.x,-e.y]),qm(a,a,[i,n]),zl(a,a,[t.x,t.y]),a},r.prototype.intersect=function(t,e){if(!t)return!1;t instanceof r||(t=r.create(t));var i=this,n=i.x,a=i.x+i.width,o=i.y,s=i.y+i.height,l=t.x,u=t.x+t.width,f=t.y,h=t.y+t.height,c=!(a<l||u<n||s<f||h<o);if(e){var v=1/0,d=0,g=Math.abs(a-l),p=Math.abs(u-n),y=Math.abs(s-f),m=Math.abs(h-o),_=Math.min(g,p),S=Math.min(y,m);a<l||u<n?_>d&&(d=_,g<p?Z.set(Zi,-g,0):Z.set(Zi,p,0)):_<v&&(v=_,g<p?Z.set($i,g,0):Z.set($i,-p,0)),s<f||h<o?S>d&&(d=S,y<m?Z.set(Zi,0,-y):Z.set(Zi,0,m)):_<v&&(v=_,y<m?Z.set($i,0,y):Z.set($i,0,-m))}return e&&Z.copy(e,c?$i:Zi),c},r.prototype.contain=function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i.height},r.prototype.clone=function(){return new r(this.x,this.y,this.width,this.height)},r.prototype.copy=function(t){r.copy(this,t)},r.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},r.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},r.prototype.isZero=function(){return this.width===0||this.height===0},r.create=function(t){return new r(t.x,t.y,t.width,t.height)},r.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},r.applyTransform=function(t,e,i){if(!i){t!==e&&r.copy(t,e);return}if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var n=i[0],a=i[3],o=i[4],s=i[5];t.x=e.x*n+o,t.y=e.y*a+s,t.width=e.width*n,t.height=e.height*a,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height);return}wr.x=xr.x=e.x,wr.y=Tr.y=e.y,br.x=Tr.x=e.x+e.width,br.y=xr.y=e.y+e.height,wr.transform(i),Tr.transform(i),br.transform(i),xr.transform(i),t.x=aa(wr.x,br.x,xr.x,Tr.x),t.y=aa(wr.y,br.y,xr.y,Tr.y);var l=oa(wr.x,br.x,xr.x,Tr.x),u=oa(wr.y,br.y,xr.y,Tr.y);t.width=l-t.x,t.height=u-t.y},r}(),Dd="silent";function Km(r,t,e){return{type:r,event:e,target:t.target,topTarget:t.topTarget,cancelBubble:!1,offsetX:e.zrX,offsetY:e.zrY,gestureEvent:e.gestureEvent,pinchX:e.pinchX,pinchY:e.pinchY,pinchScale:e.pinchScale,wheelDelta:e.zrDelta,zrByTouch:e.zrByTouch,which:e.which,stop:Qm}}function Qm(){Cd(this.event)}var Jm=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.handler=null,e}return t.prototype.dispose=function(){},t.prototype.setCursor=function(){},t}(Oe),qi=function(){function r(t,e){this.x=t,this.y=e}return r}(),jm=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],ys=new nt(0,0,0,0),Md=function(r){N(t,r);function t(e,i,n,a,o){var s=r.call(this)||this;return s._hovered=new qi(0,0),s.storage=e,s.painter=i,s.painterRoot=a,s._pointerSize=o,n=n||new Jm,s.proxy=null,s.setHandlerProxy(n),s._draggingMgr=new Om(s),s}return t.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(M(jm,function(i){e.on&&e.on(i,this[i],this)},this),e.handler=this),this.proxy=e},t.prototype.mousemove=function(e){var i=e.zrX,n=e.zrY,a=Ad(this,i,n),o=this._hovered,s=o.target;s&&!s.__zr&&(o=this.findHover(o.x,o.y),s=o.target);var l=this._hovered=a?new qi(i,n):this.findHover(i,n),u=l.target,f=this.proxy;f.setCursor&&f.setCursor(u?u.cursor:"default"),s&&u!==s&&this.dispatchToElement(o,"mouseout",e),this.dispatchToElement(l,"mousemove",e),u&&u!==s&&this.dispatchToElement(l,"mouseover",e)},t.prototype.mouseout=function(e){var i=e.zrEventControl;i!=="only_globalout"&&this.dispatchToElement(this._hovered,"mouseout",e),i!=="no_globalout"&&this.trigger("globalout",{type:"globalout",event:e})},t.prototype.resize=function(){this._hovered=new qi(0,0)},t.prototype.dispatch=function(e,i){var n=this[e];n&&n.call(this,i)},t.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},t.prototype.setCursorStyle=function(e){var i=this.proxy;i.setCursor&&i.setCursor(e)},t.prototype.dispatchToElement=function(e,i,n){e=e||{};var a=e.target;if(!(a&&a.silent)){for(var o="on"+i,s=Km(i,e,n);a&&(a[o]&&(s.cancelBubble=!!a[o].call(a,s)),a.trigger(i,s),a=a.__hostTarget?a.__hostTarget:a.parent,!s.cancelBubble););s.cancelBubble||(this.trigger(i,s),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(l){typeof l[o]=="function"&&l[o].call(l,s),l.trigger&&l.trigger(i,s)}))}},t.prototype.findHover=function(e,i,n){var a=this.storage.getDisplayList(),o=new qi(e,i);if(qf(a,o,e,i,n),this._pointerSize&&!o.target){for(var s=[],l=this._pointerSize,u=l/2,f=new nt(e-u,i-u,l,l),h=a.length-1;h>=0;h--){var c=a[h];c!==n&&!c.ignore&&!c.ignoreCoarsePointer&&(!c.parent||!c.parent.ignoreCoarsePointer)&&(ys.copy(c.getBoundingRect()),c.transform&&ys.applyTransform(c.transform),ys.intersect(f)&&s.push(c))}if(s.length)for(var v=4,d=Math.PI/12,g=Math.PI*2,p=0;p<u;p+=v)for(var y=0;y<g;y+=d){var m=e+p*Math.cos(y),_=i+p*Math.sin(y);if(qf(s,o,m,_,n),o.target)return o}}return o},t.prototype.processGesture=function(e,i){this._gestureMgr||(this._gestureMgr=new Xm);var n=this._gestureMgr;i==="start"&&n.clear();var a=n.recognize(e,this.findHover(e.zrX,e.zrY,null).target,this.proxy.dom);if(i==="end"&&n.clear(),a){var o=a.type;e.gestureEvent=o;var s=new qi;s.target=a.target,this.dispatchToElement(s,o,a.event)}},t}(Oe);M(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(r){Md.prototype[r]=function(t){var e=t.zrX,i=t.zrY,n=Ad(this,e,i),a,o;if((r!=="mouseup"||!n)&&(a=this.findHover(e,i),o=a.target),r==="mousedown")this._downEl=o,this._downPoint=[t.zrX,t.zrY],this._upEl=o;else if(r==="mouseup")this._upEl=o;else if(r==="click"){if(this._downEl!==this._upEl||!this._downPoint||kl(this._downPoint,[t.zrX,t.zrY])>4)return;this._downPoint=null}this.dispatchToElement(a,r,t)}});function t0(r,t,e){if(r[r.rectHover?"rectContain":"contain"](t,e)){for(var i=r,n=void 0,a=!1;i;){if(i.ignoreClip&&(a=!0),!a){var o=i.getClipPath();if(o&&!o.contain(t,e))return!1}i.silent&&(n=!0);var s=i.__hostTarget;i=s||i.parent}return n?Dd:!0}return!1}function qf(r,t,e,i,n){for(var a=r.length-1;a>=0;a--){var o=r[a],s=void 0;if(o!==n&&!o.ignore&&(s=t0(o,e,i))&&(!t.topTarget&&(t.topTarget=o),s!==Dd)){t.target=o;break}}}function Ad(r,t,e){var i=r.painter;return t<0||t>i.getWidth()||e<0||e>i.getHeight()}var Ld=32,Ki=7;function e0(r){for(var t=0;r>=Ld;)t|=r&1,r>>=1;return r+t}function Kf(r,t,e,i){var n=t+1;if(n===e)return 1;if(i(r[n++],r[t])<0){for(;n<e&&i(r[n],r[n-1])<0;)n++;r0(r,t,n)}else for(;n<e&&i(r[n],r[n-1])>=0;)n++;return n-t}function r0(r,t,e){for(e--;t<e;){var i=r[t];r[t++]=r[e],r[e--]=i}}function Qf(r,t,e,i,n){for(i===t&&i++;i<e;i++){for(var a=r[i],o=t,s=i,l;o<s;)l=o+s>>>1,n(a,r[l])<0?s=l:o=l+1;var u=i-o;switch(u){case 3:r[o+3]=r[o+2];case 2:r[o+2]=r[o+1];case 1:r[o+1]=r[o];break;default:for(;u>0;)r[o+u]=r[o+u-1],u--}r[o]=a}}function ms(r,t,e,i,n,a){var o=0,s=0,l=1;if(a(r,t[e+n])>0){for(s=i-n;l<s&&a(r,t[e+n+l])>0;)o=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s),o+=n,l+=n}else{for(s=n+1;l<s&&a(r,t[e+n-l])<=0;)o=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s);var u=o;o=n-l,l=n-u}for(o++;o<l;){var f=o+(l-o>>>1);a(r,t[e+f])>0?o=f+1:l=f}return l}function _s(r,t,e,i,n,a){var o=0,s=0,l=1;if(a(r,t[e+n])<0){for(s=n+1;l<s&&a(r,t[e+n-l])<0;)o=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s);var u=o;o=n-l,l=n-u}else{for(s=i-n;l<s&&a(r,t[e+n+l])>=0;)o=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s),o+=n,l+=n}for(o++;o<l;){var f=o+(l-o>>>1);a(r,t[e+f])<0?l=f:o=f+1}return l}function i0(r,t){var e=Ki,i,n,a=0,o=[];i=[],n=[];function s(v,d){i[a]=v,n[a]=d,a+=1}function l(){for(;a>1;){var v=a-2;if(v>=1&&n[v-1]<=n[v]+n[v+1]||v>=2&&n[v-2]<=n[v]+n[v-1])n[v-1]<n[v+1]&&v--;else if(n[v]>n[v+1])break;f(v)}}function u(){for(;a>1;){var v=a-2;v>0&&n[v-1]<n[v+1]&&v--,f(v)}}function f(v){var d=i[v],g=n[v],p=i[v+1],y=n[v+1];n[v]=g+y,v===a-3&&(i[v+1]=i[v+2],n[v+1]=n[v+2]),a--;var m=_s(r[p],r,d,g,0,t);d+=m,g-=m,g!==0&&(y=ms(r[d+g-1],r,p,y,y-1,t),y!==0&&(g<=y?h(d,g,p,y):c(d,g,p,y)))}function h(v,d,g,p){var y=0;for(y=0;y<d;y++)o[y]=r[v+y];var m=0,_=g,S=v;if(r[S++]=r[_++],--p===0){for(y=0;y<d;y++)r[S+y]=o[m+y];return}if(d===1){for(y=0;y<p;y++)r[S+y]=r[_+y];r[S+p]=o[m];return}for(var b=e,w,x,C;;){w=0,x=0,C=!1;do if(t(r[_],o[m])<0){if(r[S++]=r[_++],x++,w=0,--p===0){C=!0;break}}else if(r[S++]=o[m++],w++,x=0,--d===1){C=!0;break}while((w|x)<b);if(C)break;do{if(w=_s(r[_],o,m,d,0,t),w!==0){for(y=0;y<w;y++)r[S+y]=o[m+y];if(S+=w,m+=w,d-=w,d<=1){C=!0;break}}if(r[S++]=r[_++],--p===0){C=!0;break}if(x=ms(o[m],r,_,p,0,t),x!==0){for(y=0;y<x;y++)r[S+y]=r[_+y];if(S+=x,_+=x,p-=x,p===0){C=!0;break}}if(r[S++]=o[m++],--d===1){C=!0;break}b--}while(w>=Ki||x>=Ki);if(C)break;b<0&&(b=0),b+=2}if(e=b,e<1&&(e=1),d===1){for(y=0;y<p;y++)r[S+y]=r[_+y];r[S+p]=o[m]}else{if(d===0)throw new Error;for(y=0;y<d;y++)r[S+y]=o[m+y]}}function c(v,d,g,p){var y=0;for(y=0;y<p;y++)o[y]=r[g+y];var m=v+d-1,_=p-1,S=g+p-1,b=0,w=0;if(r[S--]=r[m--],--d===0){for(b=S-(p-1),y=0;y<p;y++)r[b+y]=o[y];return}if(p===1){for(S-=d,m-=d,w=S+1,b=m+1,y=d-1;y>=0;y--)r[w+y]=r[b+y];r[S]=o[_];return}for(var x=e;;){var C=0,T=0,D=!1;do if(t(o[_],r[m])<0){if(r[S--]=r[m--],C++,T=0,--d===0){D=!0;break}}else if(r[S--]=o[_--],T++,C=0,--p===1){D=!0;break}while((C|T)<x);if(D)break;do{if(C=d-_s(o[_],r,v,d,d-1,t),C!==0){for(S-=C,m-=C,d-=C,w=S+1,b=m+1,y=C-1;y>=0;y--)r[w+y]=r[b+y];if(d===0){D=!0;break}}if(r[S--]=o[_--],--p===1){D=!0;break}if(T=p-ms(r[m],o,0,p,p-1,t),T!==0){for(S-=T,_-=T,p-=T,w=S+1,b=_+1,y=0;y<T;y++)r[w+y]=o[b+y];if(p<=1){D=!0;break}}if(r[S--]=r[m--],--d===0){D=!0;break}x--}while(C>=Ki||T>=Ki);if(D)break;x<0&&(x=0),x+=2}if(e=x,e<1&&(e=1),p===1){for(S-=d,m-=d,w=S+1,b=m+1,y=d-1;y>=0;y--)r[w+y]=r[b+y];r[S]=o[_]}else{if(p===0)throw new Error;for(b=S-(p-1),y=0;y<p;y++)r[b+y]=o[y]}}return{mergeRuns:l,forceMergeRuns:u,pushRun:s}}function Ya(r,t,e,i){e||(e=0),i||(i=r.length);var n=i-e;if(!(n<2)){var a=0;if(n<Ld){a=Kf(r,e,i,t),Qf(r,e,i,e+a,t);return}var o=i0(r,t),s=e0(n);do{if(a=Kf(r,e,i,t),a<s){var l=n;l>s&&(l=s),Qf(r,e,e+l,e+a,t),a=l}o.pushRun(e,a),o.mergeRuns(),n-=a,e+=a}while(n!==0);o.forceMergeRuns()}}var Kt=1,pn=2,Si=4,Jf=!1;function Ss(){Jf||(Jf=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function jf(r,t){return r.zlevel===t.zlevel?r.z===t.z?r.z2-t.z2:r.z-t.z:r.zlevel-t.zlevel}var n0=function(){function r(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=jf}return r.prototype.traverse=function(t,e){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,e)},r.prototype.getDisplayList=function(t,e){e=e||!1;var i=this._displayList;return(t||!i.length)&&this.updateDisplayList(e),i},r.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,i=this._displayList,n=0,a=e.length;n<a;n++)this._updateAndAddDisplayable(e[n],null,t);i.length=this._displayListLen,Ya(i,jf)},r.prototype._updateAndAddDisplayable=function(t,e,i){if(!(t.ignore&&!i)){t.beforeUpdate(),t.update(),t.afterUpdate();var n=t.getClipPath();if(t.ignoreClip)e=null;else if(n){e?e=e.slice():e=[];for(var a=n,o=t;a;)a.parent=o,a.updateTransform(),e.push(a),o=a,a=a.getClipPath()}if(t.childrenRef){for(var s=t.childrenRef(),l=0;l<s.length;l++){var u=s[l];t.__dirty&&(u.__dirty|=Kt),this._updateAndAddDisplayable(u,e,i)}t.__dirty=0}else{var f=t;e&&e.length?f.__clipPaths=e:f.__clipPaths&&f.__clipPaths.length>0&&(f.__clipPaths=[]),isNaN(f.z)&&(Ss(),f.z=0),isNaN(f.z2)&&(Ss(),f.z2=0),isNaN(f.zlevel)&&(Ss(),f.zlevel=0),this._displayList[this._displayListLen++]=f}var h=t.getDecalElement&&t.getDecalElement();h&&this._updateAndAddDisplayable(h,e,i);var c=t.getTextGuideLine();c&&this._updateAndAddDisplayable(c,e,i);var v=t.getTextContent();v&&this._updateAndAddDisplayable(v,e,i)}},r.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},r.prototype.delRoot=function(t){if(t instanceof Array){for(var e=0,i=t.length;e<i;e++)this.delRoot(t[e]);return}var n=lt(this._roots,t);n>=0&&this._roots.splice(n,1)},r.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},r.prototype.getRoots=function(){return this._roots},r.prototype.dispose=function(){this._displayList=null,this._roots=null},r}(),oo;oo=$.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(r){return setTimeout(r,16)};var Tn={linear:function(r){return r},quadraticIn:function(r){return r*r},quadraticOut:function(r){return r*(2-r)},quadraticInOut:function(r){return(r*=2)<1?.5*r*r:-.5*(--r*(r-2)-1)},cubicIn:function(r){return r*r*r},cubicOut:function(r){return--r*r*r+1},cubicInOut:function(r){return(r*=2)<1?.5*r*r*r:.5*((r-=2)*r*r+2)},quarticIn:function(r){return r*r*r*r},quarticOut:function(r){return 1- --r*r*r*r},quarticInOut:function(r){return(r*=2)<1?.5*r*r*r*r:-.5*((r-=2)*r*r*r-2)},quinticIn:function(r){return r*r*r*r*r},quinticOut:function(r){return--r*r*r*r*r+1},quinticInOut:function(r){return(r*=2)<1?.5*r*r*r*r*r:.5*((r-=2)*r*r*r*r+2)},sinusoidalIn:function(r){return 1-Math.cos(r*Math.PI/2)},sinusoidalOut:function(r){return Math.sin(r*Math.PI/2)},sinusoidalInOut:function(r){return .5*(1-Math.cos(Math.PI*r))},exponentialIn:function(r){return r===0?0:Math.pow(1024,r-1)},exponentialOut:function(r){return r===1?1:1-Math.pow(2,-10*r)},exponentialInOut:function(r){return r===0?0:r===1?1:(r*=2)<1?.5*Math.pow(1024,r-1):.5*(-Math.pow(2,-10*(r-1))+2)},circularIn:function(r){return 1-Math.sqrt(1-r*r)},circularOut:function(r){return Math.sqrt(1- --r*r)},circularInOut:function(r){return(r*=2)<1?-.5*(Math.sqrt(1-r*r)-1):.5*(Math.sqrt(1-(r-=2)*r)+1)},elasticIn:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),-(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)))},elasticOut:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),e*Math.pow(2,-10*r)*Math.sin((r-t)*(2*Math.PI)/i)+1)},elasticInOut:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),(r*=2)<1?-.5*(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)):e*Math.pow(2,-10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)*.5+1)},backIn:function(r){var t=1.70158;return r*r*((t+1)*r-t)},backOut:function(r){var t=1.70158;return--r*r*((t+1)*r+t)+1},backInOut:function(r){var t=2.5949095;return(r*=2)<1?.5*(r*r*((t+1)*r-t)):.5*((r-=2)*r*((t+1)*r+t)+2)},bounceIn:function(r){return 1-Tn.bounceOut(1-r)},bounceOut:function(r){return r<1/2.75?7.5625*r*r:r<2/2.75?7.5625*(r-=1.5/2.75)*r+.75:r<2.5/2.75?7.5625*(r-=2.25/2.75)*r+.9375:7.5625*(r-=2.625/2.75)*r+.984375},bounceInOut:function(r){return r<.5?Tn.bounceIn(r*2)*.5:Tn.bounceOut(r*2-1)*.5+.5}},sa=Math.pow,hr=Math.sqrt,so=1e-8,Id=1e-4,th=hr(3),la=1/3,Me=zi(),ae=zi(),Ii=zi();function sr(r){return r>-so&&r<so}function Pd(r){return r>so||r<-so}function At(r,t,e,i,n){var a=1-n;return a*a*(a*r+3*n*t)+n*n*(n*i+3*a*e)}function eh(r,t,e,i,n){var a=1-n;return 3*(((t-r)*a+2*(e-t)*n)*a+(i-e)*n*n)}function lo(r,t,e,i,n,a){var o=i+3*(t-e)-r,s=3*(e-t*2+r),l=3*(t-r),u=r-n,f=s*s-3*o*l,h=s*l-9*o*u,c=l*l-3*s*u,v=0;if(sr(f)&&sr(h))if(sr(s))a[0]=0;else{var d=-l/s;d>=0&&d<=1&&(a[v++]=d)}else{var g=h*h-4*f*c;if(sr(g)){var p=h/f,d=-s/o+p,y=-p/2;d>=0&&d<=1&&(a[v++]=d),y>=0&&y<=1&&(a[v++]=y)}else if(g>0){var m=hr(g),_=f*s+1.5*o*(-h+m),S=f*s+1.5*o*(-h-m);_<0?_=-sa(-_,la):_=sa(_,la),S<0?S=-sa(-S,la):S=sa(S,la);var d=(-s-(_+S))/(3*o);d>=0&&d<=1&&(a[v++]=d)}else{var b=(2*f*s-3*o*h)/(2*hr(f*f*f)),w=Math.acos(b)/3,x=hr(f),C=Math.cos(w),d=(-s-2*x*C)/(3*o),y=(-s+x*(C+th*Math.sin(w)))/(3*o),T=(-s+x*(C-th*Math.sin(w)))/(3*o);d>=0&&d<=1&&(a[v++]=d),y>=0&&y<=1&&(a[v++]=y),T>=0&&T<=1&&(a[v++]=T)}}return v}function Rd(r,t,e,i,n){var a=6*e-12*t+6*r,o=9*t+3*i-3*r-9*e,s=3*t-3*r,l=0;if(sr(o)){if(Pd(a)){var u=-s/a;u>=0&&u<=1&&(n[l++]=u)}}else{var f=a*a-4*o*s;if(sr(f))n[0]=-a/(2*o);else if(f>0){var h=hr(f),u=(-a+h)/(2*o),c=(-a-h)/(2*o);u>=0&&u<=1&&(n[l++]=u),c>=0&&c<=1&&(n[l++]=c)}}return l}function uo(r,t,e,i,n,a){var o=(t-r)*n+r,s=(e-t)*n+t,l=(i-e)*n+e,u=(s-o)*n+o,f=(l-s)*n+s,h=(f-u)*n+u;a[0]=r,a[1]=o,a[2]=u,a[3]=h,a[4]=h,a[5]=f,a[6]=l,a[7]=i}function a0(r,t,e,i,n,a,o,s,l,u,f){var h,c=.005,v=1/0,d,g,p,y;Me[0]=l,Me[1]=u;for(var m=0;m<1;m+=.05)ae[0]=At(r,e,n,o,m),ae[1]=At(t,i,a,s,m),p=Mi(Me,ae),p<v&&(h=m,v=p);v=1/0;for(var _=0;_<32&&!(c<Id);_++)d=h-c,g=h+c,ae[0]=At(r,e,n,o,d),ae[1]=At(t,i,a,s,d),p=Mi(ae,Me),d>=0&&p<v?(h=d,v=p):(Ii[0]=At(r,e,n,o,g),Ii[1]=At(t,i,a,s,g),y=Mi(Ii,Me),g<=1&&y<v?(h=g,v=y):c*=.5);return hr(v)}function o0(r,t,e,i,n,a,o,s,l){for(var u=r,f=t,h=0,c=1/l,v=1;v<=l;v++){var d=v*c,g=At(r,e,n,o,d),p=At(t,i,a,s,d),y=g-u,m=p-f;h+=Math.sqrt(y*y+m*m),u=g,f=p}return h}function Vt(r,t,e,i){var n=1-i;return n*(n*r+2*i*t)+i*i*e}function rh(r,t,e,i){return 2*((1-i)*(t-r)+i*(e-t))}function s0(r,t,e,i,n){var a=r-2*t+e,o=2*(t-r),s=r-i,l=0;if(sr(a)){if(Pd(o)){var u=-s/o;u>=0&&u<=1&&(n[l++]=u)}}else{var f=o*o-4*a*s;if(sr(f)){var u=-o/(2*a);u>=0&&u<=1&&(n[l++]=u)}else if(f>0){var h=hr(f),u=(-o+h)/(2*a),c=(-o-h)/(2*a);u>=0&&u<=1&&(n[l++]=u),c>=0&&c<=1&&(n[l++]=c)}}return l}function Ed(r,t,e){var i=r+e-2*t;return i===0?.5:(r-t)/i}function fo(r,t,e,i,n){var a=(t-r)*i+r,o=(e-t)*i+t,s=(o-a)*i+a;n[0]=r,n[1]=a,n[2]=s,n[3]=s,n[4]=o,n[5]=e}function l0(r,t,e,i,n,a,o,s,l){var u,f=.005,h=1/0;Me[0]=o,Me[1]=s;for(var c=0;c<1;c+=.05){ae[0]=Vt(r,e,n,c),ae[1]=Vt(t,i,a,c);var v=Mi(Me,ae);v<h&&(u=c,h=v)}h=1/0;for(var d=0;d<32&&!(f<Id);d++){var g=u-f,p=u+f;ae[0]=Vt(r,e,n,g),ae[1]=Vt(t,i,a,g);var v=Mi(ae,Me);if(g>=0&&v<h)u=g,h=v;else{Ii[0]=Vt(r,e,n,p),Ii[1]=Vt(t,i,a,p);var y=Mi(Ii,Me);p<=1&&y<h?(u=p,h=y):f*=.5}}return hr(h)}function u0(r,t,e,i,n,a,o){for(var s=r,l=t,u=0,f=1/o,h=1;h<=o;h++){var c=h*f,v=Vt(r,e,n,c),d=Vt(t,i,a,c),g=v-s,p=d-l;u+=Math.sqrt(g*g+p*p),s=v,l=d}return u}var f0=/cubic-bezier\(([0-9,\.e ]+)\)/;function Od(r){var t=r&&f0.exec(r);if(t){var e=t[1].split(","),i=+Le(e[0]),n=+Le(e[1]),a=+Le(e[2]),o=+Le(e[3]);if(isNaN(i+n+a+o))return;var s=[];return function(l){return l<=0?0:l>=1?1:lo(0,i,a,1,l,s)&&At(0,n,o,1,s[0])}}}var h0=function(){function r(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||Yt,this.ondestroy=t.ondestroy||Yt,this.onrestart=t.onrestart||Yt,t.easing&&this.setEasing(t.easing)}return r.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),this._paused){this._pausedTime+=e;return}var i=this._life,n=t-this._startTime-this._pausedTime,a=n/i;a<0&&(a=0),a=Math.min(a,1);var o=this.easingFunc,s=o?o(a):a;if(this.onframe(s),a===1)if(this.loop){var l=n%i;this._startTime=t-l,this._pausedTime=0,this.onrestart()}else return!0;return!1},r.prototype.pause=function(){this._paused=!0},r.prototype.resume=function(){this._paused=!1},r.prototype.setEasing=function(t){this.easing=t,this.easingFunc=q(t)?t:Tn[t]||Od(t)},r}(),kd=function(){function r(t){this.value=t}return r}(),v0=function(){function r(){this._len=0}return r.prototype.insert=function(t){var e=new kd(t);return this.insertEntry(e),e},r.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},r.prototype.remove=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},r.prototype.len=function(){return this._len},r.prototype.clear=function(){this.head=this.tail=null,this._len=0},r}(),jn=function(){function r(t){this._list=new v0,this._maxSize=10,this._map={},this._maxSize=t}return r.prototype.put=function(t,e){var i=this._list,n=this._map,a=null;if(n[t]==null){var o=i.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var l=i.head;i.remove(l),delete n[l.key],a=l.value,this._lastRemovedEntry=l}s?s.value=e:s=new kd(e),s.key=t,i.insertEntry(s),n[t]=s}return a},r.prototype.get=function(t){var e=this._map[t],i=this._list;if(e!=null)return e!==i.tail&&(i.remove(e),i.insertEntry(e)),e.value},r.prototype.clear=function(){this._list.clear(),this._map={}},r.prototype.len=function(){return this._list.len()},r}(),ih={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function vr(r){return r=Math.round(r),r<0?0:r>255?255:r}function Hl(r){return r<0?0:r>1?1:r}function ws(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?vr(parseFloat(t)/100*255):vr(parseInt(t,10))}function Cn(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?Hl(parseFloat(t)/100):Hl(parseFloat(t))}function bs(r,t,e){return e<0?e+=1:e>1&&(e-=1),e*6<1?r+(t-r)*e*6:e*2<1?t:e*3<2?r+(t-r)*(2/3-e)*6:r}function ua(r,t,e){return r+(t-r)*e}function ee(r,t,e,i,n){return r[0]=t,r[1]=e,r[2]=i,r[3]=n,r}function Gl(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r}var Bd=new jn(20),fa=null;function ai(r,t){fa&&Gl(fa,t),fa=Bd.put(r,fa||t.slice())}function We(r,t){if(r){t=t||[];var e=Bd.get(r);if(e)return Gl(t,e);r=r+"";var i=r.replace(/ /g,"").toLowerCase();if(i in ih)return Gl(t,ih[i]),ai(r,t),t;var n=i.length;if(i.charAt(0)==="#"){if(n===4||n===5){var a=parseInt(i.slice(1,4),16);if(!(a>=0&&a<=4095)){ee(t,0,0,0,1);return}return ee(t,(a&3840)>>4|(a&3840)>>8,a&240|(a&240)>>4,a&15|(a&15)<<4,n===5?parseInt(i.slice(4),16)/15:1),ai(r,t),t}else if(n===7||n===9){var a=parseInt(i.slice(1,7),16);if(!(a>=0&&a<=16777215)){ee(t,0,0,0,1);return}return ee(t,(a&16711680)>>16,(a&65280)>>8,a&255,n===9?parseInt(i.slice(7),16)/255:1),ai(r,t),t}return}var o=i.indexOf("("),s=i.indexOf(")");if(o!==-1&&s+1===n){var l=i.substr(0,o),u=i.substr(o+1,s-(o+1)).split(","),f=1;switch(l){case"rgba":if(u.length!==4)return u.length===3?ee(t,+u[0],+u[1],+u[2],1):ee(t,0,0,0,1);f=Cn(u.pop());case"rgb":if(u.length>=3)return ee(t,ws(u[0]),ws(u[1]),ws(u[2]),u.length===3?f:Cn(u[3])),ai(r,t),t;ee(t,0,0,0,1);return;case"hsla":if(u.length!==4){ee(t,0,0,0,1);return}return u[3]=Cn(u[3]),nh(u,t),ai(r,t),t;case"hsl":if(u.length!==3){ee(t,0,0,0,1);return}return nh(u,t),ai(r,t),t;default:return}}ee(t,0,0,0,1)}}function nh(r,t){var e=(parseFloat(r[0])%360+360)%360/360,i=Cn(r[1]),n=Cn(r[2]),a=n<=.5?n*(i+1):n+i-n*i,o=n*2-a;return t=t||[],ee(t,vr(bs(o,a,e+1/3)*255),vr(bs(o,a,e)*255),vr(bs(o,a,e-1/3)*255),1),r.length===4&&(t[3]=r[3]),t}function ah(r,t){var e=We(r);if(e){for(var i=0;i<3;i++)e[i]=e[i]*(1-t)|0,e[i]>255?e[i]=255:e[i]<0&&(e[i]=0);return Bo(e,e.length===4?"rgba":"rgb")}}function c0(r,t,e){if(!(!(t&&t.length)||!(r>=0&&r<=1))){var i=r*(t.length-1),n=Math.floor(i),a=Math.ceil(i),o=We(t[n]),s=We(t[a]),l=i-n,u=Bo([vr(ua(o[0],s[0],l)),vr(ua(o[1],s[1],l)),vr(ua(o[2],s[2],l)),Hl(ua(o[3],s[3],l))],"rgba");return e?{color:u,leftIndex:n,rightIndex:a,value:i}:u}}function Bo(r,t){if(!(!r||!r.length)){var e=r[0]+","+r[1]+","+r[2];return(t==="rgba"||t==="hsva"||t==="hsla")&&(e+=","+r[3]),t+"("+e+")"}}function ho(r,t){var e=We(r);return e?(.299*e[0]+.587*e[1]+.114*e[2])*e[3]/255+(1-e[3])*t:0}var oh=new jn(100);function sh(r){if(G(r)){var t=oh.get(r);return t||(t=ah(r,-.1),oh.put(r,t)),t}else if(Oo(r)){var e=O({},r);return e.colorStops=V(r.colorStops,function(i){return{offset:i.offset,color:ah(i.color,-.1)}}),e}return r}function d0(r){return r.type==="linear"}function p0(r){return r.type==="radial"}(function(){return $.hasGlobalWindow&&q(window.btoa)?function(r){return window.btoa(unescape(encodeURIComponent(r)))}:typeof Buffer<"u"?function(r){return Buffer.from(r).toString("base64")}:function(r){return null}})();var Vl=Array.prototype.slice;function He(r,t,e){return(t-r)*e+r}function xs(r,t,e,i){for(var n=t.length,a=0;a<n;a++)r[a]=He(t[a],e[a],i);return r}function g0(r,t,e,i){for(var n=t.length,a=n&&t[0].length,o=0;o<n;o++){r[o]||(r[o]=[]);for(var s=0;s<a;s++)r[o][s]=He(t[o][s],e[o][s],i)}return r}function ha(r,t,e,i){for(var n=t.length,a=0;a<n;a++)r[a]=t[a]+e[a]*i;return r}function lh(r,t,e,i){for(var n=t.length,a=n&&t[0].length,o=0;o<n;o++){r[o]||(r[o]=[]);for(var s=0;s<a;s++)r[o][s]=t[o][s]+e[o][s]*i}return r}function y0(r,t){for(var e=r.length,i=t.length,n=e>i?t:r,a=Math.min(e,i),o=n[a-1]||{color:[0,0,0,0],offset:0},s=a;s<Math.max(e,i);s++)n.push({offset:o.offset,color:o.color.slice()})}function m0(r,t,e){var i=r,n=t;if(!(!i.push||!n.push)){var a=i.length,o=n.length;if(a!==o){var s=a>o;if(s)i.length=o;else for(var l=a;l<o;l++)i.push(e===1?n[l]:Vl.call(n[l]))}for(var u=i[0]&&i[0].length,l=0;l<i.length;l++)if(e===1)isNaN(i[l])&&(i[l]=n[l]);else for(var f=0;f<u;f++)isNaN(i[l][f])&&(i[l][f]=n[l][f])}}function Xa(r){if(Xt(r)){var t=r.length;if(Xt(r[0])){for(var e=[],i=0;i<t;i++)e.push(Vl.call(r[i]));return e}return Vl.call(r)}return r}function $a(r){return r[0]=Math.floor(r[0])||0,r[1]=Math.floor(r[1])||0,r[2]=Math.floor(r[2])||0,r[3]=r[3]==null?1:r[3],"rgba("+r.join(",")+")"}function _0(r){return Xt(r&&r[0])?2:1}var va=0,Za=1,Nd=2,gn=3,Wl=4,Ul=5,uh=6;function fh(r){return r===Wl||r===Ul}function ca(r){return r===Za||r===Nd}var Qi=[0,0,0,0],S0=function(){function r(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return r.prototype.isFinished=function(){return this._finished},r.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},r.prototype.needsAnimate=function(){return this.keyframes.length>=1},r.prototype.getAdditiveTrack=function(){return this._additiveTrack},r.prototype.addKeyframe=function(t,e,i){this._needsSort=!0;var n=this.keyframes,a=n.length,o=!1,s=uh,l=e;if(Xt(e)){var u=_0(e);s=u,(u===1&&!vt(e[0])||u===2&&!vt(e[0][0]))&&(o=!0)}else if(vt(e)&&!ao(e))s=va;else if(G(e))if(!isNaN(+e))s=va;else{var f=We(e);f&&(l=f,s=gn)}else if(Oo(e)){var h=O({},l);h.colorStops=V(e.colorStops,function(v){return{offset:v.offset,color:We(v.color)}}),d0(e)?s=Wl:p0(e)&&(s=Ul),l=h}a===0?this.valType=s:(s!==this.valType||s===uh)&&(o=!0),this.discrete=this.discrete||o;var c={time:t,value:l,rawValue:e,percent:0};return i&&(c.easing=i,c.easingFunc=q(i)?i:Tn[i]||Od(i)),n.push(c),c},r.prototype.prepare=function(t,e){var i=this.keyframes;this._needsSort&&i.sort(function(g,p){return g.time-p.time});for(var n=this.valType,a=i.length,o=i[a-1],s=this.discrete,l=ca(n),u=fh(n),f=0;f<a;f++){var h=i[f],c=h.value,v=o.value;h.percent=h.time/t,s||(l&&f!==a-1?m0(c,v,n):u&&y0(c.colorStops,v.colorStops))}if(!s&&n!==Ul&&e&&this.needsAnimate()&&e.needsAnimate()&&n===e.valType&&!e._finished){this._additiveTrack=e;for(var d=i[0].value,f=0;f<a;f++)n===va?i[f].additiveValue=i[f].value-d:n===gn?i[f].additiveValue=ha([],i[f].value,d,-1):ca(n)&&(i[f].additiveValue=n===Za?ha([],i[f].value,d,-1):lh([],i[f].value,d,-1))}},r.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var i=this._additiveTrack!=null,n=i?"additiveValue":"value",a=this.valType,o=this.keyframes,s=o.length,l=this.propName,u=a===gn,f,h=this._lastFr,c=Math.min,v,d;if(s===1)v=d=o[0];else{if(e<0)f=0;else if(e<this._lastFrP){var g=c(h+1,s-1);for(f=g;f>=0&&!(o[f].percent<=e);f--);f=c(f,s-2)}else{for(f=h;f<s&&!(o[f].percent>e);f++);f=c(f-1,s-2)}d=o[f+1],v=o[f]}if(v&&d){this._lastFr=f,this._lastFrP=e;var p=d.percent-v.percent,y=p===0?1:c((e-v.percent)/p,1);d.easingFunc&&(y=d.easingFunc(y));var m=i?this._additiveValue:u?Qi:t[l];if((ca(a)||u)&&!m&&(m=this._additiveValue=[]),this.discrete)t[l]=y<1?v.rawValue:d.rawValue;else if(ca(a))a===Za?xs(m,v[n],d[n],y):g0(m,v[n],d[n],y);else if(fh(a)){var _=v[n],S=d[n],b=a===Wl;t[l]={type:b?"linear":"radial",x:He(_.x,S.x,y),y:He(_.y,S.y,y),colorStops:V(_.colorStops,function(x,C){var T=S.colorStops[C];return{offset:He(x.offset,T.offset,y),color:$a(xs([],x.color,T.color,y))}}),global:S.global},b?(t[l].x2=He(_.x2,S.x2,y),t[l].y2=He(_.y2,S.y2,y)):t[l].r=He(_.r,S.r,y)}else if(u)xs(m,v[n],d[n],y),i||(t[l]=$a(m));else{var w=He(v[n],d[n],y);i?this._additiveValue=w:t[l]=w}i&&this._addToTarget(t)}}},r.prototype._addToTarget=function(t){var e=this.valType,i=this.propName,n=this._additiveValue;e===va?t[i]=t[i]+n:e===gn?(We(t[i],Qi),ha(Qi,Qi,n,1),t[i]=$a(Qi)):e===Za?ha(t[i],t[i],n,1):e===Nd&&lh(t[i],t[i],n,1)},r}(),Xu=function(){function r(t,e,i,n){if(this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&n){Hu("Can' use additive animation on looped animation.");return}this._additiveAnimators=n,this._allowDiscrete=i}return r.prototype.getMaxTime=function(){return this._maxTime},r.prototype.getDelay=function(){return this._delay},r.prototype.getLoop=function(){return this._loop},r.prototype.getTarget=function(){return this._target},r.prototype.changeTarget=function(t){this._target=t},r.prototype.when=function(t,e,i){return this.whenWithKeys(t,e,dt(e),i)},r.prototype.whenWithKeys=function(t,e,i,n){for(var a=this._tracks,o=0;o<i.length;o++){var s=i[o],l=a[s];if(!l){l=a[s]=new S0(s);var u=void 0,f=this._getAdditiveTrack(s);if(f){var h=f.keyframes,c=h[h.length-1];u=c&&c.value,f.valType===gn&&u&&(u=$a(u))}else u=this._target[s];if(u==null)continue;t>0&&l.addKeyframe(0,Xa(u),n),this._trackKeys.push(s)}l.addKeyframe(t,Xa(e[s]),n)}return this._maxTime=Math.max(this._maxTime,t),this},r.prototype.pause=function(){this._clip.pause(),this._paused=!0},r.prototype.resume=function(){this._clip.resume(),this._paused=!1},r.prototype.isPaused=function(){return!!this._paused},r.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},r.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,i=0;i<e;i++)t[i].call(this)},r.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var i=0;i<e.length;i++)e[i].call(this)},r.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,i=0;i<e.length;i++)t[e[i]].setFinished()},r.prototype._getAdditiveTrack=function(t){var e,i=this._additiveAnimators;if(i)for(var n=0;n<i.length;n++){var a=i[n].getTrack(t);a&&(e=a)}return e},r.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,i=[],n=this._maxTime||0,a=0;a<this._trackKeys.length;a++){var o=this._trackKeys[a],s=this._tracks[o],l=this._getAdditiveTrack(o),u=s.keyframes,f=u.length;if(s.prepare(n,l),s.needsAnimate())if(!this._allowDiscrete&&s.discrete){var h=u[f-1];h&&(e._target[s.propName]=h.rawValue),s.setFinished()}else i.push(s)}if(i.length||this._force){var c=new h0({life:n,loop:this._loop,delay:this._delay||0,onframe:function(v){e._started=2;var d=e._additiveAnimators;if(d){for(var g=!1,p=0;p<d.length;p++)if(d[p]._clip){g=!0;break}g||(e._additiveAnimators=null)}for(var p=0;p<i.length;p++)i[p].step(e._target,v);var y=e._onframeCbs;if(y)for(var p=0;p<y.length;p++)y[p](e._target,v)},ondestroy:function(){e._doneCallback()}});this._clip=c,this.animation&&this.animation.addClip(c),t&&c.setEasing(t)}else this._doneCallback();return this}},r.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},r.prototype.delay=function(t){return this._delay=t,this},r.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},r.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},r.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},r.prototype.getClip=function(){return this._clip},r.prototype.getTrack=function(t){return this._tracks[t]},r.prototype.getTracks=function(){var t=this;return V(this._trackKeys,function(e){return t._tracks[e]})},r.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var i=this._tracks,n=this._trackKeys,a=0;a<t.length;a++){var o=i[t[a]];o&&!o.isFinished()&&(e?o.step(this._target,1):this._started===1&&o.step(this._target,0),o.setFinished())}for(var s=!0,a=0;a<n.length;a++)if(!i[n[a]].isFinished()){s=!1;break}return s&&this._abortedCallback(),s},r.prototype.saveTo=function(t,e,i){if(t){e=e||this._trackKeys;for(var n=0;n<e.length;n++){var a=e[n],o=this._tracks[a];if(!(!o||o.isFinished())){var s=o.keyframes,l=s[i?0:s.length-1];l&&(t[a]=Xa(l.rawValue))}}}},r.prototype.__changeFinalValue=function(t,e){e=e||dt(t);for(var i=0;i<e.length;i++){var n=e[i],a=this._tracks[n];if(a){var o=a.keyframes;if(o.length>1){var s=o.pop();a.addKeyframe(s.time,t[n]),a.prepare(this._maxTime,a.getAdditiveTrack())}}}},r}();function Ti(){return new Date().getTime()}var w0=function(r){N(t,r);function t(e){var i=r.call(this)||this;return i._running=!1,i._time=0,i._pausedTime=0,i._pauseStart=0,i._paused=!1,e=e||{},i.stage=e.stage||{},i}return t.prototype.addClip=function(e){e.animation&&this.removeClip(e),this._head?(this._tail.next=e,e.prev=this._tail,e.next=null,this._tail=e):this._head=this._tail=e,e.animation=this},t.prototype.addAnimator=function(e){e.animation=this;var i=e.getClip();i&&this.addClip(i)},t.prototype.removeClip=function(e){if(e.animation){var i=e.prev,n=e.next;i?i.next=n:this._head=n,n?n.prev=i:this._tail=i,e.next=e.prev=e.animation=null}},t.prototype.removeAnimator=function(e){var i=e.getClip();i&&this.removeClip(i),e.animation=null},t.prototype.update=function(e){for(var i=Ti()-this._pausedTime,n=i-this._time,a=this._head;a;){var o=a.next,s=a.step(i,n);s&&(a.ondestroy(),this.removeClip(a)),a=o}this._time=i,e||(this.trigger("frame",n),this.stage.update&&this.stage.update())},t.prototype._startLoop=function(){var e=this;this._running=!0;function i(){e._running&&(oo(i),!e._paused&&e.update())}oo(i)},t.prototype.start=function(){this._running||(this._time=Ti(),this._pausedTime=0,this._startLoop())},t.prototype.stop=function(){this._running=!1},t.prototype.pause=function(){this._paused||(this._pauseStart=Ti(),this._paused=!0)},t.prototype.resume=function(){this._paused&&(this._pausedTime+=Ti()-this._pauseStart,this._paused=!1)},t.prototype.clear=function(){for(var e=this._head;e;){var i=e.next;e.prev=e.next=e.animation=null,e=i}this._head=this._tail=null},t.prototype.isFinished=function(){return this._head==null},t.prototype.animate=function(e,i){i=i||{},this.start();var n=new Xu(e,i.loop);return this.addAnimator(n),n},t}(Oe),b0=300,Ts=$.domSupported,Cs=function(){var r=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],t=["touchstart","touchend","touchmove"],e={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},i=V(r,function(n){var a=n.replace("mouse","pointer");return e.hasOwnProperty(a)?a:n});return{mouse:r,touch:t,pointer:i}}(),hh={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},vh=!1;function Yl(r){var t=r.pointerType;return t==="pen"||t==="touch"}function x0(r){r.touching=!0,r.touchTimer!=null&&(clearTimeout(r.touchTimer),r.touchTimer=null),r.touchTimer=setTimeout(function(){r.touching=!1,r.touchTimer=null},700)}function Ds(r){r&&(r.zrByTouch=!0)}function T0(r,t){return re(r.dom,new C0(r,t),!0)}function Fd(r,t){for(var e=t,i=!1;e&&e.nodeType!==9&&!(i=e.domBelongToZr||e!==t&&e===r.painterRoot);)e=e.parentNode;return i}var C0=function(){function r(t,e){this.stopPropagation=Yt,this.stopImmediatePropagation=Yt,this.preventDefault=Yt,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}return r}(),pe={mousedown:function(r){r=re(this.dom,r),this.__mayPointerCapture=[r.zrX,r.zrY],this.trigger("mousedown",r)},mousemove:function(r){r=re(this.dom,r);var t=this.__mayPointerCapture;t&&(r.zrX!==t[0]||r.zrY!==t[1])&&this.__togglePointerCapture(!0),this.trigger("mousemove",r)},mouseup:function(r){r=re(this.dom,r),this.__togglePointerCapture(!1),this.trigger("mouseup",r)},mouseout:function(r){r=re(this.dom,r);var t=r.toElement||r.relatedTarget;Fd(this,t)||(this.__pointerCapturing&&(r.zrEventControl="no_globalout"),this.trigger("mouseout",r))},wheel:function(r){vh=!0,r=re(this.dom,r),this.trigger("mousewheel",r)},mousewheel:function(r){vh||(r=re(this.dom,r),this.trigger("mousewheel",r))},touchstart:function(r){r=re(this.dom,r),Ds(r),this.__lastTouchMoment=new Date,this.handler.processGesture(r,"start"),pe.mousemove.call(this,r),pe.mousedown.call(this,r)},touchmove:function(r){r=re(this.dom,r),Ds(r),this.handler.processGesture(r,"change"),pe.mousemove.call(this,r)},touchend:function(r){r=re(this.dom,r),Ds(r),this.handler.processGesture(r,"end"),pe.mouseup.call(this,r),+new Date-+this.__lastTouchMoment<b0&&pe.click.call(this,r)},pointerdown:function(r){pe.mousedown.call(this,r)},pointermove:function(r){Yl(r)||pe.mousemove.call(this,r)},pointerup:function(r){pe.mouseup.call(this,r)},pointerout:function(r){Yl(r)||pe.mouseout.call(this,r)}};M(["click","dblclick","contextmenu"],function(r){pe[r]=function(t){t=re(this.dom,t),this.trigger(r,t)}});var Xl={pointermove:function(r){Yl(r)||Xl.mousemove.call(this,r)},pointerup:function(r){Xl.mouseup.call(this,r)},mousemove:function(r){this.trigger("mousemove",r)},mouseup:function(r){var t=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",r),t&&(r.zrEventControl="only_globalout",this.trigger("mouseout",r))}};function D0(r,t){var e=t.domHandlers;$.pointerEventsSupported?M(Cs.pointer,function(i){qa(t,i,function(n){e[i].call(r,n)})}):($.touchEventsSupported&&M(Cs.touch,function(i){qa(t,i,function(n){e[i].call(r,n),x0(t)})}),M(Cs.mouse,function(i){qa(t,i,function(n){n=Vu(n),t.touching||e[i].call(r,n)})}))}function M0(r,t){$.pointerEventsSupported?M(hh.pointer,e):$.touchEventsSupported||M(hh.mouse,e);function e(i){function n(a){a=Vu(a),Fd(r,a.target)||(a=T0(r,a),t.domHandlers[i].call(r,a))}qa(t,i,n,{capture:!0})}}function qa(r,t,e,i){r.mounted[t]=e,r.listenerOpts[t]=i,Um(r.domTarget,t,e,i)}function Ms(r){var t=r.mounted;for(var e in t)t.hasOwnProperty(e)&&Ym(r.domTarget,e,t[e],r.listenerOpts[e]);r.mounted={}}var ch=function(){function r(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e}return r}(),A0=function(r){N(t,r);function t(e,i){var n=r.call(this)||this;return n.__pointerCapturing=!1,n.dom=e,n.painterRoot=i,n._localHandlerScope=new ch(e,pe),Ts&&(n._globalHandlerScope=new ch(document,Xl)),D0(n,n._localHandlerScope),n}return t.prototype.dispose=function(){Ms(this._localHandlerScope),Ts&&Ms(this._globalHandlerScope)},t.prototype.setCursor=function(e){this.dom.style&&(this.dom.style.cursor=e||"default")},t.prototype.__togglePointerCapture=function(e){if(this.__mayPointerCapture=null,Ts&&+this.__pointerCapturing^+e){this.__pointerCapturing=e;var i=this._globalHandlerScope;e?M0(this,i):Ms(i)}},t}(Oe),zd=1;$.hasGlobalWindow&&(zd=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var vo=zd,$l=.4,Zl="#333",ql="#ccc",L0="#eee",dh=Wu,ph=5e-5;function Cr(r){return r>ph||r<-ph}var Dr=[],oi=[],As=Ai(),Ls=Math.abs,$u=function(){function r(){}return r.prototype.getLocalTransform=function(t){return r.getLocalTransform(this,t)},r.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},r.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},r.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},r.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},r.prototype.needLocalTransform=function(){return Cr(this.rotation)||Cr(this.x)||Cr(this.y)||Cr(this.scaleX-1)||Cr(this.scaleY-1)||Cr(this.skewX)||Cr(this.skewY)},r.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),i=this.transform;if(!(e||t)){i&&(dh(i),this.invTransform=null);return}i=i||Ai(),e?this.getLocalTransform(i):dh(i),t&&(e?Li(i,t,i):Zm(i,t)),this.transform=i,this._resolveGlobalScaleRatio(i)},r.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(e!=null&&e!==1){this.getGlobalScale(Dr);var i=Dr[0]<0?-1:1,n=Dr[1]<0?-1:1,a=((Dr[0]-i)*e+i)/Dr[0]||0,o=((Dr[1]-n)*e+n)/Dr[1]||0;t[0]*=a,t[1]*=a,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||Ai(),Yu(this.invTransform,t)},r.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},r.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],n=Math.atan2(t[1],t[0]),a=Math.PI/2+n-Math.atan2(t[3],t[2]);i=Math.sqrt(i)*Math.cos(a),e=Math.sqrt(e),this.skewX=a,this.skewY=0,this.rotation=-n,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=i,this.originX=0,this.originY=0}},r.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||Ai(),Li(oi,t.invTransform,e),e=oi);var i=this.originX,n=this.originY;(i||n)&&(As[4]=i,As[5]=n,Li(oi,e,As),oi[4]-=i,oi[5]-=n,e=oi),this.setLocalTransform(e)}},r.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},r.prototype.transformCoordToLocal=function(t,e){var i=[t,e],n=this.invTransform;return n&&ue(i,i,n),i},r.prototype.transformCoordToGlobal=function(t,e){var i=[t,e],n=this.transform;return n&&ue(i,i,n),i},r.prototype.getLineScale=function(){var t=this.transform;return t&&Ls(t[0]-1)>1e-10&&Ls(t[3]-1)>1e-10?Math.sqrt(Ls(t[0]*t[3]-t[2]*t[1])):1},r.prototype.copyTransform=function(t){I0(this,t)},r.getLocalTransform=function(t,e){e=e||[];var i=t.originX||0,n=t.originY||0,a=t.scaleX,o=t.scaleY,s=t.anchorX,l=t.anchorY,u=t.rotation||0,f=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,v=t.skewY?Math.tan(-t.skewY):0;if(i||n||s||l){var d=i+s,g=n+l;e[4]=-d*a-c*g*o,e[5]=-g*o-v*d*a}else e[4]=e[5]=0;return e[0]=a,e[3]=o,e[1]=v*a,e[2]=c*o,u&&Uu(e,e,u),e[4]+=i+f,e[5]+=n+h,e},r.initDefaultProps=function(){var t=r.prototype;t.scaleX=t.scaleY=t.globalScaleRatio=1,t.x=t.y=t.originX=t.originY=t.skewX=t.skewY=t.rotation=t.anchorX=t.anchorY=0}(),r}(),zn=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function I0(r,t){for(var e=0;e<zn.length;e++){var i=zn[e];r[i]=t[i]}}var gh={};function Qt(r,t){t=t||Kr;var e=gh[t];e||(e=gh[t]=new jn(500));var i=e.get(r);return i==null&&(i=Fi.measureText(r,t).width,e.put(r,i)),i}function yh(r,t,e,i){var n=Qt(r,t),a=qu(t),o=yn(0,n,e),s=wi(0,a,i),l=new nt(o,s,n,a);return l}function Zu(r,t,e,i){var n=((r||"")+"").split(`
`),a=n.length;if(a===1)return yh(n[0],t,e,i);for(var o=new nt(0,0,0,0),s=0;s<n.length;s++){var l=yh(n[s],t,e,i);s===0?o.copy(l):o.union(l)}return o}function yn(r,t,e){return e==="right"?r-=t:e==="center"&&(r-=t/2),r}function wi(r,t,e){return e==="middle"?r-=t/2:e==="bottom"&&(r-=t),r}function qu(r){return Qt("国",r)}function yr(r,t){return typeof r=="string"?r.lastIndexOf("%")>=0?parseFloat(r)/100*t:parseFloat(r):r}function Hd(r,t,e){var i=t.position||"inside",n=t.distance!=null?t.distance:5,a=e.height,o=e.width,s=a/2,l=e.x,u=e.y,f="left",h="top";if(i instanceof Array)l+=yr(i[0],e.width),u+=yr(i[1],e.height),f=null,h=null;else switch(i){case"left":l-=n,u+=s,f="right",h="middle";break;case"right":l+=n+o,u+=s,h="middle";break;case"top":l+=o/2,u-=n,f="center",h="bottom";break;case"bottom":l+=o/2,u+=a+n,f="center";break;case"inside":l+=o/2,u+=s,f="center",h="middle";break;case"insideLeft":l+=n,u+=s,h="middle";break;case"insideRight":l+=o-n,u+=s,f="right",h="middle";break;case"insideTop":l+=o/2,u+=n,f="center";break;case"insideBottom":l+=o/2,u+=a-n,f="center",h="bottom";break;case"insideTopLeft":l+=n,u+=n;break;case"insideTopRight":l+=o-n,u+=n,f="right";break;case"insideBottomLeft":l+=n,u+=a-n,h="bottom";break;case"insideBottomRight":l+=o-n,u+=a-n,f="right",h="bottom";break}return r=r||{},r.x=l,r.y=u,r.align=f,r.verticalAlign=h,r}var Is="__zr_normal__",Ps=zn.concat(["ignore"]),P0=gr(zn,function(r,t){return r[t]=!0,r},{ignore:!1}),si={},R0=new nt(0,0,0,0),No=function(){function r(t){this.id=_d(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return r.prototype._init=function(t){this.attr(t)},r.prototype.drift=function(t,e,i){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0;break}var n=this.transform;n||(n=this.transform=[1,0,0,1,0,0]),n[4]+=t,n[5]+=e,this.decomposeTransform(),this.markRedraw()},r.prototype.beforeUpdate=function(){},r.prototype.afterUpdate=function(){},r.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},r.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var i=this.textConfig,n=i.local,a=e.innerTransformable,o=void 0,s=void 0,l=!1;a.parent=n?this:null;var u=!1;if(a.copyTransform(e),i.position!=null){var f=R0;i.layoutRect?f.copy(i.layoutRect):f.copy(this.getBoundingRect()),n||f.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(si,i,f):Hd(si,i,f),a.x=si.x,a.y=si.y,o=si.align,s=si.verticalAlign;var h=i.origin;if(h&&i.rotation!=null){var c=void 0,v=void 0;h==="center"?(c=f.width*.5,v=f.height*.5):(c=yr(h[0],f.width),v=yr(h[1],f.height)),u=!0,a.originX=-a.x+c+(n?0:f.x),a.originY=-a.y+v+(n?0:f.y)}}i.rotation!=null&&(a.rotation=i.rotation);var d=i.offset;d&&(a.x+=d[0],a.y+=d[1],u||(a.originX=-d[0],a.originY=-d[1]));var g=i.inside==null?typeof i.position=="string"&&i.position.indexOf("inside")>=0:i.inside,p=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),y=void 0,m=void 0,_=void 0;g&&this.canBeInsideText()?(y=i.insideFill,m=i.insideStroke,(y==null||y==="auto")&&(y=this.getInsideTextFill()),(m==null||m==="auto")&&(m=this.getInsideTextStroke(y),_=!0)):(y=i.outsideFill,m=i.outsideStroke,(y==null||y==="auto")&&(y=this.getOutsideFill()),(m==null||m==="auto")&&(m=this.getOutsideStroke(y),_=!0)),y=y||"#000",(y!==p.fill||m!==p.stroke||_!==p.autoStroke||o!==p.align||s!==p.verticalAlign)&&(l=!0,p.fill=y,p.stroke=m,p.autoStroke=_,p.align=o,p.verticalAlign=s,e.setDefaultTextStyle(p)),e.__dirty|=Kt,l&&e.dirtyStyle(!0)}},r.prototype.canBeInsideText=function(){return!0},r.prototype.getInsideTextFill=function(){return"#fff"},r.prototype.getInsideTextStroke=function(t){return"#000"},r.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?ql:Zl},r.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),i=typeof e=="string"&&We(e);i||(i=[255,255,255,1]);for(var n=i[3],a=this.__zr.isDarkMode(),o=0;o<3;o++)i[o]=i[o]*n+(a?0:255)*(1-n);return i[3]=1,Bo(i,"rgba")},r.prototype.traverse=function(t,e){},r.prototype.attrKV=function(t,e){t==="textConfig"?this.setTextConfig(e):t==="textContent"?this.setTextContent(e):t==="clipPath"?this.setClipPath(e):t==="extra"?(this.extra=this.extra||{},O(this.extra,e)):this[t]=e},r.prototype.hide=function(){this.ignore=!0,this.markRedraw()},r.prototype.show=function(){this.ignore=!1,this.markRedraw()},r.prototype.attr=function(t,e){if(typeof t=="string")this.attrKV(t,e);else if(W(t))for(var i=t,n=dt(i),a=0;a<n.length;a++){var o=n[a];this.attrKV(o,t[o])}return this.markRedraw(),this},r.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,i=0;i<this.animators.length;i++){var n=this.animators[i],a=n.__fromStateTransition;if(!(n.getLoop()||a&&a!==Is)){var o=n.targetName,s=o?e[o]:e;n.saveTo(s)}}},r.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,Ps)},r.prototype._savePrimaryToNormal=function(t,e,i){for(var n=0;n<i.length;n++){var a=i[n];t[a]!=null&&!(a in e)&&(e[a]=this[a])}},r.prototype.hasState=function(){return this.currentStates.length>0},r.prototype.getState=function(t){return this.states[t]},r.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},r.prototype.clearStates=function(t){this.useState(Is,!1,t)},r.prototype.useState=function(t,e,i,n){var a=t===Is,o=this.hasState();if(!(!o&&a)){var s=this.currentStates,l=this.stateTransition;if(!(lt(s,t)>=0&&(e||s.length===1))){var u;if(this.stateProxy&&!a&&(u=this.stateProxy(t)),u||(u=this.states&&this.states[t]),!u&&!a){Hu("State "+t+" not exists.");return}a||this.saveCurrentToNormalState(u);var f=!!(u&&u.hoverLayer||n);f&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,u,this._normalState,e,!i&&!this.__inHover&&l&&l.duration>0,l);var h=this._textContent,c=this._textGuide;return h&&h.useState(t,e,i,f),c&&c.useState(t,e,i,f),a?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!f&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~Kt),u}}},r.prototype.useStates=function(t,e,i){if(!t.length)this.clearStates();else{var n=[],a=this.currentStates,o=t.length,s=o===a.length;if(s){for(var l=0;l<o;l++)if(t[l]!==a[l]){s=!1;break}}if(s)return;for(var l=0;l<o;l++){var u=t[l],f=void 0;this.stateProxy&&(f=this.stateProxy(u,t)),f||(f=this.states[u]),f&&n.push(f)}var h=n[o-1],c=!!(h&&h.hoverLayer||i);c&&this._toggleHoverLayerFlag(!0);var v=this._mergeStates(n),d=this.stateTransition;this.saveCurrentToNormalState(v),this._applyStateObj(t.join(","),v,this._normalState,!1,!e&&!this.__inHover&&d&&d.duration>0,d);var g=this._textContent,p=this._textGuide;g&&g.useStates(t,e,c),p&&p.useStates(t,e,c),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!c&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~Kt)}},r.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},r.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},r.prototype.removeState=function(t){var e=lt(this.currentStates,t);if(e>=0){var i=this.currentStates.slice();i.splice(e,1),this.useStates(i)}},r.prototype.replaceState=function(t,e,i){var n=this.currentStates.slice(),a=lt(n,t),o=lt(n,e)>=0;a>=0?o?n.splice(a,1):n[a]=e:i&&!o&&n.push(e),this.useStates(n)},r.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},r.prototype._mergeStates=function(t){for(var e={},i,n=0;n<t.length;n++){var a=t[n];O(e,a),a.textConfig&&(i=i||{},O(i,a.textConfig))}return i&&(e.textConfig=i),e},r.prototype._applyStateObj=function(t,e,i,n,a,o){var s=!(e&&n);e&&e.textConfig?(this.textConfig=O({},n?this.textConfig:i.textConfig),O(this.textConfig,e.textConfig)):s&&i.textConfig&&(this.textConfig=i.textConfig);for(var l={},u=!1,f=0;f<Ps.length;f++){var h=Ps[f],c=a&&P0[h];e&&e[h]!=null?c?(u=!0,l[h]=e[h]):this[h]=e[h]:s&&i[h]!=null&&(c?(u=!0,l[h]=i[h]):this[h]=i[h])}if(!a)for(var f=0;f<this.animators.length;f++){var v=this.animators[f],d=v.targetName;v.getLoop()||v.__changeFinalValue(d?(e||i)[d]:e||i)}u&&this._transitionState(t,l,o)},r.prototype._attachComponent=function(t){if(!(t.__zr&&!t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},r.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},r.prototype.getClipPath=function(){return this._clipPath},r.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},r.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},r.prototype.getTextContent=function(){return this._textContent},r.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new $u,this._attachComponent(t),this._textContent=t,this.markRedraw())},r.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),O(this.textConfig,t),this.markRedraw()},r.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},r.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},r.prototype.getTextGuideLine=function(){return this._textGuide},r.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},r.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},r.prototype.markRedraw=function(){this.__dirty|=Kt;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},r.prototype.dirty=function(){this.markRedraw()},r.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,i=this._textGuide;e&&(e.__inHover=t),i&&(i.__inHover=t)},r.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.addAnimator(e[i]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},r.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.removeAnimator(e[i]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},r.prototype.animate=function(t,e,i){var n=t?this[t]:this,a=new Xu(n,e,i);return t&&(a.targetName=t),this.addAnimator(a,t),a},r.prototype.addAnimator=function(t,e){var i=this.__zr,n=this;t.during(function(){n.updateDuringAnimation(e)}).done(function(){var a=n.animators,o=lt(a,t);o>=0&&a.splice(o,1)}),this.animators.push(t),i&&i.animation.addAnimator(t),i&&i.wakeUp()},r.prototype.updateDuringAnimation=function(t){this.markRedraw()},r.prototype.stopAnimation=function(t,e){for(var i=this.animators,n=i.length,a=[],o=0;o<n;o++){var s=i[o];!t||t===s.scope?s.stop(e):a.push(s)}return this.animators=a,this},r.prototype.animateTo=function(t,e,i){Rs(this,t,e,i)},r.prototype.animateFrom=function(t,e,i){Rs(this,t,e,i,!0)},r.prototype._transitionState=function(t,e,i,n){for(var a=Rs(this,e,i,n),o=0;o<a.length;o++)a[o].__fromStateTransition=t},r.prototype.getBoundingRect=function(){return null},r.prototype.getPaintRect=function(){return null},r.initDefaultProps=function(){var t=r.prototype;t.type="element",t.name="",t.ignore=t.silent=t.isGroup=t.draggable=t.dragging=t.ignoreClip=t.__inHover=!1,t.__dirty=Kt;function e(i,n,a,o){Object.defineProperty(t,i,{get:function(){if(!this[n]){var l=this[n]=[];s(this,l)}return this[n]},set:function(l){this[a]=l[0],this[o]=l[1],this[n]=l,s(this,l)}});function s(l,u){Object.defineProperty(u,0,{get:function(){return l[a]},set:function(f){l[a]=f}}),Object.defineProperty(u,1,{get:function(){return l[o]},set:function(f){l[o]=f}})}}Object.defineProperty&&(e("position","_legacyPos","x","y"),e("scale","_legacyScale","scaleX","scaleY"),e("origin","_legacyOrigin","originX","originY"))}(),r}();Ee(No,Oe);Ee(No,$u);function Rs(r,t,e,i,n){e=e||{};var a=[];Gd(r,"",r,t,e,i,a,n);var o=a.length,s=!1,l=e.done,u=e.aborted,f=function(){s=!0,o--,o<=0&&(s?l&&l():u&&u())},h=function(){o--,o<=0&&(s?l&&l():u&&u())};o||l&&l(),a.length>0&&e.during&&a[0].during(function(d,g){e.during(g)});for(var c=0;c<a.length;c++){var v=a[c];f&&v.done(f),h&&v.aborted(h),e.force&&v.duration(e.duration),v.start(e.easing)}return a}function Es(r,t,e){for(var i=0;i<e;i++)r[i]=t[i]}function E0(r){return Xt(r[0])}function O0(r,t,e){if(Xt(t[e]))if(Xt(r[e])||(r[e]=[]),$t(t[e])){var i=t[e].length;r[e].length!==i&&(r[e]=new t[e].constructor(i),Es(r[e],t[e],i))}else{var n=t[e],a=r[e],o=n.length;if(E0(n))for(var s=n[0].length,l=0;l<o;l++)a[l]?Es(a[l],n[l],s):a[l]=Array.prototype.slice.call(n[l]);else Es(a,n,o);a.length=n.length}else r[e]=t[e]}function k0(r,t){return r===t||Xt(r)&&Xt(t)&&B0(r,t)}function B0(r,t){var e=r.length;if(e!==t.length)return!1;for(var i=0;i<e;i++)if(r[i]!==t[i])return!1;return!0}function Gd(r,t,e,i,n,a,o,s){for(var l=dt(i),u=n.duration,f=n.delay,h=n.additive,c=n.setToFinal,v=!W(a),d=r.animators,g=[],p=0;p<l.length;p++){var y=l[p],m=i[y];if(m!=null&&e[y]!=null&&(v||a[y]))if(W(m)&&!Xt(m)&&!Oo(m)){if(t){s||(e[y]=m,r.updateDuringAnimation(t));continue}Gd(r,y,e[y],m,n,a&&a[y],o,s)}else g.push(y);else s||(e[y]=m,r.updateDuringAnimation(t),g.push(y))}var _=g.length;if(!h&&_)for(var S=0;S<d.length;S++){var b=d[S];if(b.targetName===t){var w=b.stopTracks(g);if(w){var x=lt(d,b);d.splice(x,1)}}}if(n.force||(g=Mt(g,function(A){return!k0(i[A],e[A])}),_=g.length),_>0||n.force&&!o.length){var C=void 0,T=void 0,D=void 0;if(s){T={},c&&(C={});for(var S=0;S<_;S++){var y=g[S];T[y]=e[y],c?C[y]=i[y]:e[y]=i[y]}}else if(c){D={};for(var S=0;S<_;S++){var y=g[S];D[y]=Xa(e[y]),O0(e,i,y)}}var b=new Xu(e,!1,!1,h?Mt(d,function(L){return L.targetName===t}):null);b.targetName=t,n.scope&&(b.scope=n.scope),c&&C&&b.whenWithKeys(0,C,g),D&&b.whenWithKeys(0,D,g),b.whenWithKeys(u??500,s?T:i,g).delay(f||0),r.addAnimator(b,t),o.push(b)}}var Ot=function(r){N(t,r);function t(e){var i=r.call(this)||this;return i.isGroup=!0,i._children=[],i.attr(e),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.children=function(){return this._children.slice()},t.prototype.childAt=function(e){return this._children[e]},t.prototype.childOfName=function(e){for(var i=this._children,n=0;n<i.length;n++)if(i[n].name===e)return i[n]},t.prototype.childCount=function(){return this._children.length},t.prototype.add=function(e){return e&&e!==this&&e.parent!==this&&(this._children.push(e),this._doAdd(e)),this},t.prototype.addBefore=function(e,i){if(e&&e!==this&&e.parent!==this&&i&&i.parent===this){var n=this._children,a=n.indexOf(i);a>=0&&(n.splice(a,0,e),this._doAdd(e))}return this},t.prototype.replace=function(e,i){var n=lt(this._children,e);return n>=0&&this.replaceAt(i,n),this},t.prototype.replaceAt=function(e,i){var n=this._children,a=n[i];if(e&&e!==this&&e.parent!==this&&e!==a){n[i]=e,a.parent=null;var o=this.__zr;o&&a.removeSelfFromZr(o),this._doAdd(e)}return this},t.prototype._doAdd=function(e){e.parent&&e.parent.remove(e),e.parent=this;var i=this.__zr;i&&i!==e.__zr&&e.addSelfToZr(i),i&&i.refresh()},t.prototype.remove=function(e){var i=this.__zr,n=this._children,a=lt(n,e);return a<0?this:(n.splice(a,1),e.parent=null,i&&e.removeSelfFromZr(i),i&&i.refresh(),this)},t.prototype.removeAll=function(){for(var e=this._children,i=this.__zr,n=0;n<e.length;n++){var a=e[n];i&&a.removeSelfFromZr(i),a.parent=null}return e.length=0,this},t.prototype.eachChild=function(e,i){for(var n=this._children,a=0;a<n.length;a++){var o=n[a];e.call(i,o,a)}return this},t.prototype.traverse=function(e,i){for(var n=0;n<this._children.length;n++){var a=this._children[n],o=e.call(i,a);a.isGroup&&!o&&a.traverse(e,i)}return this},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.addSelfToZr(e)}},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.removeSelfFromZr(e)}},t.prototype.getBoundingRect=function(e){for(var i=new nt(0,0,0,0),n=e||this._children,a=[],o=null,s=0;s<n.length;s++){var l=n[s];if(!(l.ignore||l.invisible)){var u=l.getBoundingRect(),f=l.getLocalTransform(a);f?(nt.applyTransform(i,u,f),o=o||i.clone(),o.union(i)):(o=o||u.clone(),o.union(u))}}return o||i},t}(No);Ot.prototype.type="group";/*!
* ZRender, a high performance 2d drawing library.
*
* Copyright (c) 2013, Baidu Inc.
* All rights reserved.
*
* LICENSE
* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
*/var Ka={},Vd={};function N0(r){delete Vd[r]}function F0(r){if(!r)return!1;if(typeof r=="string")return ho(r,1)<$l;if(r.colorStops){for(var t=r.colorStops,e=0,i=t.length,n=0;n<i;n++)e+=ho(t[n].color,1);return e/=i,e<$l}return!1}var z0=function(){function r(t,e,i){var n=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,i=i||{},this.dom=e,this.id=t;var a=new n0,o=i.renderer||"canvas";Ka[o]||(o=dt(Ka)[0]),i.useDirtyRect=i.useDirtyRect==null?!1:i.useDirtyRect;var s=new Ka[o](e,a,i,t),l=i.ssr||s.ssrOnly;this.storage=a,this.painter=s;var u=!$.node&&!$.worker&&!l?new A0(s.getViewportRoot(),s.root):null,f=i.useCoarsePointer,h=f==null||f==="auto"?$.touchEventsSupported:!!f,c=44,v;h&&(v=K(i.pointerSize,c)),this.handler=new Md(a,s,u,s.root,v),this.animation=new w0({stage:{update:l?null:function(){return n._flush(!0)}}}),l||this.animation.start()}return r.prototype.add=function(t){this._disposed||!t||(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},r.prototype.remove=function(t){this._disposed||!t||(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},r.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},r.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=F0(t))},r.prototype.getBackgroundColor=function(){return this._backgroundColor},r.prototype.setDarkMode=function(t){this._darkMode=t},r.prototype.isDarkMode=function(){return this._darkMode},r.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},r.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},r.prototype.flush=function(){this._disposed||this._flush(!1)},r.prototype._flush=function(t){var e,i=Ti();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var n=Ti();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:n-i})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},r.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},r.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},r.prototype.refreshHover=function(){this._needsRefreshHover=!0},r.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.getType()==="canvas"&&this.painter.refreshHover())},r.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},r.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},r.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},r.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},r.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},r.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},r.prototype.on=function(t,e,i){return this._disposed||this.handler.on(t,e,i),this},r.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},r.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},r.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Ot&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},r.prototype.dispose=function(){this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,N0(this.id))},r}();function mh(r,t){var e=new z0(_d(),r,t);return Vd[e.id]=e,e}function H0(r,t){Ka[r]=t}var _h=1e-4,Wd=20;function G0(r){return r.replace(/^\s+|\s+$/g,"")}function Kl(r,t,e,i){var n=t[0],a=t[1],o=e[0],s=e[1],l=a-n,u=s-o;if(l===0)return u===0?o:(o+s)/2;if(i)if(l>0){if(r<=n)return o;if(r>=a)return s}else{if(r>=n)return o;if(r<=a)return s}else{if(r===n)return o;if(r===a)return s}return(r-n)/l*u+o}function ct(r,t){switch(r){case"center":case"middle":r="50%";break;case"left":case"top":r="0%";break;case"right":case"bottom":r="100%";break}return G(r)?G0(r).match(/%$/)?parseFloat(r)/100*t:parseFloat(r):r==null?NaN:+r}function wt(r,t,e){return t==null&&(t=10),t=Math.min(Math.max(0,t),Wd),r=(+r).toFixed(t),e?r:+r}function Ge(r){if(r=+r,isNaN(r))return 0;if(r>1e-14){for(var t=1,e=0;e<15;e++,t*=10)if(Math.round(r*t)/t===r)return e}return V0(r)}function V0(r){var t=r.toString().toLowerCase(),e=t.indexOf("e"),i=e>0?+t.slice(e+1):0,n=e>0?e:t.length,a=t.indexOf("."),o=a<0?0:n-1-a;return Math.max(0,o-i)}function W0(r,t){var e=Math.log,i=Math.LN10,n=Math.floor(e(r[1]-r[0])/i),a=Math.round(e(Math.abs(t[1]-t[0]))/i),o=Math.min(Math.max(-n+a,0),20);return isFinite(o)?o:20}function U0(r,t){var e=gr(r,function(v,d){return v+(isNaN(d)?0:d)},0);if(e===0)return[];for(var i=Math.pow(10,t),n=V(r,function(v){return(isNaN(v)?0:v)/e*i*100}),a=i*100,o=V(n,function(v){return Math.floor(v)}),s=gr(o,function(v,d){return v+d},0),l=V(n,function(v,d){return v-o[d]});s<a;){for(var u=Number.NEGATIVE_INFINITY,f=null,h=0,c=l.length;h<c;++h)l[h]>u&&(u=l[h],f=h);++o[f],l[f]=0,++s}return V(o,function(v){return v/i})}function Y0(r,t){var e=Math.max(Ge(r),Ge(t)),i=r+t;return e>Wd?i:wt(i,e)}function Ud(r){var t=Math.PI*2;return(r%t+t)%t}function co(r){return r>-_h&&r<_h}var X0=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function Xe(r){if(r instanceof Date)return r;if(G(r)){var t=X0.exec(r);if(!t)return new Date(NaN);if(t[8]){var e=+t[4]||0;return t[8].toUpperCase()!=="Z"&&(e-=+t[8].slice(0,3)),new Date(Date.UTC(+t[1],+(t[2]||1)-1,+t[3]||1,e,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0))}else return new Date(+t[1],+(t[2]||1)-1,+t[3]||1,+t[4]||0,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0)}else if(r==null)return new Date(NaN);return new Date(Math.round(r))}function $0(r){return Math.pow(10,Ku(r))}function Ku(r){if(r===0)return 0;var t=Math.floor(Math.log(r)/Math.LN10);return r/Math.pow(10,t)>=10&&t++,t}function Yd(r,t){var e=Ku(r),i=Math.pow(10,e),n=r/i,a;return n<1.5?a=1:n<2.5?a=2:n<4?a=3:n<7?a=5:a=10,r=a*i,e>=-20?+r.toFixed(e<0?-e:0):r}function po(r){var t=parseFloat(r);return t==r&&(t!==0||!G(r)||r.indexOf("x")<=0)?t:NaN}function Z0(r){return!isNaN(po(r))}function Xd(){return Math.round(Math.random()*9)}function $d(r,t){return t===0?r:$d(t,r%t)}function Sh(r,t){return r==null?t:t==null?r:r*t/$d(r,t)}function Wt(r){throw new Error(r)}function wh(r,t,e){return(t-r)*e+r}var Zd="series\0",q0="\0_ec_\0";function Nt(r){return r instanceof Array?r:r==null?[]:[r]}function Ql(r,t,e){if(r){r[t]=r[t]||{},r.emphasis=r.emphasis||{},r.emphasis[t]=r.emphasis[t]||{};for(var i=0,n=e.length;i<n;i++){var a=e[i];!r.emphasis[t].hasOwnProperty(a)&&r[t].hasOwnProperty(a)&&(r.emphasis[t][a]=r[t][a])}}}var bh=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function ta(r){return W(r)&&!F(r)&&!(r instanceof Date)?r.value:r}function K0(r){return W(r)&&!(r instanceof Array)}function Q0(r,t,e){var i=e==="normalMerge",n=e==="replaceMerge",a=e==="replaceAll";r=r||[],t=(t||[]).slice();var o=Q();M(t,function(l,u){if(!W(l)){t[u]=null;return}});var s=J0(r,o,e);return(i||n)&&j0(s,r,o,t),i&&t_(s,t),i||n?e_(s,t,n):a&&r_(s,t),i_(s),s}function J0(r,t,e){var i=[];if(e==="replaceAll")return i;for(var n=0;n<r.length;n++){var a=r[n];a&&a.id!=null&&t.set(a.id,n),i.push({existing:e==="replaceMerge"||Hn(a)?null:a,newOption:null,keyInfo:null,brandNew:null})}return i}function j0(r,t,e,i){M(i,function(n,a){if(!(!n||n.id==null)){var o=Dn(n.id),s=e.get(o);if(s!=null){var l=r[s];Ye(!l.newOption,'Duplicated option on id "'+o+'".'),l.newOption=n,l.existing=t[s],i[a]=null}}})}function t_(r,t){M(t,function(e,i){if(!(!e||e.name==null))for(var n=0;n<r.length;n++){var a=r[n].existing;if(!r[n].newOption&&a&&(a.id==null||e.id==null)&&!Hn(e)&&!Hn(a)&&qd("name",a,e)){r[n].newOption=e,t[i]=null;return}}})}function e_(r,t,e){M(t,function(i){if(i){for(var n,a=0;(n=r[a])&&(n.newOption||Hn(n.existing)||n.existing&&i.id!=null&&!qd("id",i,n.existing));)a++;n?(n.newOption=i,n.brandNew=e):r.push({newOption:i,brandNew:e,existing:null,keyInfo:null}),a++}})}function r_(r,t){M(t,function(e){r.push({newOption:e,brandNew:!0,existing:null,keyInfo:null})})}function i_(r){var t=Q();M(r,function(e){var i=e.existing;i&&t.set(i.id,e)}),M(r,function(e){var i=e.newOption;Ye(!i||i.id==null||!t.get(i.id)||t.get(i.id)===e,"id duplicates: "+(i&&i.id)),i&&i.id!=null&&t.set(i.id,e),!e.keyInfo&&(e.keyInfo={})}),M(r,function(e,i){var n=e.existing,a=e.newOption,o=e.keyInfo;if(W(a)){if(o.name=a.name!=null?Dn(a.name):n?n.name:Zd+i,n)o.id=Dn(n.id);else if(a.id!=null)o.id=Dn(a.id);else{var s=0;do o.id="\0"+o.name+"\0"+s++;while(t.get(o.id))}t.set(o.id,e)}})}function qd(r,t,e){var i=Pe(t[r],null),n=Pe(e[r],null);return i!=null&&n!=null&&i===n}function Dn(r){return Pe(r,"")}function Pe(r,t){return r==null?t:G(r)?r:vt(r)||Rl(r)?r+"":t}function Qu(r){var t=r.name;return!!(t&&t.indexOf(Zd))}function Hn(r){return r&&r.id!=null&&Dn(r.id).indexOf(q0)===0}function n_(r,t,e){M(r,function(i){var n=i.newOption;W(n)&&(i.keyInfo.mainType=t,i.keyInfo.subType=a_(t,n,i.existing,e))})}function a_(r,t,e,i){var n=t.type?t.type:e?e.subType:i.determineSubType(r,t);return n}function Jr(r,t){if(t.dataIndexInside!=null)return t.dataIndexInside;if(t.dataIndex!=null)return F(t.dataIndex)?V(t.dataIndex,function(e){return r.indexOfRawIndex(e)}):r.indexOfRawIndex(t.dataIndex);if(t.name!=null)return F(t.name)?V(t.name,function(e){return r.indexOfName(e)}):r.indexOfName(t.name)}function _t(){var r="__ec_inner_"+o_++;return function(t){return t[r]||(t[r]={})}}var o_=Xd();function Os(r,t,e){var i=Ju(t,e),n=i.mainTypeSpecified,a=i.queryOptionMap,o=i.others,s=o,l=e?e.defaultMainType:null;return!n&&l&&a.set(l,{}),a.each(function(u,f){var h=ea(r,f,u,{useDefault:l===f,enableAll:e&&e.enableAll!=null?e.enableAll:!0,enableNone:e&&e.enableNone!=null?e.enableNone:!0});s[f+"Models"]=h.models,s[f+"Model"]=h.models[0]}),s}function Ju(r,t){var e;if(G(r)){var i={};i[r+"Index"]=0,e=i}else e=r;var n=Q(),a={},o=!1;return M(e,function(s,l){if(l==="dataIndex"||l==="dataIndexInside"){a[l]=s;return}var u=l.match(/^(\w+)(Index|Id|Name)$/)||[],f=u[1],h=(u[2]||"").toLowerCase();if(!(!f||!h||t&&t.includeMainTypes&&lt(t.includeMainTypes,f)<0)){o=o||!!f;var c=n.get(f)||n.set(f,{});c[h]=s}}),{mainTypeSpecified:o,queryOptionMap:n,others:a}}var ye={useDefault:!0,enableAll:!1,enableNone:!1};function ea(r,t,e,i){i=i||ye;var n=e.index,a=e.id,o=e.name,s={models:null,specified:n!=null||a!=null||o!=null};if(!s.specified){var l=void 0;return s.models=i.useDefault&&(l=r.getComponent(t))?[l]:[],s}return n==="none"||n===!1?(Ye(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),s.models=[],s):(n==="all"&&(Ye(i.enableAll,'`"all"` is not a valid value on index option.'),n=a=o=null),s.models=r.queryComponents({mainType:t,index:n,id:a,name:o}),s)}function Kd(r,t,e){r.setAttribute?r.setAttribute(t,e):r[t]=e}function s_(r,t){return r.getAttribute?r.getAttribute(t):r[t]}function l_(r){return r==="auto"?$.domSupported?"html":"richText":r||"html"}function u_(r,t,e,i,n){var a=t==null||t==="auto";if(i==null)return i;if(vt(i)){var o=wh(e||0,i,n);return wt(o,a?Math.max(Ge(e||0),Ge(i)):t)}else{if(G(i))return n<1?e:i;for(var s=[],l=e,u=i,f=Math.max(l?l.length:0,u.length),h=0;h<f;++h){var c=r.getDimensionInfo(h);if(c&&c.type==="ordinal")s[h]=(n<1&&l?l:u)[h];else{var v=l&&l[h]?l[h]:0,d=u[h],o=wh(v,d,n);s[h]=wt(o,a?Math.max(Ge(v),Ge(d)):t)}}return s}}var f_=".",Mr="___EC__COMPONENT__CONTAINER___",Qd="___EC__EXTENDED_CLASS___";function Ie(r){var t={main:"",sub:""};if(r){var e=r.split(f_);t.main=e[0]||"",t.sub=e[1]||""}return t}function h_(r){Ye(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(r),'componentType "'+r+'" illegal')}function v_(r){return!!(r&&r[Qd])}function ju(r,t){r.$constructor=r,r.extend=function(e){var i=this,n;return c_(i)?n=function(a){N(o,a);function o(){return a.apply(this,arguments)||this}return o}(i):(n=function(){(e.$constructor||i).apply(this,arguments)},Sm(n,this)),O(n.prototype,e),n[Qd]=!0,n.extend=this.extend,n.superCall=g_,n.superApply=y_,n.superClass=i,n}}function c_(r){return q(r)&&/^class\s/.test(Function.prototype.toString.call(r))}function Jd(r,t){r.extend=t.extend}var d_=Math.round(Math.random()*10);function p_(r){var t=["__\0is_clz",d_++].join("_");r.prototype[t]=!0,r.isInstance=function(e){return!!(e&&e[t])}}function g_(r,t){for(var e=[],i=2;i<arguments.length;i++)e[i-2]=arguments[i];return this.superClass.prototype[t].apply(r,e)}function y_(r,t,e){return this.superClass.prototype[t].apply(r,e)}function Fo(r){var t={};r.registerClass=function(i){var n=i.type||i.prototype.type;if(n){h_(n),i.prototype.type=n;var a=Ie(n);if(!a.sub)t[a.main]=i;else if(a.sub!==Mr){var o=e(a);o[a.sub]=i}}return i},r.getClass=function(i,n,a){var o=t[i];if(o&&o[Mr]&&(o=n?o[n]:null),a&&!o)throw new Error(n?"Component "+i+"."+(n||"")+" is used but not imported.":i+".type should be specified.");return o},r.getClassesByMainType=function(i){var n=Ie(i),a=[],o=t[n.main];return o&&o[Mr]?M(o,function(s,l){l!==Mr&&a.push(s)}):a.push(o),a},r.hasClass=function(i){var n=Ie(i);return!!t[n.main]},r.getAllClassMainTypes=function(){var i=[];return M(t,function(n,a){i.push(a)}),i},r.hasSubTypes=function(i){var n=Ie(i),a=t[n.main];return a&&a[Mr]};function e(i){var n=t[i.main];return(!n||!n[Mr])&&(n=t[i.main]={},n[Mr]=!0),n}}function Gn(r,t){for(var e=0;e<r.length;e++)r[e][1]||(r[e][1]=r[e][0]);return t=t||!1,function(i,n,a){for(var o={},s=0;s<r.length;s++){var l=r[s][1];if(!(n&&lt(n,l)>=0||a&&lt(a,l)<0)){var u=i.getShallow(l,t);u!=null&&(o[r[s][0]]=u)}}return o}}var m_=[["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]],__=Gn(m_),S_=function(){function r(){}return r.prototype.getAreaStyle=function(t,e){return __(this,t,e)},r}(),Jl=new jn(50);function w_(r){if(typeof r=="string"){var t=Jl.get(r);return t&&t.image}else return r}function jd(r,t,e,i,n){if(r)if(typeof r=="string"){if(t&&t.__zrImageSrc===r||!e)return t;var a=Jl.get(r),o={hostEl:e,cb:i,cbPayload:n};return a?(t=a.image,!zo(t)&&a.pending.push(o)):(t=Fi.loadImage(r,xh,xh),t.__zrImageSrc=r,Jl.put(r,t.__cachedImgObj={image:t,pending:[o]})),t}else return r;else return t}function xh(){var r=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var t=0;t<r.pending.length;t++){var e=r.pending[t],i=e.cb;i&&i(this,e.cbPayload),e.hostEl.dirty()}r.pending.length=0}function zo(r){return r&&r.width&&r.height}var ks=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function b_(r,t,e,i,n,a){if(!e){r.text="",r.isTruncated=!1;return}var o=(t+"").split(`
`);a=tp(e,i,n,a);for(var s=!1,l={},u=0,f=o.length;u<f;u++)ep(l,o[u],a),o[u]=l.textLine,s=s||l.isTruncated;r.text=o.join(`
`),r.isTruncated=s}function tp(r,t,e,i){i=i||{};var n=O({},i);n.font=t,e=K(e,"..."),n.maxIterations=K(i.maxIterations,2);var a=n.minChar=K(i.minChar,0);n.cnCharWidth=Qt("国",t);var o=n.ascCharWidth=Qt("a",t);n.placeholder=K(i.placeholder,"");for(var s=r=Math.max(0,r-1),l=0;l<a&&s>=o;l++)s-=o;var u=Qt(e,t);return u>s&&(e="",u=0),s=r-u,n.ellipsis=e,n.ellipsisWidth=u,n.contentWidth=s,n.containerWidth=r,n}function ep(r,t,e){var i=e.containerWidth,n=e.font,a=e.contentWidth;if(!i){r.textLine="",r.isTruncated=!1;return}var o=Qt(t,n);if(o<=i){r.textLine=t,r.isTruncated=!1;return}for(var s=0;;s++){if(o<=a||s>=e.maxIterations){t+=e.ellipsis;break}var l=s===0?x_(t,a,e.ascCharWidth,e.cnCharWidth):o>0?Math.floor(t.length*a/o):0;t=t.substr(0,l),o=Qt(t,n)}t===""&&(t=e.placeholder),r.textLine=t,r.isTruncated=!0}function x_(r,t,e,i){for(var n=0,a=0,o=r.length;a<o&&n<t;a++){var s=r.charCodeAt(a);n+=0<=s&&s<=127?e:i}return a}function T_(r,t){r!=null&&(r+="");var e=t.overflow,i=t.padding,n=t.font,a=e==="truncate",o=qu(n),s=K(t.lineHeight,o),l=!!t.backgroundColor,u=t.lineOverflow==="truncate",f=!1,h=t.width,c;h!=null&&(e==="break"||e==="breakAll")?c=r?rp(r,t.font,h,e==="breakAll",0).lines:[]:c=r?r.split(`
`):[];var v=c.length*s,d=K(t.height,v);if(v>d&&u){var g=Math.floor(d/s);f=f||c.length>g,c=c.slice(0,g)}if(r&&a&&h!=null)for(var p=tp(h,n,t.ellipsis,{minChar:t.truncateMinChar,placeholder:t.placeholder}),y={},m=0;m<c.length;m++)ep(y,c[m],p),c[m]=y.textLine,f=f||y.isTruncated;for(var _=d,S=0,m=0;m<c.length;m++)S=Math.max(Qt(c[m],n),S);h==null&&(h=S);var b=S;return i&&(_+=i[0]+i[2],b+=i[1]+i[3],h+=i[1]+i[3]),l&&(b=h),{lines:c,height:d,outerWidth:b,outerHeight:_,lineHeight:s,calculatedLineHeight:o,contentWidth:S,contentHeight:v,width:h,isTruncated:f}}var C_=function(){function r(){}return r}(),Th=function(){function r(t){this.tokens=[],t&&(this.tokens=t)}return r}(),D_=function(){function r(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1}return r}();function M_(r,t){var e=new D_;if(r!=null&&(r+=""),!r)return e;for(var i=t.width,n=t.height,a=t.overflow,o=(a==="break"||a==="breakAll")&&i!=null?{width:i,accumWidth:0,breakAll:a==="breakAll"}:null,s=ks.lastIndex=0,l;(l=ks.exec(r))!=null;){var u=l.index;u>s&&Bs(e,r.substring(s,u),t,o),Bs(e,l[2],t,o,l[1]),s=ks.lastIndex}s<r.length&&Bs(e,r.substring(s,r.length),t,o);var f=[],h=0,c=0,v=t.padding,d=a==="truncate",g=t.lineOverflow==="truncate",p={};function y(H,Y,U){H.width=Y,H.lineHeight=U,h+=U,c=Math.max(c,Y)}t:for(var m=0;m<e.lines.length;m++){for(var _=e.lines[m],S=0,b=0,w=0;w<_.tokens.length;w++){var x=_.tokens[w],C=x.styleName&&t.rich[x.styleName]||{},T=x.textPadding=C.padding,D=T?T[1]+T[3]:0,A=x.font=C.font||t.font;x.contentHeight=qu(A);var L=K(C.height,x.contentHeight);if(x.innerHeight=L,T&&(L+=T[0]+T[2]),x.height=L,x.lineHeight=bn(C.lineHeight,t.lineHeight,L),x.align=C&&C.align||t.align,x.verticalAlign=C&&C.verticalAlign||"middle",g&&n!=null&&h+x.lineHeight>n){var P=e.lines.length;w>0?(_.tokens=_.tokens.slice(0,w),y(_,b,S),e.lines=e.lines.slice(0,m+1)):e.lines=e.lines.slice(0,m),e.isTruncated=e.isTruncated||e.lines.length<P;break t}var I=C.width,R=I==null||I==="auto";if(typeof I=="string"&&I.charAt(I.length-1)==="%")x.percentWidth=I,f.push(x),x.contentWidth=Qt(x.text,A);else{if(R){var E=C.backgroundColor,z=E&&E.image;z&&(z=w_(z),zo(z)&&(x.width=Math.max(x.width,z.width*L/z.height)))}var B=d&&i!=null?i-b:null;B!=null&&B<x.width?!R||B<D?(x.text="",x.width=x.contentWidth=0):(b_(p,x.text,B-D,A,t.ellipsis,{minChar:t.truncateMinChar}),x.text=p.text,e.isTruncated=e.isTruncated||p.isTruncated,x.width=x.contentWidth=Qt(x.text,A)):x.contentWidth=Qt(x.text,A)}x.width+=D,b+=x.width,C&&(S=Math.max(S,x.lineHeight))}y(_,b,S)}e.outerWidth=e.width=K(i,c),e.outerHeight=e.height=K(n,h),e.contentHeight=h,e.contentWidth=c,v&&(e.outerWidth+=v[1]+v[3],e.outerHeight+=v[0]+v[2]);for(var m=0;m<f.length;m++){var x=f[m],k=x.percentWidth;x.width=parseInt(k,10)/100*e.width}return e}function Bs(r,t,e,i,n){var a=t==="",o=n&&e.rich[n]||{},s=r.lines,l=o.font||e.font,u=!1,f,h;if(i){var c=o.padding,v=c?c[1]+c[3]:0;if(o.width!=null&&o.width!=="auto"){var d=yr(o.width,i.width)+v;s.length>0&&d+i.accumWidth>i.width&&(f=t.split(`
`),u=!0),i.accumWidth=d}else{var g=rp(t,l,i.width,i.breakAll,i.accumWidth);i.accumWidth=g.accumWidth+v,h=g.linesWidths,f=g.lines}}else f=t.split(`
`);for(var p=0;p<f.length;p++){var y=f[p],m=new C_;if(m.styleName=n,m.text=y,m.isLineHolder=!y&&!a,typeof o.width=="number"?m.width=o.width:m.width=h?h[p]:Qt(y,l),!p&&!u){var _=(s[s.length-1]||(s[0]=new Th)).tokens,S=_.length;S===1&&_[0].isLineHolder?_[0]=m:(y||!S||a)&&_.push(m)}else s.push(new Th([m]))}}function A_(r){var t=r.charCodeAt(0);return t>=32&&t<=591||t>=880&&t<=4351||t>=4608&&t<=5119||t>=7680&&t<=8303}var L_=gr(",&?/;] ".split(""),function(r,t){return r[t]=!0,r},{});function I_(r){return A_(r)?!!L_[r]:!0}function rp(r,t,e,i,n){for(var a=[],o=[],s="",l="",u=0,f=0,h=0;h<r.length;h++){var c=r.charAt(h);if(c===`
`){l&&(s+=l,f+=u),a.push(s),o.push(f),s="",l="",u=0,f=0;continue}var v=Qt(c,t),d=i?!1:!I_(c);if(a.length?f+v>e:n+f+v>e){f?(s||l)&&(d?(s||(s=l,l="",u=0,f=u),a.push(s),o.push(f-u),l+=c,u+=v,s="",f=u):(l&&(s+=l,l="",u=0),a.push(s),o.push(f),s=c,f=v)):d?(a.push(l),o.push(u),l=c,u=v):(a.push(c),o.push(v));continue}f+=v,d?(l+=c,u+=v):(l&&(s+=l,l="",u=0),s+=c)}return!a.length&&!s&&(s=r,l="",u=0),l&&(s+=l),s&&(a.push(s),o.push(f)),a.length===1&&(f+=n),{accumWidth:f,lines:a,linesWidths:o}}var jl="__zr_style_"+Math.round(Math.random()*10),$r={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},Ho={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};$r[jl]=!0;var Ch=["z","z2","invisible"],P_=["invisible"],ra=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype._init=function(e){for(var i=dt(e),n=0;n<i.length;n++){var a=i[n];a==="style"?this.useStyle(e[a]):r.prototype.attrKV.call(this,a,e[a])}this.style||this.useStyle({})},t.prototype.beforeBrush=function(){},t.prototype.afterBrush=function(){},t.prototype.innerBeforeBrush=function(){},t.prototype.innerAfterBrush=function(){},t.prototype.shouldBePainted=function(e,i,n,a){var o=this.transform;if(this.ignore||this.invisible||this.style.opacity===0||this.culling&&R_(this,e,i)||o&&!o[0]&&!o[3])return!1;if(n&&this.__clipPaths){for(var s=0;s<this.__clipPaths.length;++s)if(this.__clipPaths[s].isZeroArea())return!1}if(a&&this.parent)for(var l=this.parent;l;){if(l.ignore)return!1;l=l.parent}return!0},t.prototype.contain=function(e,i){return this.rectContain(e,i)},t.prototype.traverse=function(e,i){e.call(i,this)},t.prototype.rectContain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect();return a.contain(n[0],n[1])},t.prototype.getPaintRect=function(){var e=this._paintRect;if(!this._paintRect||this.__dirty){var i=this.transform,n=this.getBoundingRect(),a=this.style,o=a.shadowBlur||0,s=a.shadowOffsetX||0,l=a.shadowOffsetY||0;e=this._paintRect||(this._paintRect=new nt(0,0,0,0)),i?nt.applyTransform(e,n,i):e.copy(n),(o||s||l)&&(e.width+=o*2+Math.abs(s),e.height+=o*2+Math.abs(l),e.x=Math.min(e.x,e.x+s-o),e.y=Math.min(e.y,e.y+l-o));var u=this.dirtyRectTolerance;e.isZero()||(e.x=Math.floor(e.x-u),e.y=Math.floor(e.y-u),e.width=Math.ceil(e.width+1+u*2),e.height=Math.ceil(e.height+1+u*2))}return e},t.prototype.setPrevPaintRect=function(e){e?(this._prevPaintRect=this._prevPaintRect||new nt(0,0,0,0),this._prevPaintRect.copy(e)):this._prevPaintRect=null},t.prototype.getPrevPaintRect=function(){return this._prevPaintRect},t.prototype.animateStyle=function(e){return this.animate("style",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():this.markRedraw()},t.prototype.attrKV=function(e,i){e!=="style"?r.prototype.attrKV.call(this,e,i):this.style?this.setStyle(i):this.useStyle(i)},t.prototype.setStyle=function(e,i){return typeof e=="string"?this.style[e]=i:O(this.style,e),this.dirtyStyle(),this},t.prototype.dirtyStyle=function(e){e||this.markRedraw(),this.__dirty|=pn,this._rect&&(this._rect=null)},t.prototype.dirty=function(){this.dirtyStyle()},t.prototype.styleChanged=function(){return!!(this.__dirty&pn)},t.prototype.styleUpdated=function(){this.__dirty&=~pn},t.prototype.createStyle=function(e){return ko($r,e)},t.prototype.useStyle=function(e){e[jl]||(e=this.createStyle(e)),this.__inHover?this.__hoverStyle=e:this.style=e,this.dirtyStyle()},t.prototype.isStyleObject=function(e){return e[jl]},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var i=this._normalState;e.style&&!i.style&&(i.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,i,Ch)},t.prototype._applyStateObj=function(e,i,n,a,o,s){r.prototype._applyStateObj.call(this,e,i,n,a,o,s);var l=!(i&&a),u;if(i&&i.style?o?a?u=i.style:(u=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(u,i.style)):(u=this._mergeStyle(this.createStyle(),a?this.style:n.style),this._mergeStyle(u,i.style)):l&&(u=n.style),u)if(o){var f=this.style;if(this.style=this.createStyle(l?{}:f),l)for(var h=dt(f),c=0;c<h.length;c++){var v=h[c];v in u&&(u[v]=u[v],this.style[v]=f[v])}for(var d=dt(u),c=0;c<d.length;c++){var v=d[c];this.style[v]=this.style[v]}this._transitionState(e,{style:u},s,this.getAnimationStyleProps())}else this.useStyle(u);for(var g=this.__inHover?P_:Ch,c=0;c<g.length;c++){var v=g[c];i&&i[v]!=null?this[v]=i[v]:l&&n[v]!=null&&(this[v]=n[v])}},t.prototype._mergeStates=function(e){for(var i=r.prototype._mergeStates.call(this,e),n,a=0;a<e.length;a++){var o=e[a];o.style&&(n=n||{},this._mergeStyle(n,o.style))}return n&&(i.style=n),i},t.prototype._mergeStyle=function(e,i){return O(e,i),e},t.prototype.getAnimationStyleProps=function(){return Ho},t.initDefaultProps=function(){var e=t.prototype;e.type="displayable",e.invisible=!1,e.z=0,e.z2=0,e.zlevel=0,e.culling=!1,e.cursor="pointer",e.rectHover=!1,e.incremental=!1,e._rect=null,e.dirtyRectTolerance=0,e.__dirty=Kt|pn}(),t}(No),Ns=new nt(0,0,0,0),Fs=new nt(0,0,0,0);function R_(r,t,e){return Ns.copy(r.getBoundingRect()),r.transform&&Ns.applyTransform(r.transform),Fs.width=t,Fs.height=e,!Ns.intersect(Fs)}var oe=Math.min,se=Math.max,zs=Math.sin,Hs=Math.cos,Ar=Math.PI*2,da=zi(),pa=zi(),ga=zi();function Dh(r,t,e,i,n,a){n[0]=oe(r,e),n[1]=oe(t,i),a[0]=se(r,e),a[1]=se(t,i)}var Mh=[],Ah=[];function E_(r,t,e,i,n,a,o,s,l,u){var f=Rd,h=At,c=f(r,e,n,o,Mh);l[0]=1/0,l[1]=1/0,u[0]=-1/0,u[1]=-1/0;for(var v=0;v<c;v++){var d=h(r,e,n,o,Mh[v]);l[0]=oe(d,l[0]),u[0]=se(d,u[0])}c=f(t,i,a,s,Ah);for(var v=0;v<c;v++){var g=h(t,i,a,s,Ah[v]);l[1]=oe(g,l[1]),u[1]=se(g,u[1])}l[0]=oe(r,l[0]),u[0]=se(r,u[0]),l[0]=oe(o,l[0]),u[0]=se(o,u[0]),l[1]=oe(t,l[1]),u[1]=se(t,u[1]),l[1]=oe(s,l[1]),u[1]=se(s,u[1])}function O_(r,t,e,i,n,a,o,s){var l=Ed,u=Vt,f=se(oe(l(r,e,n),1),0),h=se(oe(l(t,i,a),1),0),c=u(r,e,n,f),v=u(t,i,a,h);o[0]=oe(r,n,c),o[1]=oe(t,a,v),s[0]=se(r,n,c),s[1]=se(t,a,v)}function k_(r,t,e,i,n,a,o,s,l){var u=bi,f=xi,h=Math.abs(n-a);if(h%Ar<1e-4&&h>1e-4){s[0]=r-e,s[1]=t-i,l[0]=r+e,l[1]=t+i;return}if(da[0]=Hs(n)*e+r,da[1]=zs(n)*i+t,pa[0]=Hs(a)*e+r,pa[1]=zs(a)*i+t,u(s,da,pa),f(l,da,pa),n=n%Ar,n<0&&(n=n+Ar),a=a%Ar,a<0&&(a=a+Ar),n>a&&!o?a+=Ar:n<a&&o&&(n+=Ar),o){var c=a;a=n,n=c}for(var v=0;v<a;v+=Math.PI/2)v>n&&(ga[0]=Hs(v)*e+r,ga[1]=zs(v)*i+t,u(s,ga,s),f(l,ga,l))}var rt={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Lr=[],Ir=[],xe=[],Ke=[],Te=[],Ce=[],Gs=Math.min,Vs=Math.max,Pr=Math.cos,Rr=Math.sin,Fe=Math.abs,tu=Math.PI,ar=tu*2,Ws=typeof Float32Array<"u",Ji=[];function Us(r){var t=Math.round(r/tu*1e8)/1e8;return t%2*tu}function ip(r,t){var e=Us(r[0]);e<0&&(e+=ar);var i=e-r[0],n=r[1];n+=i,!t&&n-e>=ar?n=e+ar:t&&e-n>=ar?n=e-ar:!t&&e>n?n=e+(ar-Us(e-n)):t&&e<n&&(n=e-(ar-Us(n-e))),r[0]=e,r[1]=n}var jr=function(){function r(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return r.prototype.increaseVersion=function(){this._version++},r.prototype.getVersion=function(){return this._version},r.prototype.setScale=function(t,e,i){i=i||0,i>0&&(this._ux=Fe(i/vo/t)||0,this._uy=Fe(i/vo/e)||0)},r.prototype.setDPR=function(t){this.dpr=t},r.prototype.setContext=function(t){this._ctx=t},r.prototype.getContext=function(){return this._ctx},r.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},r.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},r.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(rt.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},r.prototype.lineTo=function(t,e){var i=Fe(t-this._xi),n=Fe(e-this._yi),a=i>this._ux||n>this._uy;if(this.addData(rt.L,t,e),this._ctx&&a&&this._ctx.lineTo(t,e),a)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=i*i+n*n;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},r.prototype.bezierCurveTo=function(t,e,i,n,a,o){return this._drawPendingPt(),this.addData(rt.C,t,e,i,n,a,o),this._ctx&&this._ctx.bezierCurveTo(t,e,i,n,a,o),this._xi=a,this._yi=o,this},r.prototype.quadraticCurveTo=function(t,e,i,n){return this._drawPendingPt(),this.addData(rt.Q,t,e,i,n),this._ctx&&this._ctx.quadraticCurveTo(t,e,i,n),this._xi=i,this._yi=n,this},r.prototype.arc=function(t,e,i,n,a,o){this._drawPendingPt(),Ji[0]=n,Ji[1]=a,ip(Ji,o),n=Ji[0],a=Ji[1];var s=a-n;return this.addData(rt.A,t,e,i,i,n,s,0,o?0:1),this._ctx&&this._ctx.arc(t,e,i,n,a,o),this._xi=Pr(a)*i+t,this._yi=Rr(a)*i+e,this},r.prototype.arcTo=function(t,e,i,n,a){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,i,n,a),this},r.prototype.rect=function(t,e,i,n){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,i,n),this.addData(rt.R,t,e,i,n),this},r.prototype.closePath=function(){this._drawPendingPt(),this.addData(rt.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&t.closePath(),this._xi=e,this._yi=i,this},r.prototype.fill=function(t){t&&t.fill(),this.toStatic()},r.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},r.prototype.len=function(){return this._len},r.prototype.setData=function(t){var e=t.length;!(this.data&&this.data.length===e)&&Ws&&(this.data=new Float32Array(e));for(var i=0;i<e;i++)this.data[i]=t[i];this._len=e},r.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,i=0,n=this._len,a=0;a<e;a++)i+=t[a].len();Ws&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(var a=0;a<e;a++)for(var o=t[a].data,s=0;s<o.length;s++)this.data[n++]=o[s];this._len=n},r.prototype.addData=function(t,e,i,n,a,o,s,l,u){if(this._saveData){var f=this.data;this._len+arguments.length>f.length&&(this._expandData(),f=this.data);for(var h=0;h<arguments.length;h++)f[this._len++]=arguments[h]}},r.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},r.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},r.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,Ws&&this._len>11&&(this.data=new Float32Array(t)))}},r.prototype.getBoundingRect=function(){xe[0]=xe[1]=Te[0]=Te[1]=Number.MAX_VALUE,Ke[0]=Ke[1]=Ce[0]=Ce[1]=-Number.MAX_VALUE;var t=this.data,e=0,i=0,n=0,a=0,o;for(o=0;o<this._len;){var s=t[o++],l=o===1;switch(l&&(e=t[o],i=t[o+1],n=e,a=i),s){case rt.M:e=n=t[o++],i=a=t[o++],Te[0]=n,Te[1]=a,Ce[0]=n,Ce[1]=a;break;case rt.L:Dh(e,i,t[o],t[o+1],Te,Ce),e=t[o++],i=t[o++];break;case rt.C:E_(e,i,t[o++],t[o++],t[o++],t[o++],t[o],t[o+1],Te,Ce),e=t[o++],i=t[o++];break;case rt.Q:O_(e,i,t[o++],t[o++],t[o],t[o+1],Te,Ce),e=t[o++],i=t[o++];break;case rt.A:var u=t[o++],f=t[o++],h=t[o++],c=t[o++],v=t[o++],d=t[o++]+v;o+=1;var g=!t[o++];l&&(n=Pr(v)*h+u,a=Rr(v)*c+f),k_(u,f,h,c,v,d,g,Te,Ce),e=Pr(d)*h+u,i=Rr(d)*c+f;break;case rt.R:n=e=t[o++],a=i=t[o++];var p=t[o++],y=t[o++];Dh(n,a,n+p,a+y,Te,Ce);break;case rt.Z:e=n,i=a;break}bi(xe,xe,Te),xi(Ke,Ke,Ce)}return o===0&&(xe[0]=xe[1]=Ke[0]=Ke[1]=0),new nt(xe[0],xe[1],Ke[0]-xe[0],Ke[1]-xe[1])},r.prototype._calculateLength=function(){var t=this.data,e=this._len,i=this._ux,n=this._uy,a=0,o=0,s=0,l=0;this._pathSegLen||(this._pathSegLen=[]);for(var u=this._pathSegLen,f=0,h=0,c=0;c<e;){var v=t[c++],d=c===1;d&&(a=t[c],o=t[c+1],s=a,l=o);var g=-1;switch(v){case rt.M:a=s=t[c++],o=l=t[c++];break;case rt.L:{var p=t[c++],y=t[c++],m=p-a,_=y-o;(Fe(m)>i||Fe(_)>n||c===e-1)&&(g=Math.sqrt(m*m+_*_),a=p,o=y);break}case rt.C:{var S=t[c++],b=t[c++],p=t[c++],y=t[c++],w=t[c++],x=t[c++];g=o0(a,o,S,b,p,y,w,x,10),a=w,o=x;break}case rt.Q:{var S=t[c++],b=t[c++],p=t[c++],y=t[c++];g=u0(a,o,S,b,p,y,10),a=p,o=y;break}case rt.A:var C=t[c++],T=t[c++],D=t[c++],A=t[c++],L=t[c++],P=t[c++],I=P+L;c+=1,d&&(s=Pr(L)*D+C,l=Rr(L)*A+T),g=Vs(D,A)*Gs(ar,Math.abs(P)),a=Pr(I)*D+C,o=Rr(I)*A+T;break;case rt.R:{s=a=t[c++],l=o=t[c++];var R=t[c++],E=t[c++];g=R*2+E*2;break}case rt.Z:{var m=s-a,_=l-o;g=Math.sqrt(m*m+_*_),a=s,o=l;break}}g>=0&&(u[h++]=g,f+=g)}return this._pathLen=f,f},r.prototype.rebuildPath=function(t,e){var i=this.data,n=this._ux,a=this._uy,o=this._len,s,l,u,f,h,c,v=e<1,d,g,p=0,y=0,m,_=0,S,b;if(!(v&&(this._pathSegLen||this._calculateLength(),d=this._pathSegLen,g=this._pathLen,m=e*g,!m)))t:for(var w=0;w<o;){var x=i[w++],C=w===1;switch(C&&(u=i[w],f=i[w+1],s=u,l=f),x!==rt.L&&_>0&&(t.lineTo(S,b),_=0),x){case rt.M:s=u=i[w++],l=f=i[w++],t.moveTo(u,f);break;case rt.L:{h=i[w++],c=i[w++];var T=Fe(h-u),D=Fe(c-f);if(T>n||D>a){if(v){var A=d[y++];if(p+A>m){var L=(m-p)/A;t.lineTo(u*(1-L)+h*L,f*(1-L)+c*L);break t}p+=A}t.lineTo(h,c),u=h,f=c,_=0}else{var P=T*T+D*D;P>_&&(S=h,b=c,_=P)}break}case rt.C:{var I=i[w++],R=i[w++],E=i[w++],z=i[w++],B=i[w++],k=i[w++];if(v){var A=d[y++];if(p+A>m){var L=(m-p)/A;uo(u,I,E,B,L,Lr),uo(f,R,z,k,L,Ir),t.bezierCurveTo(Lr[1],Ir[1],Lr[2],Ir[2],Lr[3],Ir[3]);break t}p+=A}t.bezierCurveTo(I,R,E,z,B,k),u=B,f=k;break}case rt.Q:{var I=i[w++],R=i[w++],E=i[w++],z=i[w++];if(v){var A=d[y++];if(p+A>m){var L=(m-p)/A;fo(u,I,E,L,Lr),fo(f,R,z,L,Ir),t.quadraticCurveTo(Lr[1],Ir[1],Lr[2],Ir[2]);break t}p+=A}t.quadraticCurveTo(I,R,E,z),u=E,f=z;break}case rt.A:var H=i[w++],Y=i[w++],U=i[w++],tt=i[w++],et=i[w++],ut=i[w++],Ft=i[w++],Jt=!i[w++],qt=U>tt?U:tt,Tt=Fe(U-tt)>.001,yt=et+ut,X=!1;if(v){var A=d[y++];p+A>m&&(yt=et+ut*(m-p)/A,X=!0),p+=A}if(Tt&&t.ellipse?t.ellipse(H,Y,U,tt,Ft,et,yt,Jt):t.arc(H,Y,qt,et,yt,Jt),X)break t;C&&(s=Pr(et)*U+H,l=Rr(et)*tt+Y),u=Pr(yt)*U+H,f=Rr(yt)*tt+Y;break;case rt.R:s=u=i[w],l=f=i[w+1],h=i[w++],c=i[w++];var J=i[w++],be=i[w++];if(v){var A=d[y++];if(p+A>m){var bt=m-p;t.moveTo(h,c),t.lineTo(h+Gs(bt,J),c),bt-=J,bt>0&&t.lineTo(h+J,c+Gs(bt,be)),bt-=be,bt>0&&t.lineTo(h+Vs(J-bt,0),c+be),bt-=J,bt>0&&t.lineTo(h,c+Vs(be-bt,0));break t}p+=A}t.rect(h,c,J,be);break;case rt.Z:if(v){var A=d[y++];if(p+A>m){var L=(m-p)/A;t.lineTo(u*(1-L)+s*L,f*(1-L)+l*L);break t}p+=A}t.closePath(),u=s,f=l}}},r.prototype.clone=function(){var t=new r,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},r.CMD=rt,r.initDefaultProps=function(){var t=r.prototype;t._saveData=!0,t._ux=0,t._uy=0,t._pendingPtDist=0,t._version=0}(),r}();function li(r,t,e,i,n,a,o){if(n===0)return!1;var s=n,l=0,u=r;if(o>t+s&&o>i+s||o<t-s&&o<i-s||a>r+s&&a>e+s||a<r-s&&a<e-s)return!1;if(r!==e)l=(t-i)/(r-e),u=(r*i-e*t)/(r-e);else return Math.abs(a-r)<=s/2;var f=l*a-o+u,h=f*f/(l*l+1);return h<=s/2*s/2}function B_(r,t,e,i,n,a,o,s,l,u,f){if(l===0)return!1;var h=l;if(f>t+h&&f>i+h&&f>a+h&&f>s+h||f<t-h&&f<i-h&&f<a-h&&f<s-h||u>r+h&&u>e+h&&u>n+h&&u>o+h||u<r-h&&u<e-h&&u<n-h&&u<o-h)return!1;var c=a0(r,t,e,i,n,a,o,s,u,f);return c<=h/2}function N_(r,t,e,i,n,a,o,s,l){if(o===0)return!1;var u=o;if(l>t+u&&l>i+u&&l>a+u||l<t-u&&l<i-u&&l<a-u||s>r+u&&s>e+u&&s>n+u||s<r-u&&s<e-u&&s<n-u)return!1;var f=l0(r,t,e,i,n,a,s,l);return f<=u/2}var Lh=Math.PI*2;function ya(r){return r%=Lh,r<0&&(r+=Lh),r}var ji=Math.PI*2;function F_(r,t,e,i,n,a,o,s,l){if(o===0)return!1;var u=o;s-=r,l-=t;var f=Math.sqrt(s*s+l*l);if(f-u>e||f+u<e)return!1;if(Math.abs(i-n)%ji<1e-4)return!0;if(a){var h=i;i=ya(n),n=ya(h)}else i=ya(i),n=ya(n);i>n&&(n+=ji);var c=Math.atan2(l,s);return c<0&&(c+=ji),c>=i&&c<=n||c+ji>=i&&c+ji<=n}function Er(r,t,e,i,n,a){if(a>t&&a>i||a<t&&a<i||i===t)return 0;var o=(a-t)/(i-t),s=i<t?1:-1;(o===1||o===0)&&(s=i<t?.5:-.5);var l=o*(e-r)+r;return l===n?1/0:l>n?s:0}var Qe=jr.CMD,Or=Math.PI*2,z_=1e-4;function H_(r,t){return Math.abs(r-t)<z_}var kt=[-1,-1,-1],ne=[-1,-1];function G_(){var r=ne[0];ne[0]=ne[1],ne[1]=r}function V_(r,t,e,i,n,a,o,s,l,u){if(u>t&&u>i&&u>a&&u>s||u<t&&u<i&&u<a&&u<s)return 0;var f=lo(t,i,a,s,u,kt);if(f===0)return 0;for(var h=0,c=-1,v=void 0,d=void 0,g=0;g<f;g++){var p=kt[g],y=p===0||p===1?.5:1,m=At(r,e,n,o,p);m<l||(c<0&&(c=Rd(t,i,a,s,ne),ne[1]<ne[0]&&c>1&&G_(),v=At(t,i,a,s,ne[0]),c>1&&(d=At(t,i,a,s,ne[1]))),c===2?p<ne[0]?h+=v<t?y:-y:p<ne[1]?h+=d<v?y:-y:h+=s<d?y:-y:p<ne[0]?h+=v<t?y:-y:h+=s<v?y:-y)}return h}function W_(r,t,e,i,n,a,o,s){if(s>t&&s>i&&s>a||s<t&&s<i&&s<a)return 0;var l=s0(t,i,a,s,kt);if(l===0)return 0;var u=Ed(t,i,a);if(u>=0&&u<=1){for(var f=0,h=Vt(t,i,a,u),c=0;c<l;c++){var v=kt[c]===0||kt[c]===1?.5:1,d=Vt(r,e,n,kt[c]);d<o||(kt[c]<u?f+=h<t?v:-v:f+=a<h?v:-v)}return f}else{var v=kt[0]===0||kt[0]===1?.5:1,d=Vt(r,e,n,kt[0]);return d<o?0:a<t?v:-v}}function U_(r,t,e,i,n,a,o,s){if(s-=t,s>e||s<-e)return 0;var l=Math.sqrt(e*e-s*s);kt[0]=-l,kt[1]=l;var u=Math.abs(i-n);if(u<1e-4)return 0;if(u>=Or-1e-4){i=0,n=Or;var f=a?1:-1;return o>=kt[0]+r&&o<=kt[1]+r?f:0}if(i>n){var h=i;i=n,n=h}i<0&&(i+=Or,n+=Or);for(var c=0,v=0;v<2;v++){var d=kt[v];if(d+r>o){var g=Math.atan2(s,d),f=a?1:-1;g<0&&(g=Or+g),(g>=i&&g<=n||g+Or>=i&&g+Or<=n)&&(g>Math.PI/2&&g<Math.PI*1.5&&(f=-f),c+=f)}}return c}function np(r,t,e,i,n){for(var a=r.data,o=r.len(),s=0,l=0,u=0,f=0,h=0,c,v,d=0;d<o;){var g=a[d++],p=d===1;switch(g===Qe.M&&d>1&&(e||(s+=Er(l,u,f,h,i,n))),p&&(l=a[d],u=a[d+1],f=l,h=u),g){case Qe.M:f=a[d++],h=a[d++],l=f,u=h;break;case Qe.L:if(e){if(li(l,u,a[d],a[d+1],t,i,n))return!0}else s+=Er(l,u,a[d],a[d+1],i,n)||0;l=a[d++],u=a[d++];break;case Qe.C:if(e){if(B_(l,u,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],t,i,n))return!0}else s+=V_(l,u,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],i,n)||0;l=a[d++],u=a[d++];break;case Qe.Q:if(e){if(N_(l,u,a[d++],a[d++],a[d],a[d+1],t,i,n))return!0}else s+=W_(l,u,a[d++],a[d++],a[d],a[d+1],i,n)||0;l=a[d++],u=a[d++];break;case Qe.A:var y=a[d++],m=a[d++],_=a[d++],S=a[d++],b=a[d++],w=a[d++];d+=1;var x=!!(1-a[d++]);c=Math.cos(b)*_+y,v=Math.sin(b)*S+m,p?(f=c,h=v):s+=Er(l,u,c,v,i,n);var C=(i-y)*S/_+y;if(e){if(F_(y,m,S,b,b+w,x,t,C,n))return!0}else s+=U_(y,m,S,b,b+w,x,C,n);l=Math.cos(b+w)*_+y,u=Math.sin(b+w)*S+m;break;case Qe.R:f=l=a[d++],h=u=a[d++];var T=a[d++],D=a[d++];if(c=f+T,v=h+D,e){if(li(f,h,c,h,t,i,n)||li(c,h,c,v,t,i,n)||li(c,v,f,v,t,i,n)||li(f,v,f,h,t,i,n))return!0}else s+=Er(c,h,c,v,i,n),s+=Er(f,v,f,h,i,n);break;case Qe.Z:if(e){if(li(l,u,f,h,t,i,n))return!0}else s+=Er(l,u,f,h,i,n);l=f,u=h;break}}return!e&&!H_(u,h)&&(s+=Er(l,u,f,h,i,n)||0),s!==0}function Y_(r,t,e){return np(r,0,!1,t,e)}function X_(r,t,e,i){return np(r,t,!0,e,i)}var ap=at({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},$r),$_={style:at({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},Ho.style)},Ys=zn.concat(["invisible","culling","z","z2","zlevel","parent"]),ft=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.update=function(){var e=this;r.prototype.update.call(this);var i=this.style;if(i.decal){var n=this._decalEl=this._decalEl||new t;n.buildPath===t.prototype.buildPath&&(n.buildPath=function(l){e.buildPath(l,e.shape)}),n.silent=!0;var a=n.style;for(var o in i)a[o]!==i[o]&&(a[o]=i[o]);a.fill=i.fill?i.decal:null,a.decal=null,a.shadowColor=null,i.strokeFirst&&(a.stroke=null);for(var s=0;s<Ys.length;++s)n[Ys[s]]=this[Ys[s]];n.__dirty|=Kt}else this._decalEl&&(this._decalEl=null)},t.prototype.getDecalElement=function(){return this._decalEl},t.prototype._init=function(e){var i=dt(e);this.shape=this.getDefaultShape();var n=this.getDefaultStyle();n&&this.useStyle(n);for(var a=0;a<i.length;a++){var o=i[a],s=e[o];o==="style"?this.style?O(this.style,s):this.useStyle(s):o==="shape"?O(this.shape,s):r.prototype.attrKV.call(this,o,s)}this.style||this.useStyle({})},t.prototype.getDefaultStyle=function(){return null},t.prototype.getDefaultShape=function(){return{}},t.prototype.canBeInsideText=function(){return this.hasFill()},t.prototype.getInsideTextFill=function(){var e=this.style.fill;if(e!=="none"){if(G(e)){var i=ho(e,0);return i>.5?Zl:i>.2?L0:ql}else if(e)return ql}return Zl},t.prototype.getInsideTextStroke=function(e){var i=this.style.fill;if(G(i)){var n=this.__zr,a=!!(n&&n.isDarkMode()),o=ho(e,0)<$l;if(a===o)return i}},t.prototype.buildPath=function(e,i,n){},t.prototype.pathUpdated=function(){this.__dirty&=~Si},t.prototype.getUpdatedPathProxy=function(e){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,e),this.path},t.prototype.createPathProxy=function(){this.path=new jr(!1)},t.prototype.hasStroke=function(){var e=this.style,i=e.stroke;return!(i==null||i==="none"||!(e.lineWidth>0))},t.prototype.hasFill=function(){var e=this.style,i=e.fill;return i!=null&&i!=="none"},t.prototype.getBoundingRect=function(){var e=this._rect,i=this.style,n=!e;if(n){var a=!1;this.path||(a=!0,this.createPathProxy());var o=this.path;(a||this.__dirty&Si)&&(o.beginPath(),this.buildPath(o,this.shape,!1),this.pathUpdated()),e=o.getBoundingRect()}if(this._rect=e,this.hasStroke()&&this.path&&this.path.len()>0){var s=this._rectStroke||(this._rectStroke=e.clone());if(this.__dirty||n){s.copy(e);var l=i.strokeNoScale?this.getLineScale():1,u=i.lineWidth;if(!this.hasFill()){var f=this.strokeContainThreshold;u=Math.max(u,f??4)}l>1e-10&&(s.width+=u/l,s.height+=u/l,s.x-=u/l/2,s.y-=u/l/2)}return s}return e},t.prototype.contain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect(),o=this.style;if(e=n[0],i=n[1],a.contain(e,i)){var s=this.path;if(this.hasStroke()){var l=o.lineWidth,u=o.strokeNoScale?this.getLineScale():1;if(u>1e-10&&(this.hasFill()||(l=Math.max(l,this.strokeContainThreshold)),X_(s,l/u,e,i)))return!0}if(this.hasFill())return Y_(s,e,i)}return!1},t.prototype.dirtyShape=function(){this.__dirty|=Si,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},t.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},t.prototype.animateShape=function(e){return this.animate("shape",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():e==="shape"?this.dirtyShape():this.markRedraw()},t.prototype.attrKV=function(e,i){e==="shape"?this.setShape(i):r.prototype.attrKV.call(this,e,i)},t.prototype.setShape=function(e,i){var n=this.shape;return n||(n=this.shape={}),typeof e=="string"?n[e]=i:O(n,e),this.dirtyShape(),this},t.prototype.shapeChanged=function(){return!!(this.__dirty&Si)},t.prototype.createStyle=function(e){return ko(ap,e)},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var i=this._normalState;e.shape&&!i.shape&&(i.shape=O({},this.shape))},t.prototype._applyStateObj=function(e,i,n,a,o,s){r.prototype._applyStateObj.call(this,e,i,n,a,o,s);var l=!(i&&a),u;if(i&&i.shape?o?a?u=i.shape:(u=O({},n.shape),O(u,i.shape)):(u=O({},a?this.shape:n.shape),O(u,i.shape)):l&&(u=n.shape),u)if(o){this.shape=O({},this.shape);for(var f={},h=dt(u),c=0;c<h.length;c++){var v=h[c];typeof u[v]=="object"?this.shape[v]=u[v]:f[v]=u[v]}this._transitionState(e,{shape:f},s)}else this.shape=u,this.dirtyShape()},t.prototype._mergeStates=function(e){for(var i=r.prototype._mergeStates.call(this,e),n,a=0;a<e.length;a++){var o=e[a];o.shape&&(n=n||{},this._mergeStyle(n,o.shape))}return n&&(i.shape=n),i},t.prototype.getAnimationStyleProps=function(){return $_},t.prototype.isZeroArea=function(){return!1},t.extend=function(e){var i=function(a){N(o,a);function o(s){var l=a.call(this,s)||this;return e.init&&e.init.call(l,s),l}return o.prototype.getDefaultStyle=function(){return j(e.style)},o.prototype.getDefaultShape=function(){return j(e.shape)},o}(t);for(var n in e)typeof e[n]=="function"&&(i.prototype[n]=e[n]);return i},t.initDefaultProps=function(){var e=t.prototype;e.type="path",e.strokeContainThreshold=5,e.segmentIgnoreThreshold=0,e.subPixelOptimize=!1,e.autoBatch=!1,e.__dirty=Kt|pn|Si}(),t}(ra),Z_=at({strokeFirst:!0,font:Kr,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},ap),go=function(r){N(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.hasStroke=function(){var e=this.style,i=e.stroke;return i!=null&&i!=="none"&&e.lineWidth>0},t.prototype.hasFill=function(){var e=this.style,i=e.fill;return i!=null&&i!=="none"},t.prototype.createStyle=function(e){return ko(Z_,e)},t.prototype.setBoundingRect=function(e){this._rect=e},t.prototype.getBoundingRect=function(){var e=this.style;if(!this._rect){var i=e.text;i!=null?i+="":i="";var n=Zu(i,e.font,e.textAlign,e.textBaseline);if(n.x+=e.x||0,n.y+=e.y||0,this.hasStroke()){var a=e.lineWidth;n.x-=a/2,n.y-=a/2,n.width+=a,n.height+=a}this._rect=n}return this._rect},t.initDefaultProps=function(){var e=t.prototype;e.dirtyRectTolerance=10}(),t}(ra);go.prototype.type="tspan";var q_=at({x:0,y:0},$r),K_={style:at({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},Ho.style)};function Q_(r){return!!(r&&typeof r!="string"&&r.width&&r.height)}var Sr=function(r){N(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.createStyle=function(e){return ko(q_,e)},t.prototype._getSize=function(e){var i=this.style,n=i[e];if(n!=null)return n;var a=Q_(i.image)?i.image:this.__image;if(!a)return 0;var o=e==="width"?"height":"width",s=i[o];return s==null?a[e]:a[e]/a[o]*s},t.prototype.getWidth=function(){return this._getSize("width")},t.prototype.getHeight=function(){return this._getSize("height")},t.prototype.getAnimationStyleProps=function(){return K_},t.prototype.getBoundingRect=function(){var e=this.style;return this._rect||(this._rect=new nt(e.x||0,e.y||0,this.getWidth(),this.getHeight())),this._rect},t}(ra);Sr.prototype.type="image";function J_(r,t){var e=t.x,i=t.y,n=t.width,a=t.height,o=t.r,s,l,u,f;n<0&&(e=e+n,n=-n),a<0&&(i=i+a,a=-a),typeof o=="number"?s=l=u=f=o:o instanceof Array?o.length===1?s=l=u=f=o[0]:o.length===2?(s=u=o[0],l=f=o[1]):o.length===3?(s=o[0],l=f=o[1],u=o[2]):(s=o[0],l=o[1],u=o[2],f=o[3]):s=l=u=f=0;var h;s+l>n&&(h=s+l,s*=n/h,l*=n/h),u+f>n&&(h=u+f,u*=n/h,f*=n/h),l+u>a&&(h=l+u,l*=a/h,u*=a/h),s+f>a&&(h=s+f,s*=a/h,f*=a/h),r.moveTo(e+s,i),r.lineTo(e+n-l,i),l!==0&&r.arc(e+n-l,i+l,l,-Math.PI/2,0),r.lineTo(e+n,i+a-u),u!==0&&r.arc(e+n-u,i+a-u,u,0,Math.PI/2),r.lineTo(e+f,i+a),f!==0&&r.arc(e+f,i+a-f,f,Math.PI/2,Math.PI),r.lineTo(e,i+s),s!==0&&r.arc(e+s,i+s,s,Math.PI,Math.PI*1.5)}var Ci=Math.round;function op(r,t,e){if(t){var i=t.x1,n=t.x2,a=t.y1,o=t.y2;r.x1=i,r.x2=n,r.y1=a,r.y2=o;var s=e&&e.lineWidth;return s&&(Ci(i*2)===Ci(n*2)&&(r.x1=r.x2=Ur(i,s,!0)),Ci(a*2)===Ci(o*2)&&(r.y1=r.y2=Ur(a,s,!0))),r}}function sp(r,t,e){if(t){var i=t.x,n=t.y,a=t.width,o=t.height;r.x=i,r.y=n,r.width=a,r.height=o;var s=e&&e.lineWidth;return s&&(r.x=Ur(i,s,!0),r.y=Ur(n,s,!0),r.width=Math.max(Ur(i+a,s,!1)-r.x,a===0?0:1),r.height=Math.max(Ur(n+o,s,!1)-r.y,o===0?0:1)),r}}function Ur(r,t,e){if(!t)return r;var i=Ci(r*2);return(i+Ci(t))%2===0?i/2:(i+(e?1:-1))/2}var j_=function(){function r(){this.x=0,this.y=0,this.width=0,this.height=0}return r}(),t1={},xt=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new j_},t.prototype.buildPath=function(e,i){var n,a,o,s;if(this.subPixelOptimize){var l=sp(t1,i,this.style);n=l.x,a=l.y,o=l.width,s=l.height,l.r=i.r,i=l}else n=i.x,a=i.y,o=i.width,s=i.height;i.r?J_(e,i):e.rect(n,a,o,s)},t.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},t}(ft);xt.prototype.type="rect";var Ih={fill:"#000"},Ph=2,e1={style:at({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},Ho.style)},Lt=function(r){N(t,r);function t(e){var i=r.call(this)||this;return i.type="text",i._children=[],i._defaultStyle=Ih,i.attr(e),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.update=function(){r.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var i=this._children[e];i.zlevel=this.zlevel,i.z=this.z,i.z2=this.z2,i.culling=this.culling,i.cursor=this.cursor,i.invisible=this.invisible}},t.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):r.prototype.updateTransform.call(this)},t.prototype.getLocalTransform=function(e){var i=this.innerTransformable;return i?i.getLocalTransform(e):r.prototype.getLocalTransform.call(this,e)},t.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),r.prototype.getComputedTransform.call(this)},t.prototype._updateSubTexts=function(){this._childCursor=0,o1(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var i=0;i<this._children.length;i++)this._children[i].__zr=e},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var i=0;i<this._children.length;i++)this._children[i].__zr=null},t.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var e=new nt(0,0,0,0),i=this._children,n=[],a=null,o=0;o<i.length;o++){var s=i[o],l=s.getBoundingRect(),u=s.getLocalTransform(n);u?(e.copy(l),e.applyTransform(u),a=a||e.clone(),a.union(e)):(a=a||l.clone(),a.union(l))}this._rect=a||e}return this._rect},t.prototype.setDefaultTextStyle=function(e){this._defaultStyle=e||Ih},t.prototype.setTextContent=function(e){},t.prototype._mergeStyle=function(e,i){if(!i)return e;var n=i.rich,a=e.rich||n&&{};return O(e,i),n&&a?(this._mergeRich(a,n),e.rich=a):a&&(e.rich=a),e},t.prototype._mergeRich=function(e,i){for(var n=dt(i),a=0;a<n.length;a++){var o=n[a];e[o]=e[o]||{},O(e[o],i[o])}},t.prototype.getAnimationStyleProps=function(){return e1},t.prototype._getOrCreateChild=function(e){var i=this._children[this._childCursor];return(!i||!(i instanceof e))&&(i=new e),this._children[this._childCursor++]=i,i.__zr=this.__zr,i.parent=this,i},t.prototype._updatePlainTexts=function(){var e=this.style,i=e.font||Kr,n=e.padding,a=Fh(e),o=T_(a,e),s=Xs(e),l=!!e.backgroundColor,u=o.outerHeight,f=o.outerWidth,h=o.contentWidth,c=o.lines,v=o.lineHeight,d=this._defaultStyle;this.isTruncated=!!o.isTruncated;var g=e.x||0,p=e.y||0,y=e.align||d.align||"left",m=e.verticalAlign||d.verticalAlign||"top",_=g,S=wi(p,o.contentHeight,m);if(s||n){var b=yn(g,f,y),w=wi(p,u,m);s&&this._renderBackground(e,e,b,w,f,u)}S+=v/2,n&&(_=Nh(g,y,n),m==="top"?S+=n[0]:m==="bottom"&&(S-=n[2]));for(var x=0,C=!1,T=Bh("fill"in e?e.fill:(C=!0,d.fill)),D=kh("stroke"in e?e.stroke:!l&&(!d.autoStroke||C)?(x=Ph,d.stroke):null),A=e.textShadowBlur>0,L=e.width!=null&&(e.overflow==="truncate"||e.overflow==="break"||e.overflow==="breakAll"),P=o.calculatedLineHeight,I=0;I<c.length;I++){var R=this._getOrCreateChild(go),E=R.createStyle();R.useStyle(E),E.text=c[I],E.x=_,E.y=S,E.textAlign=y,E.textBaseline="middle",E.opacity=e.opacity,E.strokeFirst=!0,A&&(E.shadowBlur=e.textShadowBlur||0,E.shadowColor=e.textShadowColor||"transparent",E.shadowOffsetX=e.textShadowOffsetX||0,E.shadowOffsetY=e.textShadowOffsetY||0),E.stroke=D,E.fill=T,D&&(E.lineWidth=e.lineWidth||x,E.lineDash=e.lineDash,E.lineDashOffset=e.lineDashOffset||0),E.font=i,Eh(E,e),S+=v,L&&R.setBoundingRect(new nt(yn(E.x,h,E.textAlign),wi(E.y,P,E.textBaseline),h,P))}},t.prototype._updateRichTexts=function(){var e=this.style,i=Fh(e),n=M_(i,e),a=n.width,o=n.outerWidth,s=n.outerHeight,l=e.padding,u=e.x||0,f=e.y||0,h=this._defaultStyle,c=e.align||h.align,v=e.verticalAlign||h.verticalAlign;this.isTruncated=!!n.isTruncated;var d=yn(u,o,c),g=wi(f,s,v),p=d,y=g;l&&(p+=l[3],y+=l[0]);var m=p+a;Xs(e)&&this._renderBackground(e,e,d,g,o,s);for(var _=!!e.backgroundColor,S=0;S<n.lines.length;S++){for(var b=n.lines[S],w=b.tokens,x=w.length,C=b.lineHeight,T=b.width,D=0,A=p,L=m,P=x-1,I=void 0;D<x&&(I=w[D],!I.align||I.align==="left");)this._placeToken(I,e,C,y,A,"left",_),T-=I.width,A+=I.width,D++;for(;P>=0&&(I=w[P],I.align==="right");)this._placeToken(I,e,C,y,L,"right",_),T-=I.width,L-=I.width,P--;for(A+=(a-(A-p)-(m-L)-T)/2;D<=P;)I=w[D],this._placeToken(I,e,C,y,A+I.width/2,"center",_),A+=I.width,D++;y+=C}},t.prototype._placeToken=function(e,i,n,a,o,s,l){var u=i.rich[e.styleName]||{};u.text=e.text;var f=e.verticalAlign,h=a+n/2;f==="top"?h=a+e.height/2:f==="bottom"&&(h=a+n-e.height/2);var c=!e.isLineHolder&&Xs(u);c&&this._renderBackground(u,i,s==="right"?o-e.width:s==="center"?o-e.width/2:o,h-e.height/2,e.width,e.height);var v=!!u.backgroundColor,d=e.textPadding;d&&(o=Nh(o,s,d),h-=e.height/2-d[0]-e.innerHeight/2);var g=this._getOrCreateChild(go),p=g.createStyle();g.useStyle(p);var y=this._defaultStyle,m=!1,_=0,S=Bh("fill"in u?u.fill:"fill"in i?i.fill:(m=!0,y.fill)),b=kh("stroke"in u?u.stroke:"stroke"in i?i.stroke:!v&&!l&&(!y.autoStroke||m)?(_=Ph,y.stroke):null),w=u.textShadowBlur>0||i.textShadowBlur>0;p.text=e.text,p.x=o,p.y=h,w&&(p.shadowBlur=u.textShadowBlur||i.textShadowBlur||0,p.shadowColor=u.textShadowColor||i.textShadowColor||"transparent",p.shadowOffsetX=u.textShadowOffsetX||i.textShadowOffsetX||0,p.shadowOffsetY=u.textShadowOffsetY||i.textShadowOffsetY||0),p.textAlign=s,p.textBaseline="middle",p.font=e.font||Kr,p.opacity=bn(u.opacity,i.opacity,1),Eh(p,u),b&&(p.lineWidth=bn(u.lineWidth,i.lineWidth,_),p.lineDash=K(u.lineDash,i.lineDash),p.lineDashOffset=i.lineDashOffset||0,p.stroke=b),S&&(p.fill=S);var x=e.contentWidth,C=e.contentHeight;g.setBoundingRect(new nt(yn(p.x,x,p.textAlign),wi(p.y,C,p.textBaseline),x,C))},t.prototype._renderBackground=function(e,i,n,a,o,s){var l=e.backgroundColor,u=e.borderWidth,f=e.borderColor,h=l&&l.image,c=l&&!h,v=e.borderRadius,d=this,g,p;if(c||e.lineHeight||u&&f){g=this._getOrCreateChild(xt),g.useStyle(g.createStyle()),g.style.fill=null;var y=g.shape;y.x=n,y.y=a,y.width=o,y.height=s,y.r=v,g.dirtyShape()}if(c){var m=g.style;m.fill=l||null,m.fillOpacity=K(e.fillOpacity,1)}else if(h){p=this._getOrCreateChild(Sr),p.onload=function(){d.dirtyStyle()};var _=p.style;_.image=l.image,_.x=n,_.y=a,_.width=o,_.height=s}if(u&&f){var m=g.style;m.lineWidth=u,m.stroke=f,m.strokeOpacity=K(e.strokeOpacity,1),m.lineDash=e.borderDash,m.lineDashOffset=e.borderDashOffset||0,g.strokeContainThreshold=0,g.hasFill()&&g.hasStroke()&&(m.strokeFirst=!0,m.lineWidth*=2)}var S=(g||p).style;S.shadowBlur=e.shadowBlur||0,S.shadowColor=e.shadowColor||"transparent",S.shadowOffsetX=e.shadowOffsetX||0,S.shadowOffsetY=e.shadowOffsetY||0,S.opacity=bn(e.opacity,i.opacity,1)},t.makeFont=function(e){var i="";return a1(e)&&(i=[e.fontStyle,e.fontWeight,n1(e.fontSize),e.fontFamily||"sans-serif"].join(" ")),i&&Le(i)||e.textFont||e.font},t}(ra),r1={left:!0,right:1,center:1},i1={top:1,bottom:1,middle:1},Rh=["fontStyle","fontWeight","fontSize","fontFamily"];function n1(r){return typeof r=="string"&&(r.indexOf("px")!==-1||r.indexOf("rem")!==-1||r.indexOf("em")!==-1)?r:isNaN(+r)?Nu+"px":r+"px"}function Eh(r,t){for(var e=0;e<Rh.length;e++){var i=Rh[e],n=t[i];n!=null&&(r[i]=n)}}function a1(r){return r.fontSize!=null||r.fontFamily||r.fontWeight}function o1(r){return Oh(r),M(r.rich,Oh),r}function Oh(r){if(r){r.font=Lt.makeFont(r);var t=r.align;t==="middle"&&(t="center"),r.align=t==null||r1[t]?t:"left";var e=r.verticalAlign;e==="center"&&(e="middle"),r.verticalAlign=e==null||i1[e]?e:"top";var i=r.padding;i&&(r.padding=Sd(r.padding))}}function kh(r,t){return r==null||t<=0||r==="transparent"||r==="none"?null:r.image||r.colorStops?"#000":r}function Bh(r){return r==null||r==="none"?null:r.image||r.colorStops?"#000":r}function Nh(r,t,e){return t==="right"?r-e[1]:t==="center"?r+e[3]/2-e[1]/2:r+e[3]}function Fh(r){var t=r.text;return t!=null&&(t+=""),t}function Xs(r){return!!(r.backgroundColor||r.lineHeight||r.borderWidth&&r.borderColor)}var st=_t(),s1=function(r,t,e,i){if(i){var n=st(i);n.dataIndex=e,n.dataType=t,n.seriesIndex=r,n.ssrType="chart",i.type==="group"&&i.traverse(function(a){var o=st(a);o.seriesIndex=r,o.dataIndex=e,o.dataType=t,o.ssrType="chart"})}},zh=1,Hh={},lp=_t(),tf=_t(),ef=0,Go=1,Vo=2,fe=["emphasis","blur","select"],yo=["normal","emphasis","blur","select"],l1=10,u1=9,Zr="highlight",Qa="downplay",Mn="select",Ja="unselect",An="toggleSelect";function ui(r){return r!=null&&r!=="none"}function Wo(r,t,e){r.onHoverStateChange&&(r.hoverState||0)!==e&&r.onHoverStateChange(t),r.hoverState=e}function up(r){Wo(r,"emphasis",Vo)}function fp(r){r.hoverState===Vo&&Wo(r,"normal",ef)}function rf(r){Wo(r,"blur",Go)}function hp(r){r.hoverState===Go&&Wo(r,"normal",ef)}function f1(r){r.selected=!0}function h1(r){r.selected=!1}function Gh(r,t,e){t(r,e)}function Ze(r,t,e){Gh(r,t,e),r.isGroup&&r.traverse(function(i){Gh(i,t,e)})}function Vh(r,t){switch(t){case"emphasis":r.hoverState=Vo;break;case"normal":r.hoverState=ef;break;case"blur":r.hoverState=Go;break;case"select":r.selected=!0}}function v1(r,t,e,i){for(var n=r.style,a={},o=0;o<t.length;o++){var s=t[o],l=n[s];a[s]=l??(i&&i[s])}for(var o=0;o<r.animators.length;o++){var u=r.animators[o];u.__fromStateTransition&&u.__fromStateTransition.indexOf(e)<0&&u.targetName==="style"&&u.saveTo(a,t)}return a}function c1(r,t,e,i){var n=e&&lt(e,"select")>=0,a=!1;if(r instanceof ft){var o=lp(r),s=n&&o.selectFill||o.normalFill,l=n&&o.selectStroke||o.normalStroke;if(ui(s)||ui(l)){i=i||{};var u=i.style||{};u.fill==="inherit"?(a=!0,i=O({},i),u=O({},u),u.fill=s):!ui(u.fill)&&ui(s)?(a=!0,i=O({},i),u=O({},u),u.fill=sh(s)):!ui(u.stroke)&&ui(l)&&(a||(i=O({},i),u=O({},u)),u.stroke=sh(l)),i.style=u}}if(i&&i.z2==null){a||(i=O({},i));var f=r.z2EmphasisLift;i.z2=r.z2+(f??l1)}return i}function d1(r,t,e){if(e&&e.z2==null){e=O({},e);var i=r.z2SelectLift;e.z2=r.z2+(i??u1)}return e}function p1(r,t,e){var i=lt(r.currentStates,t)>=0,n=r.style.opacity,a=i?null:v1(r,["opacity"],t,{opacity:1});e=e||{};var o=e.style||{};return o.opacity==null&&(e=O({},e),o=O({opacity:i?n:a.opacity*.1},o),e.style=o),e}function $s(r,t){var e=this.states[r];if(this.style){if(r==="emphasis")return c1(this,r,t,e);if(r==="blur")return p1(this,r,e);if(r==="select")return d1(this,r,e)}return e}function g1(r){r.stateProxy=$s;var t=r.getTextContent(),e=r.getTextGuideLine();t&&(t.stateProxy=$s),e&&(e.stateProxy=$s)}function Wh(r,t){!pp(r,t)&&!r.__highByOuter&&Ze(r,up)}function Uh(r,t){!pp(r,t)&&!r.__highByOuter&&Ze(r,fp)}function mo(r,t){r.__highByOuter|=1<<(t||0),Ze(r,up)}function _o(r,t){!(r.__highByOuter&=~(1<<(t||0)))&&Ze(r,fp)}function y1(r){Ze(r,rf)}function vp(r){Ze(r,hp)}function cp(r){Ze(r,f1)}function dp(r){Ze(r,h1)}function pp(r,t){return r.__highDownSilentOnTouch&&t.zrByTouch}function gp(r){var t=r.getModel(),e=[],i=[];t.eachComponent(function(n,a){var o=tf(a),s=n==="series",l=s?r.getViewOfSeriesModel(a):r.getViewOfComponentModel(a);!s&&i.push(l),o.isBlured&&(l.group.traverse(function(u){hp(u)}),s&&e.push(a)),o.isBlured=!1}),M(i,function(n){n&&n.toggleBlurSeries&&n.toggleBlurSeries(e,!1,t)})}function eu(r,t,e,i){var n=i.getModel();e=e||"coordinateSystem";function a(u,f){for(var h=0;h<f.length;h++){var c=u.getItemGraphicEl(f[h]);c&&vp(c)}}if(r!=null&&!(!t||t==="none")){var o=n.getSeriesByIndex(r),s=o.coordinateSystem;s&&s.master&&(s=s.master);var l=[];n.eachSeries(function(u){var f=o===u,h=u.coordinateSystem;h&&h.master&&(h=h.master);var c=h&&s?h===s:f;if(!(e==="series"&&!f||e==="coordinateSystem"&&!c||t==="series"&&f)){var v=i.getViewOfSeriesModel(u);if(v.group.traverse(function(p){p.__highByOuter&&f&&t==="self"||rf(p)}),Xt(t))a(u.getData(),t);else if(W(t))for(var d=dt(t),g=0;g<d.length;g++)a(u.getData(d[g]),t[d[g]]);l.push(u),tf(u).isBlured=!0}}),n.eachComponent(function(u,f){if(u!=="series"){var h=i.getViewOfComponentModel(f);h&&h.toggleBlurSeries&&h.toggleBlurSeries(l,!0,n)}})}}function ru(r,t,e){if(!(r==null||t==null)){var i=e.getModel().getComponent(r,t);if(i){tf(i).isBlured=!0;var n=e.getViewOfComponentModel(i);!n||!n.focusBlurEnabled||n.group.traverse(function(a){rf(a)})}}}function m1(r,t,e){var i=r.seriesIndex,n=r.getData(t.dataType);if(n){var a=Jr(n,t);a=(F(a)?a[0]:a)||0;var o=n.getItemGraphicEl(a);if(!o)for(var s=n.count(),l=0;!o&&l<s;)o=n.getItemGraphicEl(l++);if(o){var u=st(o);eu(i,u.focus,u.blurScope,e)}else{var f=r.get(["emphasis","focus"]),h=r.get(["emphasis","blurScope"]);f!=null&&eu(i,f,h,e)}}}function nf(r,t,e,i){var n={focusSelf:!1,dispatchers:null};if(r==null||r==="series"||t==null||e==null)return n;var a=i.getModel().getComponent(r,t);if(!a)return n;var o=i.getViewOfComponentModel(a);if(!o||!o.findHighDownDispatchers)return n;for(var s=o.findHighDownDispatchers(e),l,u=0;u<s.length;u++)if(st(s[u]).focus==="self"){l=!0;break}return{focusSelf:l,dispatchers:s}}function _1(r,t,e){var i=st(r),n=nf(i.componentMainType,i.componentIndex,i.componentHighDownName,e),a=n.dispatchers,o=n.focusSelf;a?(o&&ru(i.componentMainType,i.componentIndex,e),M(a,function(s){return Wh(s,t)})):(eu(i.seriesIndex,i.focus,i.blurScope,e),i.focus==="self"&&ru(i.componentMainType,i.componentIndex,e),Wh(r,t))}function S1(r,t,e){gp(e);var i=st(r),n=nf(i.componentMainType,i.componentIndex,i.componentHighDownName,e).dispatchers;n?M(n,function(a){return Uh(a,t)}):Uh(r,t)}function w1(r,t,e){if(ou(t)){var i=t.dataType,n=r.getData(i),a=Jr(n,t);F(a)||(a=[a]),r[t.type===An?"toggleSelect":t.type===Mn?"select":"unselect"](a,i)}}function Yh(r){var t=r.getAllData();M(t,function(e){var i=e.data,n=e.type;i.eachItemGraphicEl(function(a,o){r.isSelected(o,n)?cp(a):dp(a)})})}function b1(r){var t=[];return r.eachSeries(function(e){var i=e.getAllData();M(i,function(n){n.data;var a=n.type,o=e.getSelectedDataIndices();if(o.length>0){var s={dataIndex:o,seriesIndex:e.seriesIndex};a!=null&&(s.dataType=a),t.push(s)}})}),t}function iu(r,t,e){yp(r,!0),Ze(r,g1),T1(r,t,e)}function x1(r){yp(r,!1)}function So(r,t,e,i){i?x1(r):iu(r,t,e)}function T1(r,t,e){var i=st(r);t!=null?(i.focus=t,i.blurScope=e):i.focus&&(i.focus=null)}var Xh=["emphasis","blur","select"],C1={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function nu(r,t,e,i){e=e||"itemStyle";for(var n=0;n<Xh.length;n++){var a=Xh[n],o=t.getModel([a,e]),s=r.ensureState(a);s.style=o[C1[e]]()}}function yp(r,t){var e=t===!1,i=r;r.highDownSilentOnTouch&&(i.__highDownSilentOnTouch=r.highDownSilentOnTouch),(!e||i.__highDownDispatcher)&&(i.__highByOuter=i.__highByOuter||0,i.__highDownDispatcher=!e)}function au(r){return!!(r&&r.__highDownDispatcher)}function D1(r){var t=Hh[r];return t==null&&zh<=32&&(t=Hh[r]=zh++),t}function ou(r){var t=r.type;return t===Mn||t===Ja||t===An}function $h(r){var t=r.type;return t===Zr||t===Qa}function M1(r){var t=lp(r);t.normalFill=r.style.fill,t.normalStroke=r.style.stroke;var e=r.states.select||{};t.selectFill=e.style&&e.style.fill||null,t.selectStroke=e.style&&e.style.stroke||null}var fi=jr.CMD,A1=[[],[],[]],Zh=Math.sqrt,L1=Math.atan2;function I1(r,t){if(t){var e=r.data,i=r.len(),n,a,o,s,l,u,f=fi.M,h=fi.C,c=fi.L,v=fi.R,d=fi.A,g=fi.Q;for(o=0,s=0;o<i;){switch(n=e[o++],s=o,a=0,n){case f:a=1;break;case c:a=1;break;case h:a=3;break;case g:a=2;break;case d:var p=t[4],y=t[5],m=Zh(t[0]*t[0]+t[1]*t[1]),_=Zh(t[2]*t[2]+t[3]*t[3]),S=L1(-t[1]/_,t[0]/m);e[o]*=m,e[o++]+=p,e[o]*=_,e[o++]+=y,e[o++]*=m,e[o++]*=_,e[o++]+=S,e[o++]+=S,o+=2,s=o;break;case v:u[0]=e[o++],u[1]=e[o++],ue(u,u,t),e[s++]=u[0],e[s++]=u[1],u[0]+=e[o++],u[1]+=e[o++],ue(u,u,t),e[s++]=u[0],e[s++]=u[1]}for(l=0;l<a;l++){var b=A1[l];b[0]=e[o++],b[1]=e[o++],ue(b,b,t),e[s++]=b[0],e[s++]=b[1]}}r.increaseVersion()}}var Zs=Math.sqrt,ma=Math.sin,_a=Math.cos,tn=Math.PI;function qh(r){return Math.sqrt(r[0]*r[0]+r[1]*r[1])}function su(r,t){return(r[0]*t[0]+r[1]*t[1])/(qh(r)*qh(t))}function Kh(r,t){return(r[0]*t[1]<r[1]*t[0]?-1:1)*Math.acos(su(r,t))}function Qh(r,t,e,i,n,a,o,s,l,u,f){var h=l*(tn/180),c=_a(h)*(r-e)/2+ma(h)*(t-i)/2,v=-1*ma(h)*(r-e)/2+_a(h)*(t-i)/2,d=c*c/(o*o)+v*v/(s*s);d>1&&(o*=Zs(d),s*=Zs(d));var g=(n===a?-1:1)*Zs((o*o*(s*s)-o*o*(v*v)-s*s*(c*c))/(o*o*(v*v)+s*s*(c*c)))||0,p=g*o*v/s,y=g*-s*c/o,m=(r+e)/2+_a(h)*p-ma(h)*y,_=(t+i)/2+ma(h)*p+_a(h)*y,S=Kh([1,0],[(c-p)/o,(v-y)/s]),b=[(c-p)/o,(v-y)/s],w=[(-1*c-p)/o,(-1*v-y)/s],x=Kh(b,w);if(su(b,w)<=-1&&(x=tn),su(b,w)>=1&&(x=0),x<0){var C=Math.round(x/tn*1e6)/1e6;x=tn*2+C%2*tn}f.addData(u,m,_,o,s,S,x,h,a)}var P1=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/ig,R1=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function E1(r){var t=new jr;if(!r)return t;var e=0,i=0,n=e,a=i,o,s=jr.CMD,l=r.match(P1);if(!l)return t;for(var u=0;u<l.length;u++){for(var f=l[u],h=f.charAt(0),c=void 0,v=f.match(R1)||[],d=v.length,g=0;g<d;g++)v[g]=parseFloat(v[g]);for(var p=0;p<d;){var y=void 0,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0,x=void 0,C=e,T=i,D=void 0,A=void 0;switch(h){case"l":e+=v[p++],i+=v[p++],c=s.L,t.addData(c,e,i);break;case"L":e=v[p++],i=v[p++],c=s.L,t.addData(c,e,i);break;case"m":e+=v[p++],i+=v[p++],c=s.M,t.addData(c,e,i),n=e,a=i,h="l";break;case"M":e=v[p++],i=v[p++],c=s.M,t.addData(c,e,i),n=e,a=i,h="L";break;case"h":e+=v[p++],c=s.L,t.addData(c,e,i);break;case"H":e=v[p++],c=s.L,t.addData(c,e,i);break;case"v":i+=v[p++],c=s.L,t.addData(c,e,i);break;case"V":i=v[p++],c=s.L,t.addData(c,e,i);break;case"C":c=s.C,t.addData(c,v[p++],v[p++],v[p++],v[p++],v[p++],v[p++]),e=v[p-2],i=v[p-1];break;case"c":c=s.C,t.addData(c,v[p++]+e,v[p++]+i,v[p++]+e,v[p++]+i,v[p++]+e,v[p++]+i),e+=v[p-2],i+=v[p-1];break;case"S":y=e,m=i,D=t.len(),A=t.data,o===s.C&&(y+=e-A[D-4],m+=i-A[D-3]),c=s.C,C=v[p++],T=v[p++],e=v[p++],i=v[p++],t.addData(c,y,m,C,T,e,i);break;case"s":y=e,m=i,D=t.len(),A=t.data,o===s.C&&(y+=e-A[D-4],m+=i-A[D-3]),c=s.C,C=e+v[p++],T=i+v[p++],e+=v[p++],i+=v[p++],t.addData(c,y,m,C,T,e,i);break;case"Q":C=v[p++],T=v[p++],e=v[p++],i=v[p++],c=s.Q,t.addData(c,C,T,e,i);break;case"q":C=v[p++]+e,T=v[p++]+i,e+=v[p++],i+=v[p++],c=s.Q,t.addData(c,C,T,e,i);break;case"T":y=e,m=i,D=t.len(),A=t.data,o===s.Q&&(y+=e-A[D-4],m+=i-A[D-3]),e=v[p++],i=v[p++],c=s.Q,t.addData(c,y,m,e,i);break;case"t":y=e,m=i,D=t.len(),A=t.data,o===s.Q&&(y+=e-A[D-4],m+=i-A[D-3]),e+=v[p++],i+=v[p++],c=s.Q,t.addData(c,y,m,e,i);break;case"A":_=v[p++],S=v[p++],b=v[p++],w=v[p++],x=v[p++],C=e,T=i,e=v[p++],i=v[p++],c=s.A,Qh(C,T,e,i,w,x,_,S,b,c,t);break;case"a":_=v[p++],S=v[p++],b=v[p++],w=v[p++],x=v[p++],C=e,T=i,e+=v[p++],i+=v[p++],c=s.A,Qh(C,T,e,i,w,x,_,S,b,c,t);break}}(h==="z"||h==="Z")&&(c=s.Z,t.addData(c),e=n,i=a),o=c}return t.toStatic(),t}var mp=function(r){N(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.applyTransform=function(e){},t}(ft);function _p(r){return r.setData!=null}function Sp(r,t){var e=E1(r),i=O({},t);return i.buildPath=function(n){if(_p(n)){n.setData(e.data);var a=n.getContext();a&&n.rebuildPath(a,1)}else{var a=n;e.rebuildPath(a,1)}},i.applyTransform=function(n){I1(e,n),this.dirtyShape()},i}function O1(r,t){return new mp(Sp(r,t))}function k1(r,t){var e=Sp(r,t),i=function(n){N(a,n);function a(o){var s=n.call(this,o)||this;return s.applyTransform=e.applyTransform,s.buildPath=e.buildPath,s}return a}(mp);return i}function B1(r,t){for(var e=[],i=r.length,n=0;n<i;n++){var a=r[n];e.push(a.getUpdatedPathProxy(!0))}var o=new ft(t);return o.createPathProxy(),o.buildPath=function(s){if(_p(s)){s.appendPath(e);var l=s.getContext();l&&s.rebuildPath(l,1)}},o}var N1=function(){function r(){this.cx=0,this.cy=0,this.r=0}return r}(),Uo=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new N1},t.prototype.buildPath=function(e,i){e.moveTo(i.cx+i.r,i.cy),e.arc(i.cx,i.cy,i.r,0,Math.PI*2)},t}(ft);Uo.prototype.type="circle";var F1=function(){function r(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}return r}(),af=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new F1},t.prototype.buildPath=function(e,i){var n=.5522848,a=i.cx,o=i.cy,s=i.rx,l=i.ry,u=s*n,f=l*n;e.moveTo(a-s,o),e.bezierCurveTo(a-s,o-f,a-u,o-l,a,o-l),e.bezierCurveTo(a+u,o-l,a+s,o-f,a+s,o),e.bezierCurveTo(a+s,o+f,a+u,o+l,a,o+l),e.bezierCurveTo(a-u,o+l,a-s,o+f,a-s,o),e.closePath()},t}(ft);af.prototype.type="ellipse";var wp=Math.PI,qs=wp*2,kr=Math.sin,hi=Math.cos,z1=Math.acos,It=Math.atan2,Jh=Math.abs,Ln=Math.sqrt,mn=Math.max,De=Math.min,de=1e-4;function H1(r,t,e,i,n,a,o,s){var l=e-r,u=i-t,f=o-n,h=s-a,c=h*l-f*u;if(!(c*c<de))return c=(f*(t-a)-h*(r-n))/c,[r+c*l,t+c*u]}function Sa(r,t,e,i,n,a,o){var s=r-e,l=t-i,u=(o?a:-a)/Ln(s*s+l*l),f=u*l,h=-u*s,c=r+f,v=t+h,d=e+f,g=i+h,p=(c+d)/2,y=(v+g)/2,m=d-c,_=g-v,S=m*m+_*_,b=n-a,w=c*g-d*v,x=(_<0?-1:1)*Ln(mn(0,b*b*S-w*w)),C=(w*_-m*x)/S,T=(-w*m-_*x)/S,D=(w*_+m*x)/S,A=(-w*m+_*x)/S,L=C-p,P=T-y,I=D-p,R=A-y;return L*L+P*P>I*I+R*R&&(C=D,T=A),{cx:C,cy:T,x0:-f,y0:-h,x1:C*(n/b-1),y1:T*(n/b-1)}}function G1(r){var t;if(F(r)){var e=r.length;if(!e)return r;e===1?t=[r[0],r[0],0,0]:e===2?t=[r[0],r[0],r[1],r[1]]:e===3?t=r.concat(r[2]):t=r}else t=[r,r,r,r];return t}function V1(r,t){var e,i=mn(t.r,0),n=mn(t.r0||0,0),a=i>0,o=n>0;if(!(!a&&!o)){if(a||(i=n,n=0),n>i){var s=i;i=n,n=s}var l=t.startAngle,u=t.endAngle;if(!(isNaN(l)||isNaN(u))){var f=t.cx,h=t.cy,c=!!t.clockwise,v=Jh(u-l),d=v>qs&&v%qs;if(d>de&&(v=d),!(i>de))r.moveTo(f,h);else if(v>qs-de)r.moveTo(f+i*hi(l),h+i*kr(l)),r.arc(f,h,i,l,u,!c),n>de&&(r.moveTo(f+n*hi(u),h+n*kr(u)),r.arc(f,h,n,u,l,c));else{var g=void 0,p=void 0,y=void 0,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0,x=void 0,C=void 0,T=void 0,D=void 0,A=void 0,L=void 0,P=void 0,I=void 0,R=i*hi(l),E=i*kr(l),z=n*hi(u),B=n*kr(u),k=v>de;if(k){var H=t.cornerRadius;H&&(e=G1(H),g=e[0],p=e[1],y=e[2],m=e[3]);var Y=Jh(i-n)/2;if(_=De(Y,y),S=De(Y,m),b=De(Y,g),w=De(Y,p),T=x=mn(_,S),D=C=mn(b,w),(x>de||C>de)&&(A=i*hi(u),L=i*kr(u),P=n*hi(l),I=n*kr(l),v<wp)){var U=H1(R,E,P,I,A,L,z,B);if(U){var tt=R-U[0],et=E-U[1],ut=A-U[0],Ft=L-U[1],Jt=1/kr(z1((tt*ut+et*Ft)/(Ln(tt*tt+et*et)*Ln(ut*ut+Ft*Ft)))/2),qt=Ln(U[0]*U[0]+U[1]*U[1]);T=De(x,(i-qt)/(Jt+1)),D=De(C,(n-qt)/(Jt-1))}}}if(!k)r.moveTo(f+R,h+E);else if(T>de){var Tt=De(y,T),yt=De(m,T),X=Sa(P,I,R,E,i,Tt,c),J=Sa(A,L,z,B,i,yt,c);r.moveTo(f+X.cx+X.x0,h+X.cy+X.y0),T<x&&Tt===yt?r.arc(f+X.cx,h+X.cy,T,It(X.y0,X.x0),It(J.y0,J.x0),!c):(Tt>0&&r.arc(f+X.cx,h+X.cy,Tt,It(X.y0,X.x0),It(X.y1,X.x1),!c),r.arc(f,h,i,It(X.cy+X.y1,X.cx+X.x1),It(J.cy+J.y1,J.cx+J.x1),!c),yt>0&&r.arc(f+J.cx,h+J.cy,yt,It(J.y1,J.x1),It(J.y0,J.x0),!c))}else r.moveTo(f+R,h+E),r.arc(f,h,i,l,u,!c);if(!(n>de)||!k)r.lineTo(f+z,h+B);else if(D>de){var Tt=De(g,D),yt=De(p,D),X=Sa(z,B,A,L,n,-yt,c),J=Sa(R,E,P,I,n,-Tt,c);r.lineTo(f+X.cx+X.x0,h+X.cy+X.y0),D<C&&Tt===yt?r.arc(f+X.cx,h+X.cy,D,It(X.y0,X.x0),It(J.y0,J.x0),!c):(yt>0&&r.arc(f+X.cx,h+X.cy,yt,It(X.y0,X.x0),It(X.y1,X.x1),!c),r.arc(f,h,n,It(X.cy+X.y1,X.cx+X.x1),It(J.cy+J.y1,J.cx+J.x1),c),Tt>0&&r.arc(f+J.cx,h+J.cy,Tt,It(J.y1,J.x1),It(J.y0,J.x0),!c))}else r.lineTo(f+z,h+B),r.arc(f,h,n,u,l,c)}r.closePath()}}}var W1=function(){function r(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0,this.cornerRadius=0}return r}(),Hi=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new W1},t.prototype.buildPath=function(e,i){V1(e,i)},t.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},t}(ft);Hi.prototype.type="sector";var U1=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.r0=0}return r}(),of=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new U1},t.prototype.buildPath=function(e,i){var n=i.cx,a=i.cy,o=Math.PI*2;e.moveTo(n+i.r,a),e.arc(n,a,i.r,0,o,!1),e.moveTo(n+i.r0,a),e.arc(n,a,i.r0,0,o,!0)},t}(ft);of.prototype.type="ring";function Y1(r,t,e,i){var n=[],a=[],o=[],s=[],l,u,f,h;if(i){f=[1/0,1/0],h=[-1/0,-1/0];for(var c=0,v=r.length;c<v;c++)bi(f,f,r[c]),xi(h,h,r[c]);bi(f,f,i[0]),xi(h,h,i[1])}for(var c=0,v=r.length;c<v;c++){var d=r[c];if(e)l=r[c?c-1:v-1],u=r[(c+1)%v];else if(c===0||c===v-1){n.push(Am(r[c]));continue}else l=r[c-1],u=r[c+1];Lm(a,u,l),vs(a,a,t);var g=Ol(d,l),p=Ol(d,u),y=g+p;y!==0&&(g/=y,p/=y),vs(o,a,-g),vs(s,a,p);var m=Uf([],d,o),_=Uf([],d,s);i&&(xi(m,m,f),bi(m,m,h),xi(_,_,f),bi(_,_,h)),n.push(m),n.push(_)}return e&&n.push(n.shift()),n}function bp(r,t,e){var i=t.smooth,n=t.points;if(n&&n.length>=2){if(i){var a=Y1(n,i,e,t.smoothConstraint);r.moveTo(n[0][0],n[0][1]);for(var o=n.length,s=0;s<(e?o:o-1);s++){var l=a[s*2],u=a[s*2+1],f=n[(s+1)%o];r.bezierCurveTo(l[0],l[1],u[0],u[1],f[0],f[1])}}else{r.moveTo(n[0][0],n[0][1]);for(var s=1,h=n.length;s<h;s++)r.lineTo(n[s][0],n[s][1])}e&&r.closePath()}}var X1=function(){function r(){this.points=null,this.smooth=0,this.smoothConstraint=null}return r}(),sf=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new X1},t.prototype.buildPath=function(e,i){bp(e,i,!0)},t}(ft);sf.prototype.type="polygon";var $1=function(){function r(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}return r}(),ia=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new $1},t.prototype.buildPath=function(e,i){bp(e,i,!1)},t}(ft);ia.prototype.type="polyline";var Z1={},q1=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return r}(),mr=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new q1},t.prototype.buildPath=function(e,i){var n,a,o,s;if(this.subPixelOptimize){var l=op(Z1,i,this.style);n=l.x1,a=l.y1,o=l.x2,s=l.y2}else n=i.x1,a=i.y1,o=i.x2,s=i.y2;var u=i.percent;u!==0&&(e.moveTo(n,a),u<1&&(o=n*(1-u)+o*u,s=a*(1-u)+s*u),e.lineTo(o,s))},t.prototype.pointAt=function(e){var i=this.shape;return[i.x1*(1-e)+i.x2*e,i.y1*(1-e)+i.y2*e]},t}(ft);mr.prototype.type="line";var zt=[],K1=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}return r}();function jh(r,t,e){var i=r.cpx2,n=r.cpy2;return i!=null||n!=null?[(e?eh:At)(r.x1,r.cpx1,r.cpx2,r.x2,t),(e?eh:At)(r.y1,r.cpy1,r.cpy2,r.y2,t)]:[(e?rh:Vt)(r.x1,r.cpx1,r.x2,t),(e?rh:Vt)(r.y1,r.cpy1,r.y2,t)]}var lf=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new K1},t.prototype.buildPath=function(e,i){var n=i.x1,a=i.y1,o=i.x2,s=i.y2,l=i.cpx1,u=i.cpy1,f=i.cpx2,h=i.cpy2,c=i.percent;c!==0&&(e.moveTo(n,a),f==null||h==null?(c<1&&(fo(n,l,o,c,zt),l=zt[1],o=zt[2],fo(a,u,s,c,zt),u=zt[1],s=zt[2]),e.quadraticCurveTo(l,u,o,s)):(c<1&&(uo(n,l,f,o,c,zt),l=zt[1],f=zt[2],o=zt[3],uo(a,u,h,s,c,zt),u=zt[1],h=zt[2],s=zt[3]),e.bezierCurveTo(l,u,f,h,o,s)))},t.prototype.pointAt=function(e){return jh(this.shape,e,!1)},t.prototype.tangentAt=function(e){var i=jh(this.shape,e,!0);return Rm(i,i)},t}(ft);lf.prototype.type="bezier-curve";var Q1=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return r}(),Yo=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Q1},t.prototype.buildPath=function(e,i){var n=i.cx,a=i.cy,o=Math.max(i.r,0),s=i.startAngle,l=i.endAngle,u=i.clockwise,f=Math.cos(s),h=Math.sin(s);e.moveTo(f*o+n,h*o+a),e.arc(n,a,o,s,l,!u)},t}(ft);Yo.prototype.type="arc";var J1=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="compound",e}return t.prototype._updatePathDirty=function(){for(var e=this.shape.paths,i=this.shapeChanged(),n=0;n<e.length;n++)i=i||e[n].shapeChanged();i&&this.dirtyShape()},t.prototype.beforeBrush=function(){this._updatePathDirty();for(var e=this.shape.paths||[],i=this.getGlobalScale(),n=0;n<e.length;n++)e[n].path||e[n].createPathProxy(),e[n].path.setScale(i[0],i[1],e[n].segmentIgnoreThreshold)},t.prototype.buildPath=function(e,i){for(var n=i.paths||[],a=0;a<n.length;a++)n[a].buildPath(e,n[a].shape,!0)},t.prototype.afterBrush=function(){for(var e=this.shape.paths||[],i=0;i<e.length;i++)e[i].pathUpdated()},t.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),ft.prototype.getBoundingRect.call(this)},t}(ft),xp=function(){function r(t){this.colorStops=t||[]}return r.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},r}(),Tp=function(r){N(t,r);function t(e,i,n,a,o,s){var l=r.call(this,o)||this;return l.x=e??0,l.y=i??0,l.x2=n??1,l.y2=a??0,l.type="linear",l.global=s||!1,l}return t}(xp),j1=function(r){N(t,r);function t(e,i,n,a,o){var s=r.call(this,a)||this;return s.x=e??.5,s.y=i??.5,s.r=n??.5,s.type="radial",s.global=o||!1,s}return t}(xp),Br=[0,0],Nr=[0,0],wa=new Z,ba=new Z,wo=function(){function r(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var i=0;i<4;i++)this._corners[i]=new Z;for(var i=0;i<2;i++)this._axes[i]=new Z;t&&this.fromBoundingRect(t,e)}return r.prototype.fromBoundingRect=function(t,e){var i=this._corners,n=this._axes,a=t.x,o=t.y,s=a+t.width,l=o+t.height;if(i[0].set(a,o),i[1].set(s,o),i[2].set(s,l),i[3].set(a,l),e)for(var u=0;u<4;u++)i[u].transform(e);Z.sub(n[0],i[1],i[0]),Z.sub(n[1],i[3],i[0]),n[0].normalize(),n[1].normalize();for(var u=0;u<2;u++)this._origin[u]=n[u].dot(i[0])},r.prototype.intersect=function(t,e){var i=!0,n=!e;return wa.set(1/0,1/0),ba.set(0,0),!this._intersectCheckOneSide(this,t,wa,ba,n,1)&&(i=!1,n)||!this._intersectCheckOneSide(t,this,wa,ba,n,-1)&&(i=!1,n)||n||Z.copy(e,i?wa:ba),i},r.prototype._intersectCheckOneSide=function(t,e,i,n,a,o){for(var s=!0,l=0;l<2;l++){var u=this._axes[l];if(this._getProjMinMaxOnAxis(l,t._corners,Br),this._getProjMinMaxOnAxis(l,e._corners,Nr),Br[1]<Nr[0]||Br[0]>Nr[1]){if(s=!1,a)return s;var f=Math.abs(Nr[0]-Br[1]),h=Math.abs(Br[0]-Nr[1]);Math.min(f,h)>n.len()&&(f<h?Z.scale(n,u,-f*o):Z.scale(n,u,h*o))}else if(i){var f=Math.abs(Nr[0]-Br[1]),h=Math.abs(Br[0]-Nr[1]);Math.min(f,h)<i.len()&&(f<h?Z.scale(i,u,f*o):Z.scale(i,u,-h*o))}}return s},r.prototype._getProjMinMaxOnAxis=function(t,e,i){for(var n=this._axes[t],a=this._origin,o=e[0].dot(n)+a[t],s=o,l=o,u=1;u<e.length;u++){var f=e[u].dot(n)+a[t];s=Math.min(f,s),l=Math.max(f,l)}i[0]=s,i[1]=l},r}(),tS=[],eS=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return t.prototype.traverse=function(e,i){e.call(i,this)},t.prototype.useStyle=function(){this.style={}},t.prototype.getCursor=function(){return this._cursor},t.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},t.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},t.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},t.prototype.addDisplayable=function(e,i){i?this._temporaryDisplayables.push(e):this._displayables.push(e),this.markRedraw()},t.prototype.addDisplayables=function(e,i){i=i||!1;for(var n=0;n<e.length;n++)this.addDisplayable(e[n],i)},t.prototype.getDisplayables=function(){return this._displayables},t.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},t.prototype.eachPendingDisplayable=function(e){for(var i=this._cursor;i<this._displayables.length;i++)e&&e(this._displayables[i]);for(var i=0;i<this._temporaryDisplayables.length;i++)e&&e(this._temporaryDisplayables[i])},t.prototype.update=function(){this.updateTransform();for(var e=this._cursor;e<this._displayables.length;e++){var i=this._displayables[e];i.parent=this,i.update(),i.parent=null}for(var e=0;e<this._temporaryDisplayables.length;e++){var i=this._temporaryDisplayables[e];i.parent=this,i.update(),i.parent=null}},t.prototype.getBoundingRect=function(){if(!this._rect){for(var e=new nt(1/0,1/0,-1/0,-1/0),i=0;i<this._displayables.length;i++){var n=this._displayables[i],a=n.getBoundingRect().clone();n.needLocalTransform()&&a.applyTransform(n.getLocalTransform(tS)),e.union(a)}this._rect=e}return this._rect},t.prototype.contain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect();if(a.contain(n[0],n[1]))for(var o=0;o<this._displayables.length;o++){var s=this._displayables[o];if(s.contain(e,i))return!0}return!1},t}(ra),rS=_t();function iS(r,t,e,i,n){var a;if(t&&t.ecModel){var o=t.ecModel.getUpdatePayload();a=o&&o.animation}var s=t&&t.isAnimationEnabled(),l=r==="update";if(s){var u=void 0,f=void 0,h=void 0;i?(u=K(i.duration,200),f=K(i.easing,"cubicOut"),h=0):(u=t.getShallow(l?"animationDurationUpdate":"animationDuration"),f=t.getShallow(l?"animationEasingUpdate":"animationEasing"),h=t.getShallow(l?"animationDelayUpdate":"animationDelay")),a&&(a.duration!=null&&(u=a.duration),a.easing!=null&&(f=a.easing),a.delay!=null&&(h=a.delay)),q(h)&&(h=h(e,n)),q(u)&&(u=u(e));var c={duration:u||0,delay:h,easing:f};return c}else return null}function uf(r,t,e,i,n,a,o){var s=!1,l;q(n)?(o=a,a=n,n=null):W(n)&&(a=n.cb,o=n.during,s=n.isFrom,l=n.removeOpt,n=n.dataIndex);var u=r==="leave";u||t.stopAnimation("leave");var f=iS(r,i,n,u?l||{}:null,i&&i.getAnimationDelayParams?i.getAnimationDelayParams(t,n):null);if(f&&f.duration>0){var h=f.duration,c=f.delay,v=f.easing,d={duration:h,delay:c||0,easing:v,done:a,force:!!a||!!o,setToFinal:!u,scope:r,during:o};s?t.animateFrom(e,d):t.animateTo(e,d)}else t.stopAnimation(),!s&&t.attr(e),o&&o(1),a&&a()}function Re(r,t,e,i,n,a){uf("update",r,t,e,i,n,a)}function cr(r,t,e,i,n,a){uf("enter",r,t,e,i,n,a)}function In(r){if(!r.__zr)return!0;for(var t=0;t<r.animators.length;t++){var e=r.animators[t];if(e.scope==="leave")return!0}return!1}function bo(r,t,e,i,n,a){In(r)||uf("leave",r,t,e,i,n,a)}function tv(r,t,e,i){r.removeTextContent(),r.removeTextGuideLine(),bo(r,{style:{opacity:0}},t,e,i)}function Cp(r,t,e){function i(){r.parent&&r.parent.remove(r)}r.isGroup?r.traverse(function(n){n.isGroup||tv(n,t,e,i)}):tv(r,t,e,i)}function Dp(r){rS(r).oldStyle=r.style}var xo=Math.max,To=Math.min,lu={};function nS(r){return ft.extend(r)}var aS=k1;function oS(r,t){return aS(r,t)}function Se(r,t){lu[r]=t}function sS(r){if(lu.hasOwnProperty(r))return lu[r]}function ff(r,t,e,i){var n=O1(r,t);return e&&(i==="center"&&(e=Ap(e,n.getBoundingRect())),Lp(n,e)),n}function Mp(r,t,e){var i=new Sr({style:{image:r,x:t.x,y:t.y,width:t.width,height:t.height},onload:function(n){if(e==="center"){var a={width:n.width,height:n.height};i.setStyle(Ap(t,a))}}});return i}function Ap(r,t){var e=t.width/t.height,i=r.height*e,n;i<=r.width?n=r.height:(i=r.width,n=i/e);var a=r.x+r.width/2,o=r.y+r.height/2;return{x:a-i/2,y:o-n/2,width:i,height:n}}var lS=B1;function Lp(r,t){if(r.applyTransform){var e=r.getBoundingRect(),i=e.calculateTransform(t);r.applyTransform(i)}}function Vn(r,t){return op(r,r,{lineWidth:t}),r}function uS(r){return sp(r.shape,r.shape,r.style),r}var fS=Ur;function hS(r,t){for(var e=Wu([]);r&&r!==t;)Li(e,r.getLocalTransform(),e),r=r.parent;return e}function hf(r,t,e){return t&&!Xt(t)&&(t=$u.getLocalTransform(t)),e&&(t=Yu([],t)),ue([],r,t)}function vS(r,t,e){var i=t[4]===0||t[5]===0||t[0]===0?1:Math.abs(2*t[4]/t[0]),n=t[4]===0||t[5]===0||t[2]===0?1:Math.abs(2*t[4]/t[2]),a=[r==="left"?-i:r==="right"?i:0,r==="top"?-n:r==="bottom"?n:0];return a=hf(a,t,e),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"}function ev(r){return!r.isGroup}function cS(r){return r.shape!=null}function Ip(r,t,e){if(!r||!t)return;function i(o){var s={};return o.traverse(function(l){ev(l)&&l.anid&&(s[l.anid]=l)}),s}function n(o){var s={x:o.x,y:o.y,rotation:o.rotation};return cS(o)&&(s.shape=O({},o.shape)),s}var a=i(r);t.traverse(function(o){if(ev(o)&&o.anid){var s=a[o.anid];if(s){var l=n(o);o.attr(n(s)),Re(o,l,e,st(o).dataIndex)}}})}function dS(r,t){return V(r,function(e){var i=e[0];i=xo(i,t.x),i=To(i,t.x+t.width);var n=e[1];return n=xo(n,t.y),n=To(n,t.y+t.height),[i,n]})}function pS(r,t){var e=xo(r.x,t.x),i=To(r.x+r.width,t.x+t.width),n=xo(r.y,t.y),a=To(r.y+r.height,t.y+t.height);if(i>=e&&a>=n)return{x:e,y:n,width:i-e,height:a-n}}function vf(r,t,e){var i=O({rectHover:!0},t),n=i.style={strokeNoScale:!0};if(e=e||{x:-1,y:-1,width:2,height:2},r)return r.indexOf("image://")===0?(n.image=r.slice(8),at(n,e),new Sr(i)):ff(r.replace("path://",""),i,e,"center")}function gS(r,t,e,i,n){for(var a=0,o=n[n.length-1];a<n.length;a++){var s=n[a];if(Pp(r,t,e,i,s[0],s[1],o[0],o[1]))return!0;o=s}}function Pp(r,t,e,i,n,a,o,s){var l=e-r,u=i-t,f=o-n,h=s-a,c=Ks(f,h,l,u);if(yS(c))return!1;var v=r-n,d=t-a,g=Ks(v,d,l,u)/c;if(g<0||g>1)return!1;var p=Ks(v,d,f,h)/c;return!(p<0||p>1)}function Ks(r,t,e,i){return r*i-e*t}function yS(r){return r<=1e-6&&r>=-1e-6}function Xo(r){var t=r.itemTooltipOption,e=r.componentModel,i=r.itemName,n=G(t)?{formatter:t}:t,a=e.mainType,o=e.componentIndex,s={componentType:a,name:i,$vars:["name"]};s[a+"Index"]=o;var l=r.formatterParamsExtra;l&&M(dt(l),function(f){Qr(s,f)||(s[f]=l[f],s.$vars.push(f))});var u=st(r.el);u.componentMainType=a,u.componentIndex=o,u.tooltipConfig={name:i,option:at({content:i,encodeHTMLContent:!0,formatterParams:s},n)}}function rv(r,t){var e;r.isGroup&&(e=t(r)),e||r.traverse(t)}function cf(r,t){if(r)if(F(r))for(var e=0;e<r.length;e++)rv(r[e],t);else rv(r,t)}Se("circle",Uo);Se("ellipse",af);Se("sector",Hi);Se("ring",of);Se("polygon",sf);Se("polyline",ia);Se("rect",xt);Se("line",mr);Se("bezierCurve",lf);Se("arc",Yo);const mS=Object.freeze(Object.defineProperty({__proto__:null,Arc:Yo,BezierCurve:lf,BoundingRect:nt,Circle:Uo,CompoundPath:J1,Ellipse:af,Group:Ot,Image:Sr,IncrementalDisplayable:eS,Line:mr,LinearGradient:Tp,OrientedBoundingRect:wo,Path:ft,Point:Z,Polygon:sf,Polyline:ia,RadialGradient:j1,Rect:xt,Ring:of,Sector:Hi,Text:Lt,applyTransform:hf,clipPointsByRect:dS,clipRectByRect:pS,createIcon:vf,extendPath:oS,extendShape:nS,getShapeClass:sS,getTransform:hS,groupTransition:Ip,initProps:cr,isElementRemoved:In,lineLineIntersect:Pp,linePolygonIntersect:gS,makeImage:Mp,makePath:ff,mergePath:lS,registerShape:Se,removeElement:bo,removeElementWithFadeOut:Cp,resizePath:Lp,setTooltipConfig:Xo,subPixelOptimize:fS,subPixelOptimizeLine:Vn,subPixelOptimizeRect:uS,transformDirection:vS,traverseElements:cf,updateProps:Re},Symbol.toStringTag,{value:"Module"}));var $o={};function _S(r,t){for(var e=0;e<fe.length;e++){var i=fe[e],n=t[i],a=r.ensureState(i);a.style=a.style||{},a.style.text=n}var o=r.currentStates.slice();r.clearStates(!0),r.setStyle({text:t.normal}),r.useStates(o,!0)}function iv(r,t,e){var i=r.labelFetcher,n=r.labelDataIndex,a=r.labelDimIndex,o=t.normal,s;i&&(s=i.getFormattedLabel(n,"normal",null,a,o&&o.get("formatter"),e!=null?{interpolatedValue:e}:null)),s==null&&(s=q(r.defaultText)?r.defaultText(n,r,e):r.defaultText);for(var l={normal:s},u=0;u<fe.length;u++){var f=fe[u],h=t[f];l[f]=K(i?i.getFormattedLabel(n,f,null,a,h&&h.get("formatter")):null,s)}return l}function Zo(r,t,e,i){e=e||$o;for(var n=r instanceof Lt,a=!1,o=0;o<yo.length;o++){var s=t[yo[o]];if(s&&s.getShallow("show")){a=!0;break}}var l=n?r:r.getTextContent();if(a){n||(l||(l=new Lt,r.setTextContent(l)),r.stateProxy&&(l.stateProxy=r.stateProxy));var u=iv(e,t),f=t.normal,h=!!f.getShallow("show"),c=_r(f,i&&i.normal,e,!1,!n);c.text=u.normal,n||r.setTextConfig(nv(f,e,!1));for(var o=0;o<fe.length;o++){var v=fe[o],s=t[v];if(s){var d=l.ensureState(v),g=!!K(s.getShallow("show"),h);if(g!==h&&(d.ignore=!g),d.style=_r(s,i&&i[v],e,!0,!n),d.style.text=u[v],!n){var p=r.ensureState(v);p.textConfig=nv(s,e,!0)}}}l.silent=!!f.getShallow("silent"),l.style.x!=null&&(c.x=l.style.x),l.style.y!=null&&(c.y=l.style.y),l.ignore=!h,l.useStyle(c),l.dirty(),e.enableTextSetter&&(Rp(l).setLabelText=function(y){var m=iv(e,t,y);_S(l,m)})}else l&&(l.ignore=!0);r.dirty()}function qo(r,t){t=t||"label";for(var e={normal:r.getModel(t)},i=0;i<fe.length;i++){var n=fe[i];e[n]=r.getModel([n,t])}return e}function _r(r,t,e,i,n){var a={};return SS(a,r,e,i,n),t&&O(a,t),a}function nv(r,t,e){t=t||{};var i={},n,a=r.getShallow("rotate"),o=K(r.getShallow("distance"),e?null:5),s=r.getShallow("offset");return n=r.getShallow("position")||(e?null:"inside"),n==="outside"&&(n=t.defaultOutsidePosition||"top"),n!=null&&(i.position=n),s!=null&&(i.offset=s),a!=null&&(a*=Math.PI/180,i.rotation=a),o!=null&&(i.distance=o),i.outsideFill=r.get("color")==="inherit"?t.inheritColor||null:"auto",i}function SS(r,t,e,i,n){e=e||$o;var a=t.ecModel,o=a&&a.option.textStyle,s=wS(t),l;if(s){l={};for(var u in s)if(s.hasOwnProperty(u)){var f=t.getModel(["rich",u]);lv(l[u]={},f,o,e,i,n,!1,!0)}}l&&(r.rich=l);var h=t.get("overflow");h&&(r.overflow=h);var c=t.get("minMargin");c!=null&&(r.margin=c),lv(r,t,o,e,i,n,!0,!1)}function wS(r){for(var t;r&&r!==r.ecModel;){var e=(r.option||$o).rich;if(e){t=t||{};for(var i=dt(e),n=0;n<i.length;n++){var a=i[n];t[a]=1}}r=r.parentModel}return t}var av=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],ov=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],sv=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function lv(r,t,e,i,n,a,o,s){e=!n&&e||$o;var l=i&&i.inheritColor,u=t.getShallow("color"),f=t.getShallow("textBorderColor"),h=K(t.getShallow("opacity"),e.opacity);(u==="inherit"||u==="auto")&&(l?u=l:u=null),(f==="inherit"||f==="auto")&&(l?f=l:f=null),a||(u=u||e.color,f=f||e.textBorderColor),u!=null&&(r.fill=u),f!=null&&(r.stroke=f);var c=K(t.getShallow("textBorderWidth"),e.textBorderWidth);c!=null&&(r.lineWidth=c);var v=K(t.getShallow("textBorderType"),e.textBorderType);v!=null&&(r.lineDash=v);var d=K(t.getShallow("textBorderDashOffset"),e.textBorderDashOffset);d!=null&&(r.lineDashOffset=d),!n&&h==null&&!s&&(h=i&&i.defaultOpacity),h!=null&&(r.opacity=h),!n&&!a&&r.fill==null&&i.inheritColor&&(r.fill=i.inheritColor);for(var g=0;g<av.length;g++){var p=av[g],y=K(t.getShallow(p),e[p]);y!=null&&(r[p]=y)}for(var g=0;g<ov.length;g++){var p=ov[g],y=t.getShallow(p);y!=null&&(r[p]=y)}if(r.verticalAlign==null){var m=t.getShallow("baseline");m!=null&&(r.verticalAlign=m)}if(!o||!i.disableBox){for(var g=0;g<sv.length;g++){var p=sv[g],y=t.getShallow(p);y!=null&&(r[p]=y)}var _=t.getShallow("borderType");_!=null&&(r.borderDash=_),(r.backgroundColor==="auto"||r.backgroundColor==="inherit")&&l&&(r.backgroundColor=l),(r.borderColor==="auto"||r.borderColor==="inherit")&&l&&(r.borderColor=l)}}function bS(r,t){var e=t&&t.getModel("textStyle");return Le([r.fontStyle||e&&e.getShallow("fontStyle")||"",r.fontWeight||e&&e.getShallow("fontWeight")||"",(r.fontSize||e&&e.getShallow("fontSize")||12)+"px",r.fontFamily||e&&e.getShallow("fontFamily")||"sans-serif"].join(" "))}var Rp=_t(),xS=["textStyle","color"],Qs=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],Js=new Lt,TS=function(){function r(){}return r.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(xS):null)},r.prototype.getFont=function(){return bS({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},r.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},i=0;i<Qs.length;i++)e[Qs[i]]=this.getShallow(Qs[i]);return Js.useStyle(e),Js.update(),Js.getBoundingRect()},r}(),Ep=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],CS=Gn(Ep),DS=function(){function r(){}return r.prototype.getLineStyle=function(t){return CS(this,t)},r}(),Op=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],MS=Gn(Op),AS=function(){function r(){}return r.prototype.getItemStyle=function(t,e){return MS(this,t,e)},r}(),gt=function(){function r(t,e,i){this.parentModel=e,this.ecModel=i,this.option=t}return r.prototype.init=function(t,e,i){},r.prototype.mergeOption=function(t,e){it(this.option,t,!0)},r.prototype.get=function(t,e){return t==null?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},r.prototype.getShallow=function(t,e){var i=this.option,n=i==null?i:i[t];if(n==null&&!e){var a=this.parentModel;a&&(n=a.getShallow(t))}return n},r.prototype.getModel=function(t,e){var i=t!=null,n=i?this.parsePath(t):null,a=i?this._doGet(n):this.option;return e=e||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(n)),new r(a,e,this.ecModel)},r.prototype.isEmpty=function(){return this.option==null},r.prototype.restoreData=function(){},r.prototype.clone=function(){var t=this.constructor;return new t(j(this.option))},r.prototype.parsePath=function(t){return typeof t=="string"?t.split("."):t},r.prototype.resolveParentPath=function(t){return t},r.prototype.isAnimationEnabled=function(){if(!$.node&&this.option){if(this.option.animation!=null)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},r.prototype._doGet=function(t,e){var i=this.option;if(!t)return i;for(var n=0;n<t.length&&!(t[n]&&(i=i&&typeof i=="object"?i[t[n]]:null,i==null));n++);return i==null&&e&&(i=e._doGet(this.resolveParentPath(t),e.parentModel)),i},r}();ju(gt);p_(gt);Ee(gt,DS);Ee(gt,AS);Ee(gt,S_);Ee(gt,TS);var LS=Math.round(Math.random()*10);function Ko(r){return[r||"",LS++].join("_")}function IS(r){var t={};r.registerSubTypeDefaulter=function(e,i){var n=Ie(e);t[n.main]=i},r.determineSubType=function(e,i){var n=i.type;if(!n){var a=Ie(e).main;r.hasSubTypes(e)&&t[a]&&(n=t[a](i))}return n}}function PS(r,t){r.topologicalTravel=function(a,o,s,l){if(!a.length)return;var u=e(o),f=u.graph,h=u.noEntryList,c={};for(M(a,function(m){c[m]=!0});h.length;){var v=h.pop(),d=f[v],g=!!c[v];g&&(s.call(l,v,d.originalDeps.slice()),delete c[v]),M(d.successor,g?y:p)}M(c,function(){var m="";throw new Error(m)});function p(m){f[m].entryCount--,f[m].entryCount===0&&h.push(m)}function y(m){c[m]=!0,p(m)}};function e(a){var o={},s=[];return M(a,function(l){var u=i(o,l),f=u.originalDeps=t(l),h=n(f,a);u.entryCount=h.length,u.entryCount===0&&s.push(l),M(h,function(c){lt(u.predecessor,c)<0&&u.predecessor.push(c);var v=i(o,c);lt(v.successor,c)<0&&v.successor.push(l)})}),{graph:o,noEntryList:s}}function i(a,o){return a[o]||(a[o]={predecessor:[],successor:[]}),a[o]}function n(a,o){var s=[];return M(a,function(l){lt(o,l)>=0&&s.push(l)}),s}}function RS(r,t){return it(it({},r,!0),t,!0)}const ES={time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}},OS={time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}};var Co="ZH",df="EN",Pi=df,ja={},pf={},kp=$.domSupported?function(){var r=(document.documentElement.lang||navigator.language||navigator.browserLanguage||Pi).toUpperCase();return r.indexOf(Co)>-1?Co:Pi}():Pi;function Bp(r,t){r=r.toUpperCase(),pf[r]=new gt(t),ja[r]=t}function kS(r){if(G(r)){var t=ja[r.toUpperCase()]||{};return r===Co||r===df?j(t):it(j(t),j(ja[Pi]),!1)}else return it(j(r),j(ja[Pi]),!1)}function BS(r){return pf[r]}function NS(){return pf[Pi]}Bp(df,ES);Bp(Co,OS);var gf=1e3,yf=gf*60,Pn=yf*60,le=Pn*24,uv=le*365,_n={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},xa="{yyyy}-{MM}-{dd}",fv={year:"{yyyy}",month:"{yyyy}-{MM}",day:xa,hour:xa+" "+_n.hour,minute:xa+" "+_n.minute,second:xa+" "+_n.second,millisecond:_n.none},js=["year","month","day","hour","minute","second","millisecond"],Np=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function Je(r,t){return r+="","0000".substr(0,t-r.length)+r}function Ri(r){switch(r){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return r}}function FS(r){return r===Ri(r)}function zS(r){switch(r){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}function Qo(r,t,e,i){var n=Xe(r),a=n[mf(e)](),o=n[Ei(e)]()+1,s=Math.floor((o-1)/3)+1,l=n[Jo(e)](),u=n["get"+(e?"UTC":"")+"Day"](),f=n[Wn(e)](),h=(f-1)%12+1,c=n[jo(e)](),v=n[ts(e)](),d=n[es(e)](),g=f>=12?"pm":"am",p=g.toUpperCase(),y=i instanceof gt?i:BS(i||kp)||NS(),m=y.getModel("time"),_=m.get("month"),S=m.get("monthAbbr"),b=m.get("dayOfWeek"),w=m.get("dayOfWeekAbbr");return(t||"").replace(/{a}/g,g+"").replace(/{A}/g,p+"").replace(/{yyyy}/g,a+"").replace(/{yy}/g,Je(a%100+"",2)).replace(/{Q}/g,s+"").replace(/{MMMM}/g,_[o-1]).replace(/{MMM}/g,S[o-1]).replace(/{MM}/g,Je(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,Je(l,2)).replace(/{d}/g,l+"").replace(/{eeee}/g,b[u]).replace(/{ee}/g,w[u]).replace(/{e}/g,u+"").replace(/{HH}/g,Je(f,2)).replace(/{H}/g,f+"").replace(/{hh}/g,Je(h+"",2)).replace(/{h}/g,h+"").replace(/{mm}/g,Je(c,2)).replace(/{m}/g,c+"").replace(/{ss}/g,Je(v,2)).replace(/{s}/g,v+"").replace(/{SSS}/g,Je(d,3)).replace(/{S}/g,d+"")}function HS(r,t,e,i,n){var a=null;if(G(e))a=e;else if(q(e))a=e(r.value,t,{level:r.level});else{var o=O({},_n);if(r.level>0)for(var s=0;s<js.length;++s)o[js[s]]="{primary|"+o[js[s]]+"}";var l=e?e.inherit===!1?e:at(e,o):o,u=Fp(r.value,n);if(l[u])a=l[u];else if(l.inherit){for(var f=Np.indexOf(u),s=f-1;s>=0;--s)if(l[u]){a=l[u];break}a=a||o.none}if(F(a)){var h=r.level==null?0:r.level>=0?r.level:a.length+r.level;h=Math.min(h,a.length-1),a=a[h]}}return Qo(new Date(r.value),a,n,i)}function Fp(r,t){var e=Xe(r),i=e[Ei(t)]()+1,n=e[Jo(t)](),a=e[Wn(t)](),o=e[jo(t)](),s=e[ts(t)](),l=e[es(t)](),u=l===0,f=u&&s===0,h=f&&o===0,c=h&&a===0,v=c&&n===1,d=v&&i===1;return d?"year":v?"month":c?"day":h?"hour":f?"minute":u?"second":"millisecond"}function hv(r,t,e){var i=vt(r)?Xe(r):r;switch(t=t||Fp(r,e),t){case"year":return i[mf(e)]();case"half-year":return i[Ei(e)]()>=6?1:0;case"quarter":return Math.floor((i[Ei(e)]()+1)/4);case"month":return i[Ei(e)]();case"day":return i[Jo(e)]();case"half-day":return i[Wn(e)]()/24;case"hour":return i[Wn(e)]();case"minute":return i[jo(e)]();case"second":return i[ts(e)]();case"millisecond":return i[es(e)]()}}function mf(r){return r?"getUTCFullYear":"getFullYear"}function Ei(r){return r?"getUTCMonth":"getMonth"}function Jo(r){return r?"getUTCDate":"getDate"}function Wn(r){return r?"getUTCHours":"getHours"}function jo(r){return r?"getUTCMinutes":"getMinutes"}function ts(r){return r?"getUTCSeconds":"getSeconds"}function es(r){return r?"getUTCMilliseconds":"getMilliseconds"}function GS(r){return r?"setUTCFullYear":"setFullYear"}function zp(r){return r?"setUTCMonth":"setMonth"}function Hp(r){return r?"setUTCDate":"setDate"}function Gp(r){return r?"setUTCHours":"setHours"}function Vp(r){return r?"setUTCMinutes":"setMinutes"}function Wp(r){return r?"setUTCSeconds":"setSeconds"}function Up(r){return r?"setUTCMilliseconds":"setMilliseconds"}function Yp(r){if(!Z0(r))return G(r)?r:"-";var t=(r+"").split(".");return t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:"")}function Xp(r,t){return r=(r||"").toLowerCase().replace(/-(.)/g,function(e,i){return i.toUpperCase()}),t&&r&&(r=r.charAt(0).toUpperCase()+r.slice(1)),r}var rs=Sd;function uu(r,t,e){var i="{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}";function n(f){return f&&Le(f)?f:"-"}function a(f){return!!(f!=null&&!isNaN(f)&&isFinite(f))}var o=t==="time",s=r instanceof Date;if(o||s){var l=o?Xe(r):r;if(isNaN(+l)){if(s)return"-"}else return Qo(l,i,e)}if(t==="ordinal")return Rl(r)?n(r):vt(r)&&a(r)?r+"":"-";var u=po(r);return a(u)?Yp(u):Rl(r)?n(r):typeof r=="boolean"?r+"":"-"}var vv=["a","b","c","d","e","f","g"],tl=function(r,t){return"{"+r+(t??"")+"}"};function $p(r,t,e){F(t)||(t=[t]);var i=t.length;if(!i)return"";for(var n=t[0].$vars||[],a=0;a<n.length;a++){var o=vv[a];r=r.replace(tl(o),tl(o,0))}for(var s=0;s<i;s++)for(var l=0;l<n.length;l++){var u=t[s][n[l]];r=r.replace(tl(vv[l],s),e?Gt(u):u)}return r}function VS(r,t){var e=G(r)?{color:r,extraCssText:t}:r||{},i=e.color,n=e.type;t=e.extraCssText;var a=e.renderMode||"html";if(!i)return"";if(a==="html")return n==="subItem"?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Gt(i)+";"+(t||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+Gt(i)+";"+(t||"")+'"></span>';var o=e.markerId||"markerX";return{renderMode:a,content:"{"+o+"|}  ",style:n==="subItem"?{width:4,height:4,borderRadius:2,backgroundColor:i}:{width:10,height:10,borderRadius:5,backgroundColor:i}}}function ti(r,t){return t=t||"transparent",G(r)?r:W(r)&&r.colorStops&&(r.colorStops[0]||{}).color||t}function cv(r,t){if(t==="_blank"||t==="blank"){var e=window.open();e.opener=null,e.location.href=r}else window.open(r,t)}var to=M,WS=["left","right","top","bottom","width","height"],Ta=[["width","left","right"],["height","top","bottom"]];function _f(r,t,e,i,n){var a=0,o=0;i==null&&(i=1/0),n==null&&(n=1/0);var s=0;t.eachChild(function(l,u){var f=l.getBoundingRect(),h=t.childAt(u+1),c=h&&h.getBoundingRect(),v,d;if(r==="horizontal"){var g=f.width+(c?-c.x+f.x:0);v=a+g,v>i||l.newline?(a=0,v=g,o+=s+e,s=f.height):s=Math.max(s,f.height)}else{var p=f.height+(c?-c.y+f.y:0);d=o+p,d>n||l.newline?(a+=s+e,o=0,d=p,s=f.width):s=Math.max(s,f.width)}l.newline||(l.x=a,l.y=o,l.markRedraw(),r==="horizontal"?a=v+e:o=d+e)})}var Rn=_f;St(_f,"vertical");St(_f,"horizontal");function Oi(r,t,e){e=rs(e||0);var i=t.width,n=t.height,a=ct(r.left,i),o=ct(r.top,n),s=ct(r.right,i),l=ct(r.bottom,n),u=ct(r.width,i),f=ct(r.height,n),h=e[2]+e[0],c=e[1]+e[3],v=r.aspect;switch(isNaN(u)&&(u=i-s-c-a),isNaN(f)&&(f=n-l-h-o),v!=null&&(isNaN(u)&&isNaN(f)&&(v>i/n?u=i*.8:f=n*.8),isNaN(u)&&(u=v*f),isNaN(f)&&(f=u/v)),isNaN(a)&&(a=i-s-u-c),isNaN(o)&&(o=n-l-f-h),r.left||r.right){case"center":a=i/2-u/2-e[3];break;case"right":a=i-u-c;break}switch(r.top||r.bottom){case"middle":case"center":o=n/2-f/2-e[0];break;case"bottom":o=n-f-h;break}a=a||0,o=o||0,isNaN(u)&&(u=i-c-a-(s||0)),isNaN(f)&&(f=n-h-o-(l||0));var d=new nt(a+e[3],o+e[0],u,f);return d.margin=e,d}function Un(r){var t=r.layoutMode||r.constructor.layoutMode;return W(t)?t:t?{type:t}:null}function ki(r,t,e){var i=e&&e.ignoreSize;!F(i)&&(i=[i,i]);var n=o(Ta[0],0),a=o(Ta[1],1);u(Ta[0],r,n),u(Ta[1],r,a);function o(f,h){var c={},v=0,d={},g=0,p=2;if(to(f,function(_){d[_]=r[_]}),to(f,function(_){s(t,_)&&(c[_]=d[_]=t[_]),l(c,_)&&v++,l(d,_)&&g++}),i[h])return l(t,f[1])?d[f[2]]=null:l(t,f[2])&&(d[f[1]]=null),d;if(g===p||!v)return d;if(v>=p)return c;for(var y=0;y<f.length;y++){var m=f[y];if(!s(c,m)&&s(r,m)){c[m]=r[m];break}}return c}function s(f,h){return f.hasOwnProperty(h)}function l(f,h){return f[h]!=null&&f[h]!=="auto"}function u(f,h,c){to(f,function(v){h[v]=c[v]})}}function is(r){return US({},r)}function US(r,t){return t&&r&&to(WS,function(e){t.hasOwnProperty(e)&&(r[e]=t[e])}),r}var YS=_t(),ot=function(r){N(t,r);function t(e,i,n){var a=r.call(this,e,i,n)||this;return a.uid=Ko("ec_cpt_model"),a}return t.prototype.init=function(e,i,n){this.mergeDefaultAndTheme(e,n)},t.prototype.mergeDefaultAndTheme=function(e,i){var n=Un(this),a=n?is(e):{},o=i.getTheme();it(e,o.get(this.mainType)),it(e,this.getDefaultOption()),n&&ki(e,a,n)},t.prototype.mergeOption=function(e,i){it(this.option,e,!0);var n=Un(this);n&&ki(this.option,e,n)},t.prototype.optionUpdated=function(e,i){},t.prototype.getDefaultOption=function(){var e=this.constructor;if(!v_(e))return e.defaultOption;var i=YS(this);if(!i.defaultOption){for(var n=[],a=e;a;){var o=a.prototype.defaultOption;o&&n.push(o),a=a.superClass}for(var s={},l=n.length-1;l>=0;l--)s=it(s,n[l],!0);i.defaultOption=s}return i.defaultOption},t.prototype.getReferringComponents=function(e,i){var n=e+"Index",a=e+"Id";return ea(this.ecModel,e,{index:this.get(n,!0),id:this.get(a,!0)},i)},t.prototype.getBoxLayoutParams=function(){var e=this;return{left:e.get("left"),top:e.get("top"),right:e.get("right"),bottom:e.get("bottom"),width:e.get("width"),height:e.get("height")}},t.prototype.getZLevelKey=function(){return""},t.prototype.setZLevel=function(e){this.option.zlevel=e},t.protoInitialize=function(){var e=t.prototype;e.type="component",e.id="",e.name="",e.mainType="",e.subType="",e.componentIndex=0}(),t}(gt);Jd(ot,gt);Fo(ot);IS(ot);PS(ot,XS);function XS(r){var t=[];return M(ot.getClassesByMainType(r),function(e){t=t.concat(e.dependencies||e.prototype.dependencies||[])}),t=V(t,function(e){return Ie(e).main}),r!=="dataset"&&lt(t,"dataset")<=0&&t.unshift("dataset"),t}var Zp="";typeof navigator<"u"&&(Zp=navigator.platform||"");var vi="rgba(0, 0, 0, 0.2)";const $S={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:vi,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:vi,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:vi,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:vi,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:vi,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:vi,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:Zp.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1};var qp=Q(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),he="original",Zt="arrayRows",we="objectRows",ke="keyedColumns",dr="typedArray",Kp="unknown",Ue="column",Gi="row",Ct={Must:1,Might:2,Not:3},Qp=_t();function ZS(r){Qp(r).datasetMap=Q()}function qS(r,t,e){var i={},n=Sf(t);if(!n||!r)return i;var a=[],o=[],s=t.ecModel,l=Qp(s).datasetMap,u=n.uid+"_"+e.seriesLayoutBy,f,h;r=r.slice(),M(r,function(g,p){var y=W(g)?g:r[p]={name:g};y.type==="ordinal"&&f==null&&(f=p,h=d(y)),i[y.name]=[]});var c=l.get(u)||l.set(u,{categoryWayDim:h,valueWayDim:0});M(r,function(g,p){var y=g.name,m=d(g);if(f==null){var _=c.valueWayDim;v(i[y],_,m),v(o,_,m),c.valueWayDim+=m}else if(f===p)v(i[y],0,m),v(a,0,m);else{var _=c.categoryWayDim;v(i[y],_,m),v(o,_,m),c.categoryWayDim+=m}});function v(g,p,y){for(var m=0;m<y;m++)g.push(p+m)}function d(g){var p=g.dimsDef;return p?p.length:1}return a.length&&(i.itemName=a),o.length&&(i.seriesName=o),i}function KS(r,t,e){var i={},n=Sf(r);if(!n)return i;var a=t.sourceFormat,o=t.dimensionsDefine,s;(a===we||a===ke)&&M(o,function(f,h){(W(f)?f.name:f)==="name"&&(s=h)});var l=function(){for(var f={},h={},c=[],v=0,d=Math.min(5,e);v<d;v++){var g=jp(t.data,a,t.seriesLayoutBy,o,t.startIndex,v);c.push(g);var p=g===Ct.Not;if(p&&f.v==null&&v!==s&&(f.v=v),(f.n==null||f.n===f.v||!p&&c[f.n]===Ct.Not)&&(f.n=v),y(f)&&c[f.n]!==Ct.Not)return f;p||(g===Ct.Might&&h.v==null&&v!==s&&(h.v=v),(h.n==null||h.n===h.v)&&(h.n=v))}function y(m){return m.v!=null&&m.n!=null}return y(f)?f:y(h)?h:null}();if(l){i.value=[l.v];var u=s??l.n;i.itemName=[u],i.seriesName=[u]}return i}function Sf(r){var t=r.get("data",!0);if(!t)return ea(r.ecModel,"dataset",{index:r.get("datasetIndex",!0),id:r.get("datasetId",!0)},ye).models[0]}function QS(r){return!r.get("transform",!0)&&!r.get("fromTransformResult",!0)?[]:ea(r.ecModel,"dataset",{index:r.get("fromDatasetIndex",!0),id:r.get("fromDatasetId",!0)},ye).models}function Jp(r,t){return jp(r.data,r.sourceFormat,r.seriesLayoutBy,r.dimensionsDefine,r.startIndex,t)}function jp(r,t,e,i,n,a){var o,s=5;if($t(r))return Ct.Not;var l,u;if(i){var f=i[a];W(f)?(l=f.name,u=f.type):G(f)&&(l=f)}if(u!=null)return u==="ordinal"?Ct.Must:Ct.Not;if(t===Zt){var h=r;if(e===Gi){for(var c=h[a],v=0;v<(c||[]).length&&v<s;v++)if((o=S(c[n+v]))!=null)return o}else for(var v=0;v<h.length&&v<s;v++){var d=h[n+v];if(d&&(o=S(d[a]))!=null)return o}}else if(t===we){var g=r;if(!l)return Ct.Not;for(var v=0;v<g.length&&v<s;v++){var p=g[v];if(p&&(o=S(p[l]))!=null)return o}}else if(t===ke){var y=r;if(!l)return Ct.Not;var c=y[l];if(!c||$t(c))return Ct.Not;for(var v=0;v<c.length&&v<s;v++)if((o=S(c[v]))!=null)return o}else if(t===he)for(var m=r,v=0;v<m.length&&v<s;v++){var p=m[v],_=ta(p);if(!F(_))return Ct.Not;if((o=S(_[a]))!=null)return o}function S(b){var w=G(b);if(b!=null&&Number.isFinite(Number(b))&&b!=="")return w?Ct.Might:Ct.Not;if(w&&b!=="-")return Ct.Must}return Ct.Not}var JS=Q();function jS(r,t,e){var i=JS.get(t);if(!i)return e;var n=i(r);return n?e.concat(n):e}var dv=_t();_t();var wf=function(){function r(){}return r.prototype.getColorFromPalette=function(t,e,i){var n=Nt(this.get("color",!0)),a=this.get("colorLayer",!0);return ew(this,dv,n,a,t,e,i)},r.prototype.clearColorPalette=function(){rw(this,dv)},r}();function tw(r,t){for(var e=r.length,i=0;i<e;i++)if(r[i].length>t)return r[i];return r[e-1]}function ew(r,t,e,i,n,a,o){a=a||r;var s=t(a),l=s.paletteIdx||0,u=s.paletteNameMap=s.paletteNameMap||{};if(u.hasOwnProperty(n))return u[n];var f=o==null||!i?e:tw(i,o);if(f=f||e,!(!f||!f.length)){var h=f[l];return n&&(u[n]=h),s.paletteIdx=(l+1)%f.length,h}}function rw(r,t){t(r).paletteIdx=0,t(r).paletteNameMap={}}var Ca,en,pv,gv="\0_ec_inner",iw=1,bf=function(r){N(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.init=function(e,i,n,a,o,s){a=a||{},this.option=null,this._theme=new gt(a),this._locale=new gt(o),this._optionManager=s},t.prototype.setOption=function(e,i,n){var a=_v(i);this._optionManager.setOption(e,n,a),this._resetOption(null,a)},t.prototype.resetOption=function(e,i){return this._resetOption(e,_v(i))},t.prototype._resetOption=function(e,i){var n=!1,a=this._optionManager;if(!e||e==="recreate"){var o=a.mountOption(e==="recreate");!this.option||e==="recreate"?pv(this,o):(this.restoreData(),this._mergeOption(o,i)),n=!0}if((e==="timeline"||e==="media")&&this.restoreData(),!e||e==="recreate"||e==="timeline"){var s=a.getTimelineOption(this);s&&(n=!0,this._mergeOption(s,i))}if(!e||e==="recreate"||e==="media"){var l=a.getMediaOption(this);l.length&&M(l,function(u){n=!0,this._mergeOption(u,i)},this)}return n},t.prototype.mergeOption=function(e){this._mergeOption(e,null)},t.prototype._mergeOption=function(e,i){var n=this.option,a=this._componentsMap,o=this._componentsCount,s=[],l=Q(),u=i&&i.replaceMergeMainTypeMap;ZS(this),M(e,function(h,c){h!=null&&(ot.hasClass(c)?c&&(s.push(c),l.set(c,!0)):n[c]=n[c]==null?j(h):it(n[c],h,!0))}),u&&u.each(function(h,c){ot.hasClass(c)&&!l.get(c)&&(s.push(c),l.set(c,!0))}),ot.topologicalTravel(s,ot.getAllClassMainTypes(),f,this);function f(h){var c=jS(this,h,Nt(e[h])),v=a.get(h),d=v?u&&u.get(h)?"replaceMerge":"normalMerge":"replaceAll",g=Q0(v,c,d);n_(g,h,ot),n[h]=null,a.set(h,null),o.set(h,0);var p=[],y=[],m=0,_;M(g,function(S,b){var w=S.existing,x=S.newOption;if(!x)w&&(w.mergeOption({},this),w.optionUpdated({},!1));else{var C=h==="series",T=ot.getClass(h,S.keyInfo.subType,!C);if(!T)return;if(h==="tooltip"){if(_)return;_=!0}if(w&&w.constructor===T)w.name=S.keyInfo.name,w.mergeOption(x,this),w.optionUpdated(x,!1);else{var D=O({componentIndex:b},S.keyInfo);w=new T(x,this,this,D),O(w,D),S.brandNew&&(w.__requireNewView=!0),w.init(x,this,this),w.optionUpdated(null,!0)}}w?(p.push(w.option),y.push(w),m++):(p.push(void 0),y.push(void 0))},this),n[h]=p,a.set(h,y),o.set(h,m),h==="series"&&Ca(this)}this._seriesIndices||Ca(this)},t.prototype.getOption=function(){var e=j(this.option);return M(e,function(i,n){if(ot.hasClass(n)){for(var a=Nt(i),o=a.length,s=!1,l=o-1;l>=0;l--)a[l]&&!Hn(a[l])?s=!0:(a[l]=null,!s&&o--);a.length=o,e[n]=a}}),delete e[gv],e},t.prototype.getTheme=function(){return this._theme},t.prototype.getLocaleModel=function(){return this._locale},t.prototype.setUpdatePayload=function(e){this._payload=e},t.prototype.getUpdatePayload=function(){return this._payload},t.prototype.getComponent=function(e,i){var n=this._componentsMap.get(e);if(n){var a=n[i||0];if(a)return a;if(i==null){for(var o=0;o<n.length;o++)if(n[o])return n[o]}}},t.prototype.queryComponents=function(e){var i=e.mainType;if(!i)return[];var n=e.index,a=e.id,o=e.name,s=this._componentsMap.get(i);if(!s||!s.length)return[];var l;return n!=null?(l=[],M(Nt(n),function(u){s[u]&&l.push(s[u])})):a!=null?l=yv("id",a,s):o!=null?l=yv("name",o,s):l=Mt(s,function(u){return!!u}),mv(l,e)},t.prototype.findComponents=function(e){var i=e.query,n=e.mainType,a=s(i),o=a?this.queryComponents(a):Mt(this._componentsMap.get(n),function(u){return!!u});return l(mv(o,e));function s(u){var f=n+"Index",h=n+"Id",c=n+"Name";return u&&(u[f]!=null||u[h]!=null||u[c]!=null)?{mainType:n,index:u[f],id:u[h],name:u[c]}:null}function l(u){return e.filter?Mt(u,e.filter):u}},t.prototype.eachComponent=function(e,i,n){var a=this._componentsMap;if(q(e)){var o=i,s=e;a.each(function(h,c){for(var v=0;h&&v<h.length;v++){var d=h[v];d&&s.call(o,c,d,d.componentIndex)}})}else for(var l=G(e)?a.get(e):W(e)?this.findComponents(e):null,u=0;l&&u<l.length;u++){var f=l[u];f&&i.call(n,f,f.componentIndex)}},t.prototype.getSeriesByName=function(e){var i=Pe(e,null);return Mt(this._componentsMap.get("series"),function(n){return!!n&&i!=null&&n.name===i})},t.prototype.getSeriesByIndex=function(e){return this._componentsMap.get("series")[e]},t.prototype.getSeriesByType=function(e){return Mt(this._componentsMap.get("series"),function(i){return!!i&&i.subType===e})},t.prototype.getSeries=function(){return Mt(this._componentsMap.get("series"),function(e){return!!e})},t.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},t.prototype.eachSeries=function(e,i){en(this),M(this._seriesIndices,function(n){var a=this._componentsMap.get("series")[n];e.call(i,a,n)},this)},t.prototype.eachRawSeries=function(e,i){M(this._componentsMap.get("series"),function(n){n&&e.call(i,n,n.componentIndex)})},t.prototype.eachSeriesByType=function(e,i,n){en(this),M(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];o.subType===e&&i.call(n,o,a)},this)},t.prototype.eachRawSeriesByType=function(e,i,n){return M(this.getSeriesByType(e),i,n)},t.prototype.isSeriesFiltered=function(e){return en(this),this._seriesIndicesMap.get(e.componentIndex)==null},t.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},t.prototype.filterSeries=function(e,i){en(this);var n=[];M(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];e.call(i,o,a)&&n.push(a)},this),this._seriesIndices=n,this._seriesIndicesMap=Q(n)},t.prototype.restoreData=function(e){Ca(this);var i=this._componentsMap,n=[];i.each(function(a,o){ot.hasClass(o)&&n.push(o)}),ot.topologicalTravel(n,ot.getAllClassMainTypes(),function(a){M(i.get(a),function(o){o&&(a!=="series"||!nw(o,e))&&o.restoreData()})})},t.internalField=function(){Ca=function(e){var i=e._seriesIndices=[];M(e._componentsMap.get("series"),function(n){n&&i.push(n.componentIndex)}),e._seriesIndicesMap=Q(i)},en=function(e){},pv=function(e,i){e.option={},e.option[gv]=iw,e._componentsMap=Q({series:[]}),e._componentsCount=Q();var n=i.aria;W(n)&&n.enabled==null&&(n.enabled=!0),aw(i,e._theme.option),it(i,$S,!1),e._mergeOption(i,null)}}(),t}(gt);function nw(r,t){if(t){var e=t.seriesIndex,i=t.seriesId,n=t.seriesName;return e!=null&&r.componentIndex!==e||i!=null&&r.id!==i||n!=null&&r.name!==n}}function aw(r,t){var e=r.color&&!r.colorLayer;M(t,function(i,n){n==="colorLayer"&&e||ot.hasClass(n)||(typeof i=="object"?r[n]=r[n]?it(r[n],i,!1):j(i):r[n]==null&&(r[n]=i))})}function yv(r,t,e){if(F(t)){var i=Q();return M(t,function(a){if(a!=null){var o=Pe(a,null);o!=null&&i.set(a,!0)}}),Mt(e,function(a){return a&&i.get(a[r])})}else{var n=Pe(t,null);return Mt(e,function(a){return a&&n!=null&&a[r]===n})}}function mv(r,t){return t.hasOwnProperty("subType")?Mt(r,function(e){return e&&e.subType===t.subType}):r}function _v(r){var t=Q();return r&&M(Nt(r.replaceMerge),function(e){t.set(e,!0)}),{replaceMergeMainTypeMap:t}}Ee(bf,wf);var ow=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],tg=function(){function r(t){M(ow,function(e){this[e]=ht(t[e],t)},this)}return r}(),el={},xf=function(){function r(){this._coordinateSystems=[]}return r.prototype.create=function(t,e){var i=[];M(el,function(n,a){var o=n.create(t,e);i=i.concat(o||[])}),this._coordinateSystems=i},r.prototype.update=function(t,e){M(this._coordinateSystems,function(i){i.update&&i.update(t,e)})},r.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},r.register=function(t,e){el[t]=e},r.get=function(t){return el[t]},r}(),sw=/^(min|max)?(.+)$/,lw=function(){function r(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}return r.prototype.setOption=function(t,e,i){t&&(M(Nt(t.series),function(o){o&&o.data&&$t(o.data)&&El(o.data)}),M(Nt(t.dataset),function(o){o&&o.source&&$t(o.source)&&El(o.source)})),t=j(t);var n=this._optionBackup,a=uw(t,e,!n);this._newBaseOption=a.baseOption,n?(a.timelineOptions.length&&(n.timelineOptions=a.timelineOptions),a.mediaList.length&&(n.mediaList=a.mediaList),a.mediaDefault&&(n.mediaDefault=a.mediaDefault)):this._optionBackup=a},r.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],j(t?e.baseOption:this._newBaseOption)},r.prototype.getTimelineOption=function(t){var e,i=this._timelineOptions;if(i.length){var n=t.getComponent("timeline");n&&(e=j(i[n.getCurrentIndex()]))}return e},r.prototype.getMediaOption=function(t){var e=this._api.getWidth(),i=this._api.getHeight(),n=this._mediaList,a=this._mediaDefault,o=[],s=[];if(!n.length&&!a)return s;for(var l=0,u=n.length;l<u;l++)fw(n[l].query,e,i)&&o.push(l);return!o.length&&a&&(o=[-1]),o.length&&!vw(o,this._currentMediaIndices)&&(s=V(o,function(f){return j(f===-1?a.option:n[f].option)})),this._currentMediaIndices=o,s},r}();function uw(r,t,e){var i=[],n,a,o=r.baseOption,s=r.timeline,l=r.options,u=r.media,f=!!r.media,h=!!(l||s||o&&o.timeline);o?(a=o,a.timeline||(a.timeline=s)):((h||f)&&(r.options=r.media=null),a=r),f&&F(u)&&M(u,function(v){v&&v.option&&(v.query?i.push(v):n||(n=v))}),c(a),M(l,function(v){return c(v)}),M(i,function(v){return c(v.option)});function c(v){M(t,function(d){d(v,e)})}return{baseOption:a,timelineOptions:l||[],mediaDefault:n,mediaList:i}}function fw(r,t,e){var i={width:t,height:e,aspectratio:t/e},n=!0;return M(r,function(a,o){var s=o.match(sw);if(!(!s||!s[1]||!s[2])){var l=s[1],u=s[2].toLowerCase();hw(i[u],a,l)||(n=!1)}}),n}function hw(r,t,e){return e==="min"?r>=t:e==="max"?r<=t:r===t}function vw(r,t){return r.join(",")===t.join(",")}var ve=M,Yn=W,Sv=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function rl(r){var t=r&&r.itemStyle;if(t)for(var e=0,i=Sv.length;e<i;e++){var n=Sv[e],a=t.normal,o=t.emphasis;a&&a[n]&&(r[n]=r[n]||{},r[n].normal?it(r[n].normal,a[n]):r[n].normal=a[n],a[n]=null),o&&o[n]&&(r[n]=r[n]||{},r[n].emphasis?it(r[n].emphasis,o[n]):r[n].emphasis=o[n],o[n]=null)}}function Et(r,t,e){if(r&&r[t]&&(r[t].normal||r[t].emphasis)){var i=r[t].normal,n=r[t].emphasis;i&&(e?(r[t].normal=r[t].emphasis=null,at(r[t],i)):r[t]=i),n&&(r.emphasis=r.emphasis||{},r.emphasis[t]=n,n.focus&&(r.emphasis.focus=n.focus),n.blurScope&&(r.emphasis.blurScope=n.blurScope))}}function Sn(r){Et(r,"itemStyle"),Et(r,"lineStyle"),Et(r,"areaStyle"),Et(r,"label"),Et(r,"labelLine"),Et(r,"upperLabel"),Et(r,"edgeLabel")}function mt(r,t){var e=Yn(r)&&r[t],i=Yn(e)&&e.textStyle;if(i)for(var n=0,a=bh.length;n<a;n++){var o=bh[n];i.hasOwnProperty(o)&&(e[o]=i[o])}}function ie(r){r&&(Sn(r),mt(r,"label"),r.emphasis&&mt(r.emphasis,"label"))}function cw(r){if(Yn(r)){rl(r),Sn(r),mt(r,"label"),mt(r,"upperLabel"),mt(r,"edgeLabel"),r.emphasis&&(mt(r.emphasis,"label"),mt(r.emphasis,"upperLabel"),mt(r.emphasis,"edgeLabel"));var t=r.markPoint;t&&(rl(t),ie(t));var e=r.markLine;e&&(rl(e),ie(e));var i=r.markArea;i&&ie(i);var n=r.data;if(r.type==="graph"){n=n||r.nodes;var a=r.links||r.edges;if(a&&!$t(a))for(var o=0;o<a.length;o++)ie(a[o]);M(r.categories,function(u){Sn(u)})}if(n&&!$t(n))for(var o=0;o<n.length;o++)ie(n[o]);if(t=r.markPoint,t&&t.data)for(var s=t.data,o=0;o<s.length;o++)ie(s[o]);if(e=r.markLine,e&&e.data)for(var l=e.data,o=0;o<l.length;o++)F(l[o])?(ie(l[o][0]),ie(l[o][1])):ie(l[o]);r.type==="gauge"?(mt(r,"axisLabel"),mt(r,"title"),mt(r,"detail")):r.type==="treemap"?(Et(r.breadcrumb,"itemStyle"),M(r.levels,function(u){Sn(u)})):r.type==="tree"&&Sn(r.leaves)}}function ze(r){return F(r)?r:r?[r]:[]}function wv(r){return(F(r)?r[0]:r)||{}}function dw(r,t){ve(ze(r.series),function(i){Yn(i)&&cw(i)});var e=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&e.push("valueAxis","categoryAxis","logAxis","timeAxis"),ve(e,function(i){ve(ze(r[i]),function(n){n&&(mt(n,"axisLabel"),mt(n.axisPointer,"label"))})}),ve(ze(r.parallel),function(i){var n=i&&i.parallelAxisDefault;mt(n,"axisLabel"),mt(n&&n.axisPointer,"label")}),ve(ze(r.calendar),function(i){Et(i,"itemStyle"),mt(i,"dayLabel"),mt(i,"monthLabel"),mt(i,"yearLabel")}),ve(ze(r.radar),function(i){mt(i,"name"),i.name&&i.axisName==null&&(i.axisName=i.name,delete i.name),i.nameGap!=null&&i.axisNameGap==null&&(i.axisNameGap=i.nameGap,delete i.nameGap)}),ve(ze(r.geo),function(i){Yn(i)&&(ie(i),ve(ze(i.regions),function(n){ie(n)}))}),ve(ze(r.timeline),function(i){ie(i),Et(i,"label"),Et(i,"itemStyle"),Et(i,"controlStyle",!0);var n=i.data;F(n)&&M(n,function(a){W(a)&&(Et(a,"label"),Et(a,"itemStyle"))})}),ve(ze(r.toolbox),function(i){Et(i,"iconStyle"),ve(i.feature,function(n){Et(n,"iconStyle")})}),mt(wv(r.axisPointer),"label"),mt(wv(r.tooltip).axisPointer,"label")}function pw(r,t){for(var e=t.split(","),i=r,n=0;n<e.length&&(i=i&&i[e[n]],i!=null);n++);return i}function gw(r,t,e,i){for(var n=t.split(","),a=r,o,s=0;s<n.length-1;s++)o=n[s],a[o]==null&&(a[o]={}),a=a[o];a[n[s]]==null&&(a[n[s]]=e)}function bv(r){r&&M(yw,function(t){t[0]in r&&!(t[1]in r)&&(r[t[1]]=r[t[0]])})}var yw=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],mw=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],il=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function rn(r){var t=r&&r.itemStyle;if(t)for(var e=0;e<il.length;e++){var i=il[e][1],n=il[e][0];t[i]!=null&&(t[n]=t[i])}}function xv(r){r&&r.alignTo==="edge"&&r.margin!=null&&r.edgeDistance==null&&(r.edgeDistance=r.margin)}function Tv(r){r&&r.downplay&&!r.blur&&(r.blur=r.downplay)}function _w(r){r&&r.focusNodeAdjacency!=null&&(r.emphasis=r.emphasis||{},r.emphasis.focus==null&&(r.emphasis.focus="adjacency"))}function eg(r,t){if(r)for(var e=0;e<r.length;e++)t(r[e]),r[e]&&eg(r[e].children,t)}function rg(r,t){dw(r,t),r.series=Nt(r.series),M(r.series,function(e){if(W(e)){var i=e.type;if(i==="line")e.clipOverflow!=null&&(e.clip=e.clipOverflow);else if(i==="pie"||i==="gauge"){e.clockWise!=null&&(e.clockwise=e.clockWise),xv(e.label);var n=e.data;if(n&&!$t(n))for(var a=0;a<n.length;a++)xv(n[a]);e.hoverOffset!=null&&(e.emphasis=e.emphasis||{},(e.emphasis.scaleSize=null)&&(e.emphasis.scaleSize=e.hoverOffset))}else if(i==="gauge"){var o=pw(e,"pointer.color");o!=null&&gw(e,"itemStyle.color",o)}else if(i==="bar"){rn(e),rn(e.backgroundStyle),rn(e.emphasis);var n=e.data;if(n&&!$t(n))for(var a=0;a<n.length;a++)typeof n[a]=="object"&&(rn(n[a]),rn(n[a]&&n[a].emphasis))}else if(i==="sunburst"){var s=e.highlightPolicy;s&&(e.emphasis=e.emphasis||{},e.emphasis.focus||(e.emphasis.focus=s)),Tv(e),eg(e.data,Tv)}else i==="graph"||i==="sankey"?_w(e):i==="map"&&(e.mapType&&!e.map&&(e.map=e.mapType),e.mapLocation&&at(e,e.mapLocation));e.hoverAnimation!=null&&(e.emphasis=e.emphasis||{},e.emphasis&&e.emphasis.scale==null&&(e.emphasis.scale=e.hoverAnimation)),bv(e)}}),r.dataRange&&(r.visualMap=r.dataRange),M(mw,function(e){var i=r[e];i&&(F(i)||(i=[i]),M(i,function(n){bv(n)}))})}function Sw(r){var t=Q();r.eachSeries(function(e){var i=e.get("stack");if(i){var n=t.get(i)||t.set(i,[]),a=e.getData(),o={stackResultDimension:a.getCalculationInfo("stackResultDimension"),stackedOverDimension:a.getCalculationInfo("stackedOverDimension"),stackedDimension:a.getCalculationInfo("stackedDimension"),stackedByDimension:a.getCalculationInfo("stackedByDimension"),isStackedByIndex:a.getCalculationInfo("isStackedByIndex"),data:a,seriesModel:e};if(!o.stackedDimension||!(o.isStackedByIndex||o.stackedByDimension))return;n.length&&a.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(o)}}),t.each(ww)}function ww(r){M(r,function(t,e){var i=[],n=[NaN,NaN],a=[t.stackResultDimension,t.stackedOverDimension],o=t.data,s=t.isStackedByIndex,l=t.seriesModel.get("stackStrategy")||"samesign";o.modify(a,function(u,f,h){var c=o.get(t.stackedDimension,h);if(isNaN(c))return n;var v,d;s?d=o.getRawIndex(h):v=o.get(t.stackedByDimension,h);for(var g=NaN,p=e-1;p>=0;p--){var y=r[p];if(s||(d=y.data.rawIndexOf(y.stackedByDimension,v)),d>=0){var m=y.data.getByRawIndex(y.stackResultDimension,d);if(l==="all"||l==="positive"&&m>0||l==="negative"&&m<0||l==="samesign"&&c>=0&&m>0||l==="samesign"&&c<=0&&m<0){c=Y0(c,m),g=m;break}}}return i[0]=c,i[1]=g,i})})}var ns=function(){function r(t){this.data=t.data||(t.sourceFormat===ke?{}:[]),this.sourceFormat=t.sourceFormat||Kp,this.seriesLayoutBy=t.seriesLayoutBy||Ue,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var i=0;i<e.length;i++){var n=e[i];n.type==null&&Jp(this,i)===Ct.Must&&(n.type="ordinal")}}return r}();function Tf(r){return r instanceof ns}function fu(r,t,e){e=e||ng(r);var i=t.seriesLayoutBy,n=xw(r,e,i,t.sourceHeader,t.dimensions),a=new ns({data:r,sourceFormat:e,seriesLayoutBy:i,dimensionsDefine:n.dimensionsDefine,startIndex:n.startIndex,dimensionsDetectedCount:n.dimensionsDetectedCount,metaRawOption:j(t)});return a}function ig(r){return new ns({data:r,sourceFormat:$t(r)?dr:he})}function bw(r){return new ns({data:r.data,sourceFormat:r.sourceFormat,seriesLayoutBy:r.seriesLayoutBy,dimensionsDefine:j(r.dimensionsDefine),startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount})}function ng(r){var t=Kp;if($t(r))t=dr;else if(F(r)){r.length===0&&(t=Zt);for(var e=0,i=r.length;e<i;e++){var n=r[e];if(n!=null){if(F(n)||$t(n)){t=Zt;break}else if(W(n)){t=we;break}}}}else if(W(r)){for(var a in r)if(Qr(r,a)&&Xt(r[a])){t=ke;break}}return t}function xw(r,t,e,i,n){var a,o;if(!r)return{dimensionsDefine:Cv(n),startIndex:o,dimensionsDetectedCount:a};if(t===Zt){var s=r;i==="auto"||i==null?Dv(function(u){u!=null&&u!=="-"&&(G(u)?o==null&&(o=1):o=0)},e,s,10):o=vt(i)?i:i?1:0,!n&&o===1&&(n=[],Dv(function(u,f){n[f]=u!=null?u+"":""},e,s,1/0)),a=n?n.length:e===Gi?s.length:s[0]?s[0].length:null}else if(t===we)n||(n=Tw(r));else if(t===ke)n||(n=[],M(r,function(u,f){n.push(f)}));else if(t===he){var l=ta(r[0]);a=F(l)&&l.length||1}return{startIndex:o,dimensionsDefine:Cv(n),dimensionsDetectedCount:a}}function Tw(r){for(var t=0,e;t<r.length&&!(e=r[t++]););if(e)return dt(e)}function Cv(r){if(r){var t=Q();return V(r,function(e,i){e=W(e)?e:{name:e};var n={name:e.name,displayName:e.displayName,type:e.type};if(n.name==null)return n;n.name+="",n.displayName==null&&(n.displayName=n.name);var a=t.get(n.name);return a?n.name+="-"+a.count++:t.set(n.name,{count:1}),n})}}function Dv(r,t,e,i){if(t===Gi)for(var n=0;n<e.length&&n<i;n++)r(e[n]?e[n][0]:null,n);else for(var a=e[0]||[],n=0;n<a.length&&n<i;n++)r(a[n],n)}function ag(r){var t=r.sourceFormat;return t===we||t===ke}var Fr,zr,Hr,Mv,Av,og=function(){function r(t,e){var i=Tf(t)?t:ig(t);this._source=i;var n=this._data=i.data;i.sourceFormat===dr&&(this._offset=0,this._dimSize=e,this._data=n),Av(this,n,i)}return r.prototype.getSource=function(){return this._source},r.prototype.count=function(){return 0},r.prototype.getItem=function(t,e){},r.prototype.appendData=function(t){},r.prototype.clean=function(){},r.protoInitialize=function(){var t=r.prototype;t.pure=!1,t.persistent=!0}(),r.internalField=function(){var t;Av=function(o,s,l){var u=l.sourceFormat,f=l.seriesLayoutBy,h=l.startIndex,c=l.dimensionsDefine,v=Mv[Cf(u,f)];if(O(o,v),u===dr)o.getItem=e,o.count=n,o.fillStorage=i;else{var d=sg(u,f);o.getItem=ht(d,null,s,h,c);var g=lg(u,f);o.count=ht(g,null,s,h,c)}};var e=function(o,s){o=o-this._offset,s=s||[];for(var l=this._data,u=this._dimSize,f=u*o,h=0;h<u;h++)s[h]=l[f+h];return s},i=function(o,s,l,u){for(var f=this._data,h=this._dimSize,c=0;c<h;c++){for(var v=u[c],d=v[0]==null?1/0:v[0],g=v[1]==null?-1/0:v[1],p=s-o,y=l[c],m=0;m<p;m++){var _=f[m*h+c];y[o+m]=_,_<d&&(d=_),_>g&&(g=_)}v[0]=d,v[1]=g}},n=function(){return this._data?this._data.length/this._dimSize:0};Mv=(t={},t[Zt+"_"+Ue]={pure:!0,appendData:a},t[Zt+"_"+Gi]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},t[we]={pure:!0,appendData:a},t[ke]={pure:!0,appendData:function(o){var s=this._data;M(o,function(l,u){for(var f=s[u]||(s[u]=[]),h=0;h<(l||[]).length;h++)f.push(l[h])})}},t[he]={appendData:a},t[dr]={persistent:!1,pure:!0,appendData:function(o){this._data=o},clean:function(){this._offset+=this.count(),this._data=null}},t);function a(o){for(var s=0;s<o.length;s++)this._data.push(o[s])}}(),r}(),Lv=function(r,t,e,i){return r[i]},Cw=(Fr={},Fr[Zt+"_"+Ue]=function(r,t,e,i){return r[i+t]},Fr[Zt+"_"+Gi]=function(r,t,e,i,n){i+=t;for(var a=n||[],o=r,s=0;s<o.length;s++){var l=o[s];a[s]=l?l[i]:null}return a},Fr[we]=Lv,Fr[ke]=function(r,t,e,i,n){for(var a=n||[],o=0;o<e.length;o++){var s=e[o].name,l=r[s];a[o]=l?l[i]:null}return a},Fr[he]=Lv,Fr);function sg(r,t){var e=Cw[Cf(r,t)];return e}var Iv=function(r,t,e){return r.length},Dw=(zr={},zr[Zt+"_"+Ue]=function(r,t,e){return Math.max(0,r.length-t)},zr[Zt+"_"+Gi]=function(r,t,e){var i=r[0];return i?Math.max(0,i.length-t):0},zr[we]=Iv,zr[ke]=function(r,t,e){var i=e[0].name,n=r[i];return n?n.length:0},zr[he]=Iv,zr);function lg(r,t){var e=Dw[Cf(r,t)];return e}var nl=function(r,t,e){return r[t]},Mw=(Hr={},Hr[Zt]=nl,Hr[we]=function(r,t,e){return r[e]},Hr[ke]=nl,Hr[he]=function(r,t,e){var i=ta(r);return i instanceof Array?i[t]:i},Hr[dr]=nl,Hr);function ug(r){var t=Mw[r];return t}function Cf(r,t){return r===Zt?r+"_"+t:r}function Bi(r,t,e){if(r){var i=r.getRawDataItem(t);if(i!=null){var n=r.getStore(),a=n.getSource().sourceFormat;if(e!=null){var o=r.getDimensionIndex(e),s=n.getDimensionProperty(o);return ug(a)(i,o,s)}else{var l=i;return a===he&&(l=ta(i)),l}}}}var Aw=/\{@(.+?)\}/g,Lw=function(){function r(){}return r.prototype.getDataParams=function(t,e){var i=this.getData(e),n=this.getRawValue(t,e),a=i.getRawIndex(t),o=i.getName(t),s=i.getRawDataItem(t),l=i.getItemVisual(t,"style"),u=l&&l[i.getItemVisual(t,"drawType")||"fill"],f=l&&l.stroke,h=this.mainType,c=h==="series",v=i.userOutput&&i.userOutput.get();return{componentType:h,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:c?this.subType:null,seriesIndex:this.seriesIndex,seriesId:c?this.id:null,seriesName:c?this.name:null,name:o,dataIndex:a,data:s,dataType:e,value:n,color:u,borderColor:f,dimensionNames:v?v.fullDimensions:null,encode:v?v.encode:null,$vars:["seriesName","name","value"]}},r.prototype.getFormattedLabel=function(t,e,i,n,a,o){e=e||"normal";var s=this.getData(i),l=this.getDataParams(t,i);if(o&&(l.value=o.interpolatedValue),n!=null&&F(l.value)&&(l.value=l.value[n]),!a){var u=s.getItemModel(t);a=u.get(e==="normal"?["label","formatter"]:[e,"label","formatter"])}if(q(a))return l.status=e,l.dimensionIndex=n,a(l);if(G(a)){var f=$p(a,l);return f.replace(Aw,function(h,c){var v=c.length,d=c;d.charAt(0)==="["&&d.charAt(v-1)==="]"&&(d=+d.slice(1,v-1));var g=Bi(s,t,d);if(o&&F(o.interpolatedValue)){var p=s.getDimensionIndex(d);p>=0&&(g=o.interpolatedValue[p])}return g!=null?g+"":""})}},r.prototype.getRawValue=function(t,e){return Bi(this.getData(e),t)},r.prototype.formatTooltip=function(t,e,i){},r}();function Pv(r){var t,e;return W(r)?r.type&&(e=r):t=r,{text:t,frag:e}}function En(r){return new Iw(r)}var Iw=function(){function r(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}return r.prototype.perform=function(t){var e=this._upstream,i=t&&t.skip;if(this._dirty&&e){var n=this.context;n.data=n.outputData=e.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var a;this._plan&&!i&&(a=this._plan(this.context));var o=f(this._modBy),s=this._modDataCount||0,l=f(t&&t.modBy),u=t&&t.modDataCount||0;(o!==l||s!==u)&&(a="reset");function f(m){return!(m>=1)&&(m=1),m}var h;(this._dirty||a==="reset")&&(this._dirty=!1,h=this._doReset(i)),this._modBy=l,this._modDataCount=u;var c=t&&t.step;if(e?this._dueEnd=e._outputDueEnd:this._dueEnd=this._count?this._count(this.context):1/0,this._progress){var v=this._dueIndex,d=Math.min(c!=null?this._dueIndex+c:1/0,this._dueEnd);if(!i&&(h||v<d)){var g=this._progress;if(F(g))for(var p=0;p<g.length;p++)this._doProgress(g[p],v,d,l,u);else this._doProgress(g,v,d,l,u)}this._dueIndex=d;var y=this._settedOutputEnd!=null?this._settedOutputEnd:d;this._outputDueEnd=y}else this._dueIndex=this._outputDueEnd=this._settedOutputEnd!=null?this._settedOutputEnd:this._dueEnd;return this.unfinished()},r.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},r.prototype._doProgress=function(t,e,i,n,a){Rv.reset(e,i,n,a),this._callingProgress=t,this._callingProgress({start:e,end:i,count:i-e,next:Rv.next},this.context)},r.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null;var e,i;!t&&this._reset&&(e=this._reset(this.context),e&&e.progress&&(i=e.forceFirstProgress,e=e.progress),F(e)&&!e.length&&(e=null)),this._progress=e,this._modBy=this._modDataCount=null;var n=this._downstream;return n&&n.dirty(),i},r.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},r.prototype.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},r.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},r.prototype.getUpstream=function(){return this._upstream},r.prototype.getDownstream=function(){return this._downstream},r.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},r}(),Rv=function(){var r,t,e,i,n,a={reset:function(l,u,f,h){t=l,r=u,e=f,i=h,n=Math.ceil(i/e),a.next=e>1&&i>0?s:o}};return a;function o(){return t<r?t++:null}function s(){var l=t%n*e+Math.ceil(t/n),u=t>=r?null:l<i?l:t;return t++,u}}();function eo(r,t){var e=t&&t.type;return e==="ordinal"?r:(e==="time"&&!vt(r)&&r!=null&&r!=="-"&&(r=+Xe(r)),r==null||r===""?NaN:Number(r))}Q({number:function(r){return parseFloat(r)},time:function(r){return+Xe(r)},trim:function(r){return G(r)?Le(r):r}});var Pw=function(){function r(t,e){var i=t==="desc";this._resultLT=i?1:-1,e==null&&(e=i?"min":"max"),this._incomparable=e==="min"?-1/0:1/0}return r.prototype.evaluate=function(t,e){var i=vt(t)?t:po(t),n=vt(e)?e:po(e),a=isNaN(i),o=isNaN(n);if(a&&(i=this._incomparable),o&&(n=this._incomparable),a&&o){var s=G(t),l=G(e);s&&(i=l?t:0),l&&(n=s?e:0)}return i<n?this._resultLT:i>n?-this._resultLT:0},r}(),Rw=function(){function r(){}return r.prototype.getRawData=function(){throw new Error("not supported")},r.prototype.getRawDataItem=function(t){throw new Error("not supported")},r.prototype.cloneRawData=function(){},r.prototype.getDimensionInfo=function(t){},r.prototype.cloneAllDimensionInfo=function(){},r.prototype.count=function(){},r.prototype.retrieveValue=function(t,e){},r.prototype.retrieveValueFromItem=function(t,e){},r.prototype.convertValue=function(t,e){return eo(t,e)},r}();function Ew(r,t){var e=new Rw,i=r.data,n=e.sourceFormat=r.sourceFormat,a=r.startIndex,o="";r.seriesLayoutBy!==Ue&&Wt(o);var s=[],l={},u=r.dimensionsDefine;if(u)M(u,function(g,p){var y=g.name,m={index:p,name:y,displayName:g.displayName};if(s.push(m),y!=null){var _="";Qr(l,y)&&Wt(_),l[y]=m}});else for(var f=0;f<r.dimensionsDetectedCount;f++)s.push({index:f});var h=sg(n,Ue);t.__isBuiltIn&&(e.getRawDataItem=function(g){return h(i,a,s,g)},e.getRawData=ht(Ow,null,r)),e.cloneRawData=ht(kw,null,r);var c=lg(n,Ue);e.count=ht(c,null,i,a,s);var v=ug(n);e.retrieveValue=function(g,p){var y=h(i,a,s,g);return d(y,p)};var d=e.retrieveValueFromItem=function(g,p){if(g!=null){var y=s[p];if(y)return v(g,p,y.name)}};return e.getDimensionInfo=ht(Bw,null,s,l),e.cloneAllDimensionInfo=ht(Nw,null,s),e}function Ow(r){var t=r.sourceFormat;if(!Df(t)){var e="";Wt(e)}return r.data}function kw(r){var t=r.sourceFormat,e=r.data;if(!Df(t)){var i="";Wt(i)}if(t===Zt){for(var n=[],a=0,o=e.length;a<o;a++)n.push(e[a].slice());return n}else if(t===we){for(var n=[],a=0,o=e.length;a<o;a++)n.push(O({},e[a]));return n}}function Bw(r,t,e){if(e!=null){if(vt(e)||!isNaN(e)&&!Qr(t,e))return r[e];if(Qr(t,e))return t[e]}}function Nw(r){return j(r)}var fg=Q();function Fw(r){r=j(r);var t=r.type,e="";t||Wt(e);var i=t.split(":");i.length!==2&&Wt(e);var n=!1;i[0]==="echarts"&&(t=i[1],n=!0),r.__isBuiltIn=n,fg.set(t,r)}function zw(r,t,e){var i=Nt(r),n=i.length,a="";n||Wt(a);for(var o=0,s=n;o<s;o++){var l=i[o];t=Hw(l,t),o!==s-1&&(t.length=Math.max(t.length,1))}return t}function Hw(r,t,e,i){var n="";t.length||Wt(n),W(r)||Wt(n);var a=r.type,o=fg.get(a);o||Wt(n);var s=V(t,function(u){return Ew(u,o)}),l=Nt(o.transform({upstream:s[0],upstreamList:s,config:j(r.config)}));return V(l,function(u,f){var h="";W(u)||Wt(h),u.data||Wt(h);var c=ng(u.data);Df(c)||Wt(h);var v,d=t[0];if(d&&f===0&&!u.dimensions){var g=d.startIndex;g&&(u.data=d.data.slice(0,g).concat(u.data)),v={seriesLayoutBy:Ue,sourceHeader:g,dimensions:d.metaRawOption.dimensions}}else v={seriesLayoutBy:Ue,sourceHeader:0,dimensions:u.dimensions};return fu(u.data,v,null)})}function Df(r){return r===Zt||r===we}var as="undefined",Gw=typeof Uint32Array===as?Array:Uint32Array,Vw=typeof Uint16Array===as?Array:Uint16Array,hg=typeof Int32Array===as?Array:Int32Array,Ev=typeof Float64Array===as?Array:Float64Array,vg={float:Ev,int:hg,ordinal:Array,number:Array,time:Ev},al;function ci(r){return r>65535?Gw:Vw}function di(){return[1/0,-1/0]}function Ww(r){var t=r.constructor;return t===Array?r.slice():new t(r)}function Ov(r,t,e,i,n){var a=vg[e||"float"];if(n){var o=r[t],s=o&&o.length;if(s!==i){for(var l=new a(i),u=0;u<s;u++)l[u]=o[u];r[t]=l}}else r[t]=new a(i)}var hu=function(){function r(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=Q()}return r.prototype.initData=function(t,e,i){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var n=t.getSource(),a=this.defaultDimValueGetter=al[n.sourceFormat];this._dimValueGetter=i||a,this._rawExtent=[],ag(n),this._dimensions=V(e,function(o){return{type:o.type,property:o.property}}),this._initDataFromProvider(0,t.count())},r.prototype.getProvider=function(){return this._provider},r.prototype.getSource=function(){return this._provider.getSource()},r.prototype.ensureCalculationDimension=function(t,e){var i=this._calcDimNameToIdx,n=this._dimensions,a=i.get(t);if(a!=null){if(n[a].type===e)return a}else a=n.length;return n[a]={type:e},i.set(t,a),this._chunks[a]=new vg[e||"float"](this._rawCount),this._rawExtent[a]=di(),a},r.prototype.collectOrdinalMeta=function(t,e){var i=this._chunks[t],n=this._dimensions[t],a=this._rawExtent,o=n.ordinalOffset||0,s=i.length;o===0&&(a[t]=di());for(var l=a[t],u=o;u<s;u++){var f=i[u]=e.parseAndCollect(i[u]);isNaN(f)||(l[0]=Math.min(f,l[0]),l[1]=Math.max(f,l[1]))}n.ordinalMeta=e,n.ordinalOffset=s,n.type="ordinal"},r.prototype.getOrdinalMeta=function(t){var e=this._dimensions[t],i=e.ordinalMeta;return i},r.prototype.getDimensionProperty=function(t){var e=this._dimensions[t];return e&&e.property},r.prototype.appendData=function(t){var e=this._provider,i=this.count();e.appendData(t);var n=e.count();return e.persistent||(n+=i),i<n&&this._initDataFromProvider(i,n,!0),[i,n]},r.prototype.appendValues=function(t,e){for(var i=this._chunks,n=this._dimensions,a=n.length,o=this._rawExtent,s=this.count(),l=s+Math.max(t.length,e||0),u=0;u<a;u++){var f=n[u];Ov(i,u,f.type,l,!0)}for(var h=[],c=s;c<l;c++)for(var v=c-s,d=0;d<a;d++){var f=n[d],g=al.arrayRows.call(this,t[v]||h,f.property,v,d);i[d][c]=g;var p=o[d];g<p[0]&&(p[0]=g),g>p[1]&&(p[1]=g)}return this._rawCount=this._count=l,{start:s,end:l}},r.prototype._initDataFromProvider=function(t,e,i){for(var n=this._provider,a=this._chunks,o=this._dimensions,s=o.length,l=this._rawExtent,u=V(o,function(m){return m.property}),f=0;f<s;f++){var h=o[f];l[f]||(l[f]=di()),Ov(a,f,h.type,e,i)}if(n.fillStorage)n.fillStorage(t,e,a,l);else for(var c=[],v=t;v<e;v++){c=n.getItem(v,c);for(var d=0;d<s;d++){var g=a[d],p=this._dimValueGetter(c,u[d],v,d);g[v]=p;var y=l[d];p<y[0]&&(y[0]=p),p>y[1]&&(y[1]=p)}}!n.persistent&&n.clean&&n.clean(),this._rawCount=this._count=e,this._extent=[]},r.prototype.count=function(){return this._count},r.prototype.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var i=this._chunks[t];return i?i[this.getRawIndex(e)]:NaN},r.prototype.getValues=function(t,e){var i=[],n=[];if(e==null){e=t,t=[];for(var a=0;a<this._dimensions.length;a++)n.push(a)}else n=t;for(var a=0,o=n.length;a<o;a++)i.push(this.get(n[a],e));return i},r.prototype.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var i=this._chunks[t];return i?i[e]:NaN},r.prototype.getSum=function(t){var e=this._chunks[t],i=0;if(e)for(var n=0,a=this.count();n<a;n++){var o=this.get(t,n);isNaN(o)||(i+=o)}return i},r.prototype.getMedian=function(t){var e=[];this.each([t],function(a){isNaN(a)||e.push(a)});var i=e.sort(function(a,o){return a-o}),n=this.count();return n===0?0:n%2===1?i[(n-1)/2]:(i[n/2]+i[n/2-1])/2},r.prototype.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,i=e[t];if(i!=null&&i<this._count&&i===t)return t;for(var n=0,a=this._count-1;n<=a;){var o=(n+a)/2|0;if(e[o]<t)n=o+1;else if(e[o]>t)a=o-1;else return o}return-1},r.prototype.indicesOfNearest=function(t,e,i){var n=this._chunks,a=n[t],o=[];if(!a)return o;i==null&&(i=1/0);for(var s=1/0,l=-1,u=0,f=0,h=this.count();f<h;f++){var c=this.getRawIndex(f),v=e-a[c],d=Math.abs(v);d<=i&&((d<s||d===s&&v>=0&&l<0)&&(s=d,l=v,u=0),v===l&&(o[u++]=f))}return o.length=u,o},r.prototype.getIndices=function(){var t,e=this._indices;if(e){var i=e.constructor,n=this._count;if(i===Array){t=new i(n);for(var a=0;a<n;a++)t[a]=e[a]}else t=new i(e.buffer,0,n)}else{var i=ci(this._rawCount);t=new i(this.count());for(var a=0;a<t.length;a++)t[a]=a}return t},r.prototype.filter=function(t,e){if(!this._count)return this;for(var i=this.clone(),n=i.count(),a=ci(i._rawCount),o=new a(n),s=[],l=t.length,u=0,f=t[0],h=i._chunks,c=0;c<n;c++){var v=void 0,d=i.getRawIndex(c);if(l===0)v=e(c);else if(l===1){var g=h[f][d];v=e(g,c)}else{for(var p=0;p<l;p++)s[p]=h[t[p]][d];s[p]=c,v=e.apply(null,s)}v&&(o[u++]=d)}return u<n&&(i._indices=o),i._count=u,i._extent=[],i._updateGetRawIdx(),i},r.prototype.selectRange=function(t){var e=this.clone(),i=e._count;if(!i)return this;var n=dt(t),a=n.length;if(!a)return this;var o=e.count(),s=ci(e._rawCount),l=new s(o),u=0,f=n[0],h=t[f][0],c=t[f][1],v=e._chunks,d=!1;if(!e._indices){var g=0;if(a===1){for(var p=v[n[0]],y=0;y<i;y++){var m=p[y];(m>=h&&m<=c||isNaN(m))&&(l[u++]=g),g++}d=!0}else if(a===2){for(var p=v[n[0]],_=v[n[1]],S=t[n[1]][0],b=t[n[1]][1],y=0;y<i;y++){var m=p[y],w=_[y];(m>=h&&m<=c||isNaN(m))&&(w>=S&&w<=b||isNaN(w))&&(l[u++]=g),g++}d=!0}}if(!d)if(a===1)for(var y=0;y<o;y++){var x=e.getRawIndex(y),m=v[n[0]][x];(m>=h&&m<=c||isNaN(m))&&(l[u++]=x)}else for(var y=0;y<o;y++){for(var C=!0,x=e.getRawIndex(y),T=0;T<a;T++){var D=n[T],m=v[D][x];(m<t[D][0]||m>t[D][1])&&(C=!1)}C&&(l[u++]=e.getRawIndex(y))}return u<o&&(e._indices=l),e._count=u,e._extent=[],e._updateGetRawIdx(),e},r.prototype.map=function(t,e){var i=this.clone(t);return this._updateDims(i,t,e),i},r.prototype.modify=function(t,e){this._updateDims(this,t,e)},r.prototype._updateDims=function(t,e,i){for(var n=t._chunks,a=[],o=e.length,s=t.count(),l=[],u=t._rawExtent,f=0;f<e.length;f++)u[e[f]]=di();for(var h=0;h<s;h++){for(var c=t.getRawIndex(h),v=0;v<o;v++)l[v]=n[e[v]][c];l[o]=h;var d=i&&i.apply(null,l);if(d!=null){typeof d!="object"&&(a[0]=d,d=a);for(var f=0;f<d.length;f++){var g=e[f],p=d[f],y=u[g],m=n[g];m&&(m[c]=p),p<y[0]&&(y[0]=p),p>y[1]&&(y[1]=p)}}}},r.prototype.lttbDownSample=function(t,e){var i=this.clone([t],!0),n=i._chunks,a=n[t],o=this.count(),s=0,l=Math.floor(1/e),u=this.getRawIndex(0),f,h,c,v=new(ci(this._rawCount))(Math.min((Math.ceil(o/l)+2)*2,o));v[s++]=u;for(var d=1;d<o-1;d+=l){for(var g=Math.min(d+l,o-1),p=Math.min(d+l*2,o),y=(p+g)/2,m=0,_=g;_<p;_++){var S=this.getRawIndex(_),b=a[S];isNaN(b)||(m+=b)}m/=p-g;var w=d,x=Math.min(d+l,o),C=d-1,T=a[u];f=-1,c=w;for(var D=-1,A=0,_=w;_<x;_++){var S=this.getRawIndex(_),b=a[S];if(isNaN(b)){A++,D<0&&(D=S);continue}h=Math.abs((C-y)*(b-T)-(C-_)*(m-T)),h>f&&(f=h,c=S)}A>0&&A<x-w&&(v[s++]=Math.min(D,c),c=Math.max(D,c)),v[s++]=c,u=c}return v[s++]=this.getRawIndex(o-1),i._count=s,i._indices=v,i.getRawIndex=this._getRawIdx,i},r.prototype.minmaxDownSample=function(t,e){for(var i=this.clone([t],!0),n=i._chunks,a=Math.floor(1/e),o=n[t],s=this.count(),l=new(ci(this._rawCount))(Math.ceil(s/a)*2),u=0,f=0;f<s;f+=a){var h=f,c=o[this.getRawIndex(h)],v=f,d=o[this.getRawIndex(v)],g=a;f+a>s&&(g=s-f);for(var p=0;p<g;p++){var y=this.getRawIndex(f+p),m=o[y];m<c&&(c=m,h=f+p),m>d&&(d=m,v=f+p)}var _=this.getRawIndex(h),S=this.getRawIndex(v);h<v?(l[u++]=_,l[u++]=S):(l[u++]=S,l[u++]=_)}return i._count=u,i._indices=l,i._updateGetRawIdx(),i},r.prototype.downSample=function(t,e,i,n){for(var a=this.clone([t],!0),o=a._chunks,s=[],l=Math.floor(1/e),u=o[t],f=this.count(),h=a._rawExtent[t]=di(),c=new(ci(this._rawCount))(Math.ceil(f/l)),v=0,d=0;d<f;d+=l){l>f-d&&(l=f-d,s.length=l);for(var g=0;g<l;g++){var p=this.getRawIndex(d+g);s[g]=u[p]}var y=i(s),m=this.getRawIndex(Math.min(d+n(s,y)||0,f-1));u[m]=y,y<h[0]&&(h[0]=y),y>h[1]&&(h[1]=y),c[v++]=m}return a._count=v,a._indices=c,a._updateGetRawIdx(),a},r.prototype.each=function(t,e){if(this._count)for(var i=t.length,n=this._chunks,a=0,o=this.count();a<o;a++){var s=this.getRawIndex(a);switch(i){case 0:e(a);break;case 1:e(n[t[0]][s],a);break;case 2:e(n[t[0]][s],n[t[1]][s],a);break;default:for(var l=0,u=[];l<i;l++)u[l]=n[t[l]][s];u[l]=a,e.apply(null,u)}}},r.prototype.getDataExtent=function(t){var e=this._chunks[t],i=di();if(!e)return i;var n=this.count(),a=!this._indices,o;if(a)return this._rawExtent[t].slice();if(o=this._extent[t],o)return o.slice();o=i;for(var s=o[0],l=o[1],u=0;u<n;u++){var f=this.getRawIndex(u),h=e[f];h<s&&(s=h),h>l&&(l=h)}return o=[s,l],this._extent[t]=o,o},r.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var i=[],n=this._chunks,a=0;a<n.length;a++)i.push(n[a][e]);return i},r.prototype.clone=function(t,e){var i=new r,n=this._chunks,a=t&&gr(t,function(s,l){return s[l]=!0,s},{});if(a)for(var o=0;o<n.length;o++)i._chunks[o]=a[o]?Ww(n[o]):n[o];else i._chunks=n;return this._copyCommonProps(i),e||(i._indices=this._cloneIndices()),i._updateGetRawIdx(),i},r.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=j(this._extent),t._rawExtent=j(this._rawExtent)},r.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array){var i=this._indices.length;e=new t(i);for(var n=0;n<i;n++)e[n]=this._indices[n]}else e=new t(this._indices);return e}return null},r.prototype._getRawIdxIdentity=function(t){return t},r.prototype._getRawIdx=function(t){return t<this._count&&t>=0?this._indices[t]:-1},r.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},r.internalField=function(){function t(e,i,n,a){return eo(e[a],this._dimensions[a])}al={arrayRows:t,objectRows:function(e,i,n,a){return eo(e[i],this._dimensions[a])},keyedColumns:t,original:function(e,i,n,a){var o=e&&(e.value==null?e:e.value);return eo(o instanceof Array?o[a]:o,this._dimensions[a])},typedArray:function(e,i,n,a){return e[a]}}}(),r}(),Uw=function(){function r(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return r.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},r.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},r.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},r.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},r.prototype._createSource=function(){this._setLocalSource([],[]);var t=this._sourceHost,e=this._getUpstreamSourceManagers(),i=!!e.length,n,a;if(Da(t)){var o=t,s=void 0,l=void 0,u=void 0;if(i){var f=e[0];f.prepareSource(),u=f.getSource(),s=u.data,l=u.sourceFormat,a=[f._getVersionSign()]}else s=o.get("data",!0),l=$t(s)?dr:he,a=[];var h=this._getSourceMetaRawOption()||{},c=u&&u.metaRawOption||{},v=K(h.seriesLayoutBy,c.seriesLayoutBy)||null,d=K(h.sourceHeader,c.sourceHeader),g=K(h.dimensions,c.dimensions),p=v!==c.seriesLayoutBy||!!d!=!!c.sourceHeader||g;n=p?[fu(s,{seriesLayoutBy:v,sourceHeader:d,dimensions:g},l)]:[]}else{var y=t;if(i){var m=this._applyTransform(e);n=m.sourceList,a=m.upstreamSignList}else{var _=y.get("source",!0);n=[fu(_,this._getSourceMetaRawOption(),null)],a=[]}}this._setLocalSource(n,a)},r.prototype._applyTransform=function(t){var e=this._sourceHost,i=e.get("transform",!0),n=e.get("fromTransformResult",!0);if(n!=null){var a="";t.length!==1&&kv(a)}var o,s=[],l=[];return M(t,function(u){u.prepareSource();var f=u.getSource(n||0),h="";n!=null&&!f&&kv(h),s.push(f),l.push(u._getVersionSign())}),i?o=zw(i,s,{datasetIndex:e.componentIndex}):n!=null&&(o=[bw(s[0])]),{sourceList:o,upstreamSignList:l}},r.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var i=t[e];if(i._isDirty()||this._upstreamSignList[e]!==i._getVersionSign())return!0}},r.prototype.getSource=function(t){t=t||0;var e=this._sourceList[t];if(!e){var i=this._getUpstreamSourceManagers();return i[0]&&i[0].getSource(t)}return e},r.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},r.prototype._innerGetDataStore=function(t,e,i){var n=0,a=this._storeList,o=a[n];o||(o=a[n]={});var s=o[i];if(!s){var l=this._getUpstreamSourceManagers()[0];Da(this._sourceHost)&&l?s=l._innerGetDataStore(t,e,i):(s=new hu,s.initData(new og(e,t.length),t)),o[i]=s}return s},r.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(Da(t)){var e=Sf(t);return e?[e.getSourceManager()]:[]}else return V(QS(t),function(i){return i.getSourceManager()})},r.prototype._getSourceMetaRawOption=function(){var t=this._sourceHost,e,i,n;if(Da(t))e=t.get("seriesLayoutBy",!0),i=t.get("sourceHeader",!0),n=t.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var a=t;e=a.get("seriesLayoutBy",!0),i=a.get("sourceHeader",!0),n=a.get("dimensions",!0)}return{seriesLayoutBy:e,sourceHeader:i,dimensions:n}},r}();function Da(r){return r.mainType==="series"}function kv(r){throw new Error(r)}var Yw="line-height:1";function cg(r){var t=r.lineHeight;return t==null?Yw:"line-height:"+Gt(t+"")+"px"}function dg(r,t){var e=r.color||"#6e7079",i=r.fontSize||12,n=r.fontWeight||"400",a=r.color||"#464646",o=r.fontSize||14,s=r.fontWeight||"900";return t==="html"?{nameStyle:"font-size:"+Gt(i+"")+"px;color:"+Gt(e)+";font-weight:"+Gt(n+""),valueStyle:"font-size:"+Gt(o+"")+"px;color:"+Gt(a)+";font-weight:"+Gt(s+"")}:{nameStyle:{fontSize:i,fill:e,fontWeight:n},valueStyle:{fontSize:o,fill:a,fontWeight:s}}}var Xw=[0,10,20,30],$w=["",`
`,`

`,`


`];function Xn(r,t){return t.type=r,t}function vu(r){return r.type==="section"}function pg(r){return vu(r)?Zw:qw}function gg(r){if(vu(r)){var t=0,e=r.blocks.length,i=e>1||e>0&&!r.noHeader;return M(r.blocks,function(n){var a=gg(n);a>=t&&(t=a+ +(i&&(!a||vu(n)&&!n.noHeader)))}),t}return 0}function Zw(r,t,e,i){var n=t.noHeader,a=Kw(gg(t)),o=[],s=t.blocks||[];Ye(!s||F(s)),s=s||[];var l=r.orderMode;if(t.sortBlocks&&l){s=s.slice();var u={valueAsc:"asc",valueDesc:"desc"};if(Qr(u,l)){var f=new Pw(u[l],null);s.sort(function(g,p){return f.evaluate(g.sortParam,p.sortParam)})}else l==="seriesDesc"&&s.reverse()}M(s,function(g,p){var y=t.valueFormatter,m=pg(g)(y?O(O({},r),{valueFormatter:y}):r,g,p>0?a.html:0,i);m!=null&&o.push(m)});var h=r.renderMode==="richText"?o.join(a.richText):cu(i,o.join(""),n?e:a.html);if(n)return h;var c=uu(t.header,"ordinal",r.useUTC),v=dg(i,r.renderMode).nameStyle,d=cg(i);return r.renderMode==="richText"?yg(r,c,v)+a.richText+h:cu(i,'<div style="'+v+";"+d+';">'+Gt(c)+"</div>"+h,e)}function qw(r,t,e,i){var n=r.renderMode,a=t.noName,o=t.noValue,s=!t.markerType,l=t.name,u=r.useUTC,f=t.valueFormatter||r.valueFormatter||function(S){return S=F(S)?S:[S],V(S,function(b,w){return uu(b,F(v)?v[w]:v,u)})};if(!(a&&o)){var h=s?"":r.markupStyleCreator.makeTooltipMarker(t.markerType,t.markerColor||"#333",n),c=a?"":uu(l,"ordinal",u),v=t.valueType,d=o?[]:f(t.value,t.dataIndex),g=!s||!a,p=!s&&a,y=dg(i,n),m=y.nameStyle,_=y.valueStyle;return n==="richText"?(s?"":h)+(a?"":yg(r,c,m))+(o?"":jw(r,d,g,p,_)):cu(i,(s?"":h)+(a?"":Qw(c,!s,m))+(o?"":Jw(d,g,p,_)),e)}}function Bv(r,t,e,i,n,a){if(r){var o=pg(r),s={useUTC:n,renderMode:e,orderMode:i,markupStyleCreator:t,valueFormatter:r.valueFormatter};return o(s,r,0,a)}}function Kw(r){return{html:Xw[r],richText:$w[r]}}function cu(r,t,e){var i='<div style="clear:both"></div>',n="margin: "+e+"px 0 0",a=cg(r);return'<div style="'+n+";"+a+';">'+t+i+"</div>"}function Qw(r,t,e){var i=t?"margin-left:2px":"";return'<span style="'+e+";"+i+'">'+Gt(r)+"</span>"}function Jw(r,t,e,i){var n=e?"10px":"20px",a=t?"float:right;margin-left:"+n:"";return r=F(r)?r:[r],'<span style="'+a+";"+i+'">'+V(r,function(o){return Gt(o)}).join("&nbsp;&nbsp;")+"</span>"}function yg(r,t,e){return r.markupStyleCreator.wrapRichTextStyle(t,e)}function jw(r,t,e,i,n){var a=[n],o=i?10:20;return e&&a.push({padding:[0,0,0,o],align:"right"}),r.markupStyleCreator.wrapRichTextStyle(F(t)?t.join("  "):t,a)}function tb(r,t){var e=r.getData().getItemVisual(t,"style"),i=e[r.visualDrawType];return ti(i)}function mg(r,t){var e=r.get("padding");return e??(t==="richText"?[8,10]:10)}var ol=function(){function r(){this.richTextStyles={},this._nextStyleNameId=Xd()}return r.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},r.prototype.makeTooltipMarker=function(t,e,i){var n=i==="richText"?this._generateStyleName():null,a=VS({color:e,type:t,renderMode:i,markerId:n});return G(a)?a:(this.richTextStyles[n]=a.style,a.content)},r.prototype.wrapRichTextStyle=function(t,e){var i={};F(e)?M(e,function(a){return O(i,a)}):O(i,e);var n=this._generateStyleName();return this.richTextStyles[n]=i,"{"+n+"|"+t+"}"},r}();function eb(r){var t=r.series,e=r.dataIndex,i=r.multipleSeries,n=t.getData(),a=n.mapDimensionsAll("defaultedTooltip"),o=a.length,s=t.getRawValue(e),l=F(s),u=tb(t,e),f,h,c,v;if(o>1||l&&!o){var d=rb(s,t,e,a,u);f=d.inlineValues,h=d.inlineValueTypes,c=d.blocks,v=d.inlineValues[0]}else if(o){var g=n.getDimensionInfo(a[0]);v=f=Bi(n,e,a[0]),h=g.type}else v=f=l?s[0]:s;var p=Qu(t),y=p&&t.name||"",m=n.getName(e),_=i?y:m;return Xn("section",{header:y,noHeader:i||!p,sortParam:v,blocks:[Xn("nameValue",{markerType:"item",markerColor:u,name:_,noName:!Le(_),value:f,valueType:h,dataIndex:e})].concat(c||[])})}function rb(r,t,e,i,n){var a=t.getData(),o=gr(r,function(h,c,v){var d=a.getDimensionInfo(v);return h=h||d&&d.tooltip!==!1&&d.displayName!=null},!1),s=[],l=[],u=[];i.length?M(i,function(h){f(Bi(a,e,h),h)}):M(r,f);function f(h,c){var v=a.getDimensionInfo(c);!v||v.otherDims.tooltip===!1||(o?u.push(Xn("nameValue",{markerType:"subItem",markerColor:n,name:v.displayName,value:h,valueType:v.type})):(s.push(h),l.push(v.type)))}return{inlineValues:s,inlineValueTypes:l,blocks:u}}var je=_t();function Ma(r,t){return r.getName(t)||r.getId(t)}var ib="__universalTransitionEnabled",$e=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e._selectedDataIndicesMap={},e}return t.prototype.init=function(e,i,n){this.seriesIndex=this.componentIndex,this.dataTask=En({count:ab,reset:ob}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(e,n);var a=je(this).sourceManager=new Uw(this);a.prepareSource();var o=this.getInitialData(e,n);Fv(o,this),this.dataTask.context.data=o,je(this).dataBeforeProcessed=o,Nv(this),this._initSelectedMapFromData(o)},t.prototype.mergeDefaultAndTheme=function(e,i){var n=Un(this),a=n?is(e):{},o=this.subType;ot.hasClass(o)&&(o+="Series"),it(e,i.getTheme().get(this.subType)),it(e,this.getDefaultOption()),Ql(e,"label",["show"]),this.fillDataTextStyle(e.data),n&&ki(e,a,n)},t.prototype.mergeOption=function(e,i){e=it(this.option,e,!0),this.fillDataTextStyle(e.data);var n=Un(this);n&&ki(this.option,e,n);var a=je(this).sourceManager;a.dirty(),a.prepareSource();var o=this.getInitialData(e,i);Fv(o,this),this.dataTask.dirty(),this.dataTask.context.data=o,je(this).dataBeforeProcessed=o,Nv(this),this._initSelectedMapFromData(o)},t.prototype.fillDataTextStyle=function(e){if(e&&!$t(e))for(var i=["show"],n=0;n<e.length;n++)e[n]&&e[n].label&&Ql(e[n],"label",i)},t.prototype.getInitialData=function(e,i){},t.prototype.appendData=function(e){var i=this.getRawData();i.appendData(e.data)},t.prototype.getData=function(e){var i=du(this);if(i){var n=i.context.data;return e==null||!n.getLinkedData?n:n.getLinkedData(e)}else return je(this).data},t.prototype.getAllData=function(){var e=this.getData();return e&&e.getLinkedDataAll?e.getLinkedDataAll():[{data:e}]},t.prototype.setData=function(e){var i=du(this);if(i){var n=i.context;n.outputData=e,i!==this.dataTask&&(n.data=e)}je(this).data=e},t.prototype.getEncode=function(){var e=this.get("encode",!0);if(e)return Q(e)},t.prototype.getSourceManager=function(){return je(this).sourceManager},t.prototype.getSource=function(){return this.getSourceManager().getSource()},t.prototype.getRawData=function(){return je(this).dataBeforeProcessed},t.prototype.getColorBy=function(){var e=this.get("colorBy");return e||"series"},t.prototype.isColorBySeries=function(){return this.getColorBy()==="series"},t.prototype.getBaseAxis=function(){var e=this.coordinateSystem;return e&&e.getBaseAxis&&e.getBaseAxis()},t.prototype.formatTooltip=function(e,i,n){return eb({series:this,dataIndex:e,multipleSeries:i})},t.prototype.isAnimationEnabled=function(){var e=this.ecModel;if($.node&&!(e&&e.ssr))return!1;var i=this.getShallow("animation");return i&&this.getData().count()>this.getShallow("animationThreshold")&&(i=!1),!!i},t.prototype.restoreData=function(){this.dataTask.dirty()},t.prototype.getColorFromPalette=function(e,i,n){var a=this.ecModel,o=wf.prototype.getColorFromPalette.call(this,e,i,n);return o||(o=a.getColorFromPalette(e,i,n)),o},t.prototype.coordDimToDataDim=function(e){return this.getRawData().mapDimensionsAll(e)},t.prototype.getProgressive=function(){return this.get("progressive")},t.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},t.prototype.select=function(e,i){this._innerSelect(this.getData(i),e)},t.prototype.unselect=function(e,i){var n=this.option.selectedMap;if(n){var a=this.option.selectedMode,o=this.getData(i);if(a==="series"||n==="all"){this.option.selectedMap={},this._selectedDataIndicesMap={};return}for(var s=0;s<e.length;s++){var l=e[s],u=Ma(o,l);n[u]=!1,this._selectedDataIndicesMap[u]=-1}}},t.prototype.toggleSelect=function(e,i){for(var n=[],a=0;a<e.length;a++)n[0]=e[a],this.isSelected(e[a],i)?this.unselect(n,i):this.select(n,i)},t.prototype.getSelectedDataIndices=function(){if(this.option.selectedMap==="all")return[].slice.call(this.getData().getIndices());for(var e=this._selectedDataIndicesMap,i=dt(e),n=[],a=0;a<i.length;a++){var o=e[i[a]];o>=0&&n.push(o)}return n},t.prototype.isSelected=function(e,i){var n=this.option.selectedMap;if(!n)return!1;var a=this.getData(i);return(n==="all"||n[Ma(a,e)])&&!a.getItemModel(e).get(["select","disabled"])},t.prototype.isUniversalTransitionEnabled=function(){if(this[ib])return!0;var e=this.option.universalTransition;return e?e===!0?!0:e&&e.enabled:!1},t.prototype._innerSelect=function(e,i){var n,a,o=this.option,s=o.selectedMode,l=i.length;if(!(!s||!l)){if(s==="series")o.selectedMap="all";else if(s==="multiple"){W(o.selectedMap)||(o.selectedMap={});for(var u=o.selectedMap,f=0;f<l;f++){var h=i[f],c=Ma(e,h);u[c]=!0,this._selectedDataIndicesMap[c]=e.getRawIndex(h)}}else if(s==="single"||s===!0){var v=i[l-1],c=Ma(e,v);o.selectedMap=(n={},n[c]=!0,n),this._selectedDataIndicesMap=(a={},a[c]=e.getRawIndex(v),a)}}},t.prototype._initSelectedMapFromData=function(e){if(!this.option.selectedMap){var i=[];e.hasItemOption&&e.each(function(n){var a=e.getRawDataItem(n);a&&a.selected&&i.push(n)}),i.length>0&&this._innerSelect(e,i)}},t.registerClass=function(e){return ot.registerClass(e)},t.protoInitialize=function(){var e=t.prototype;e.type="series.__base__",e.seriesIndex=0,e.ignoreStyleOnData=!1,e.hasSymbolVisual=!1,e.defaultSymbol="circle",e.visualStyleAccessPath="itemStyle",e.visualDrawType="fill"}(),t}(ot);Ee($e,Lw);Ee($e,wf);Jd($e,ot);function Nv(r){var t=r.name;Qu(r)||(r.name=nb(r)||t)}function nb(r){var t=r.getRawData(),e=t.mapDimensionsAll("seriesName"),i=[];return M(e,function(n){var a=t.getDimensionInfo(n);a.displayName&&i.push(a.displayName)}),i.join(" ")}function ab(r){return r.model.getRawData().count()}function ob(r){var t=r.model;return t.setData(t.getRawData().cloneShallow()),sb}function sb(r,t){t.outputData&&r.end>t.outputData.count()&&t.model.getRawData().cloneShallow(t.outputData)}function Fv(r,t){M(Dm(r.CHANGABLE_METHODS,r.DOWNSAMPLE_METHODS),function(e){r.wrapMethod(e,St(lb,t))})}function lb(r,t){var e=du(r);return e&&e.setOutputEnd((t||this).count()),t}function du(r){var t=(r.ecModel||{}).scheduler,e=t&&t.getPipeline(r.uid);if(e){var i=e.currentTask;if(i){var n=i.agentStubMap;n&&(i=n.get(r.uid))}return i}}var _e=function(){function r(){this.group=new Ot,this.uid=Ko("viewComponent")}return r.prototype.init=function(t,e){},r.prototype.render=function(t,e,i,n){},r.prototype.dispose=function(t,e){},r.prototype.updateView=function(t,e,i,n){},r.prototype.updateLayout=function(t,e,i,n){},r.prototype.updateVisual=function(t,e,i,n){},r.prototype.toggleBlurSeries=function(t,e,i){},r.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)},r}();ju(_e);Fo(_e);function _g(){var r=_t();return function(t){var e=r(t),i=t.pipelineContext,n=!!e.large,a=!!e.progressiveRender,o=e.large=!!(i&&i.large),s=e.progressiveRender=!!(i&&i.progressiveRender);return(n!==o||a!==s)&&"reset"}}var Sg=_t(),ub=_g(),me=function(){function r(){this.group=new Ot,this.uid=Ko("viewChart"),this.renderTask=En({plan:fb,reset:hb}),this.renderTask.context={view:this}}return r.prototype.init=function(t,e){},r.prototype.render=function(t,e,i,n){},r.prototype.highlight=function(t,e,i,n){var a=t.getData(n&&n.dataType);a&&Hv(a,n,"emphasis")},r.prototype.downplay=function(t,e,i,n){var a=t.getData(n&&n.dataType);a&&Hv(a,n,"normal")},r.prototype.remove=function(t,e){this.group.removeAll()},r.prototype.dispose=function(t,e){},r.prototype.updateView=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.updateLayout=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.updateVisual=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.eachRendered=function(t){cf(this.group,t)},r.markUpdateMethod=function(t,e){Sg(t).updateMethod=e},r.protoInitialize=function(){var t=r.prototype;t.type="chart"}(),r}();function zv(r,t,e){r&&au(r)&&(t==="emphasis"?mo:_o)(r,e)}function Hv(r,t,e){var i=Jr(r,t),n=t&&t.highlightKey!=null?D1(t.highlightKey):null;i!=null?M(Nt(i),function(a){zv(r.getItemGraphicEl(a),e,n)}):r.eachItemGraphicEl(function(a){zv(a,e,n)})}ju(me);Fo(me);function fb(r){return ub(r.model)}function hb(r){var t=r.model,e=r.ecModel,i=r.api,n=r.payload,a=t.pipelineContext.progressiveRender,o=r.view,s=n&&Sg(n).updateMethod,l=a?"incrementalPrepareRender":s&&o[s]?s:"render";return l!=="render"&&o[l](t,e,i,n),vb[l]}var vb={incrementalPrepareRender:{progress:function(r,t){t.view.incrementalRender(r,t.model,t.ecModel,t.api,t.payload)}},render:{forceFirstProgress:!0,progress:function(r,t){t.view.render(t.model,t.ecModel,t.api,t.payload)}}},Do="\0__throttleOriginMethod",Gv="\0__throttleRate",Vv="\0__throttleType";function Mf(r,t,e){var i,n=0,a=0,o=null,s,l,u,f;t=t||0;function h(){a=new Date().getTime(),o=null,r.apply(l,u||[])}var c=function(){for(var v=[],d=0;d<arguments.length;d++)v[d]=arguments[d];i=new Date().getTime(),l=this,u=v;var g=f||t,p=f||e;f=null,s=i-(p?n:a)-g,clearTimeout(o),p?o=setTimeout(h,g):s>=0?h():o=setTimeout(h,-s),n=i};return c.clear=function(){o&&(clearTimeout(o),o=null)},c.debounceNextCall=function(v){f=v},c}function wg(r,t,e,i){var n=r[t];if(n){var a=n[Do]||n,o=n[Vv],s=n[Gv];if(s!==e||o!==i){if(e==null||!i)return r[t]=a;n=r[t]=Mf(a,e,i==="debounce"),n[Do]=a,n[Vv]=i,n[Gv]=e}return n}}function pu(r,t){var e=r[t];e&&e[Do]&&(e.clear&&e.clear(),r[t]=e[Do])}var Wv=_t(),Uv={itemStyle:Gn(Op,!0),lineStyle:Gn(Ep,!0)},cb={lineStyle:"stroke",itemStyle:"fill"};function bg(r,t){var e=r.visualStyleMapper||Uv[t];return e||(console.warn("Unknown style type '"+t+"'."),Uv.itemStyle)}function xg(r,t){var e=r.visualDrawType||cb[t];return e||(console.warn("Unknown style type '"+t+"'."),"fill")}var db={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData(),i=r.visualStyleAccessPath||"itemStyle",n=r.getModel(i),a=bg(r,i),o=a(n),s=n.getShallow("decal");s&&(e.setVisual("decal",s),s.dirty=!0);var l=xg(r,i),u=o[l],f=q(u)?u:null,h=o.fill==="auto"||o.stroke==="auto";if(!o[l]||f||h){var c=r.getColorFromPalette(r.name,null,t.getSeriesCount());o[l]||(o[l]=c,e.setVisual("colorFromPalette",!0)),o.fill=o.fill==="auto"||q(o.fill)?c:o.fill,o.stroke=o.stroke==="auto"||q(o.stroke)?c:o.stroke}if(e.setVisual("style",o),e.setVisual("drawType",l),!t.isSeriesFiltered(r)&&f)return e.setVisual("colorFromPalette",!1),{dataEach:function(v,d){var g=r.getDataParams(d),p=O({},o);p[l]=f(g),v.setItemVisual(d,"style",p)}}}},nn=new gt,pb={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){if(!(r.ignoreStyleOnData||t.isSeriesFiltered(r))){var e=r.getData(),i=r.visualStyleAccessPath||"itemStyle",n=bg(r,i),a=e.getVisual("drawType");return{dataEach:e.hasItemOption?function(o,s){var l=o.getRawDataItem(s);if(l&&l[i]){nn.option=l[i];var u=n(nn),f=o.ensureUniqueItemVisual(s,"style");O(f,u),nn.option.decal&&(o.setItemVisual(s,"decal",nn.option.decal),nn.option.decal.dirty=!0),a in u&&o.setItemVisual(s,"colorFromPalette",!1)}}:null}}}},gb={performRawSeries:!0,overallReset:function(r){var t=Q();r.eachSeries(function(e){var i=e.getColorBy();if(!e.isColorBySeries()){var n=e.type+"-"+i,a=t.get(n);a||(a={},t.set(n,a)),Wv(e).scope=a}}),r.eachSeries(function(e){if(!(e.isColorBySeries()||r.isSeriesFiltered(e))){var i=e.getRawData(),n={},a=e.getData(),o=Wv(e).scope,s=e.visualStyleAccessPath||"itemStyle",l=xg(e,s);a.each(function(u){var f=a.getRawIndex(u);n[f]=u}),i.each(function(u){var f=n[u],h=a.getItemVisual(f,"colorFromPalette");if(h){var c=a.ensureUniqueItemVisual(f,"style"),v=i.getName(u)||u+"",d=i.count();c[l]=e.getColorFromPalette(v,o,d)}})}})}},Aa=Math.PI;function yb(r,t){t=t||{},at(t,{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var e=new Ot,i=new xt({style:{fill:t.maskColor},zlevel:t.zlevel,z:1e4});e.add(i);var n=new Lt({style:{text:t.text,fill:t.textColor,fontSize:t.fontSize,fontWeight:t.fontWeight,fontStyle:t.fontStyle,fontFamily:t.fontFamily},zlevel:t.zlevel,z:10001}),a=new xt({style:{fill:"none"},textContent:n,textConfig:{position:"right",distance:10},zlevel:t.zlevel,z:10001});e.add(a);var o;return t.showSpinner&&(o=new Yo({shape:{startAngle:-Aa/2,endAngle:-Aa/2+.1,r:t.spinnerRadius},style:{stroke:t.color,lineCap:"round",lineWidth:t.lineWidth},zlevel:t.zlevel,z:10001}),o.animateShape(!0).when(1e3,{endAngle:Aa*3/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:Aa*3/2}).delay(300).start("circularInOut"),e.add(o)),e.resize=function(){var s=n.getBoundingRect().width,l=t.showSpinner?t.spinnerRadius:0,u=(r.getWidth()-l*2-(t.showSpinner&&s?10:0)-s)/2-(t.showSpinner&&s?0:5+s/2)+(t.showSpinner?0:s/2)+(s?0:l),f=r.getHeight()/2;t.showSpinner&&o.setShape({cx:u,cy:f}),a.setShape({x:u-l,y:f-l,width:l*2,height:l*2}),i.setShape({x:0,y:0,width:r.getWidth(),height:r.getHeight()})},e.resize(),e}var Tg=function(){function r(t,e,i,n){this._stageTaskMap=Q(),this.ecInstance=t,this.api=e,i=this._dataProcessorHandlers=i.slice(),n=this._visualHandlers=n.slice(),this._allHandlers=i.concat(n)}return r.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(i){var n=i.overallTask;n&&n.dirty()})},r.prototype.getPerformArgs=function(t,e){if(t.__pipeline){var i=this._pipelineMap.get(t.__pipeline.id),n=i.context,a=!e&&i.progressiveEnabled&&(!n||n.progressiveRender)&&t.__idxInPipeline>i.blockIndex,o=a?i.step:null,s=n&&n.modDataCount,l=s!=null?Math.ceil(s/o):null;return{step:o,modBy:l,modDataCount:s}}},r.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},r.prototype.updateStreamModes=function(t,e){var i=this._pipelineMap.get(t.uid),n=t.getData(),a=n.count(),o=i.progressiveEnabled&&e.incrementalPrepareRender&&a>=i.threshold,s=t.get("large")&&a>=t.get("largeThreshold"),l=t.get("progressiveChunkMode")==="mod"?a:null;t.pipelineContext=i.context={progressiveRender:o,modDataCount:l,large:s}},r.prototype.restorePipelines=function(t){var e=this,i=e._pipelineMap=Q();t.eachSeries(function(n){var a=n.getProgressive(),o=n.uid;i.set(o,{id:o,head:null,tail:null,threshold:n.getProgressiveThreshold(),progressiveEnabled:a&&!(n.preventIncremental&&n.preventIncremental()),blockIndex:-1,step:Math.round(a||700),count:0}),e._pipe(n,n.dataTask)})},r.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.api.getModel(),i=this.api;M(this._allHandlers,function(n){var a=t.get(n.uid)||t.set(n.uid,{}),o="";Ye(!(n.reset&&n.overallReset),o),n.reset&&this._createSeriesStageTask(n,a,e,i),n.overallReset&&this._createOverallStageTask(n,a,e,i)},this)},r.prototype.prepareView=function(t,e,i,n){var a=t.renderTask,o=a.context;o.model=e,o.ecModel=i,o.api=n,a.__block=!t.incrementalPrepareRender,this._pipe(e,a)},r.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},r.prototype.performVisualTasks=function(t,e,i){this._performStageTasks(this._visualHandlers,t,e,i)},r.prototype._performStageTasks=function(t,e,i,n){n=n||{};var a=!1,o=this;M(t,function(l,u){if(!(n.visualType&&n.visualType!==l.visualType)){var f=o._stageTaskMap.get(l.uid),h=f.seriesTaskMap,c=f.overallTask;if(c){var v,d=c.agentStubMap;d.each(function(p){s(n,p)&&(p.dirty(),v=!0)}),v&&c.dirty(),o.updatePayload(c,i);var g=o.getPerformArgs(c,n.block);d.each(function(p){p.perform(g)}),c.perform(g)&&(a=!0)}else h&&h.each(function(p,y){s(n,p)&&p.dirty();var m=o.getPerformArgs(p,n.block);m.skip=!l.performRawSeries&&e.isSeriesFiltered(p.context.model),o.updatePayload(p,i),p.perform(m)&&(a=!0)})}});function s(l,u){return l.setDirty&&(!l.dirtyMap||l.dirtyMap.get(u.__pipeline.id))}this.unfinished=a||this.unfinished},r.prototype.performSeriesTasks=function(t){var e;t.eachSeries(function(i){e=i.dataTask.perform()||e}),this.unfinished=e||this.unfinished},r.prototype.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})},r.prototype.updatePayload=function(t,e){e!=="remain"&&(t.context.payload=e)},r.prototype._createSeriesStageTask=function(t,e,i,n){var a=this,o=e.seriesTaskMap,s=e.seriesTaskMap=Q(),l=t.seriesType,u=t.getTargetSeries;t.createOnAllSeries?i.eachRawSeries(f):l?i.eachRawSeriesByType(l,f):u&&u(i,n).each(f);function f(h){var c=h.uid,v=s.set(c,o&&o.get(c)||En({plan:bb,reset:xb,count:Cb}));v.context={model:h,ecModel:i,api:n,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:a},a._pipe(h,v)}},r.prototype._createOverallStageTask=function(t,e,i,n){var a=this,o=e.overallTask=e.overallTask||En({reset:mb});o.context={ecModel:i,api:n,overallReset:t.overallReset,scheduler:a};var s=o.agentStubMap,l=o.agentStubMap=Q(),u=t.seriesType,f=t.getTargetSeries,h=!0,c=!1,v="";Ye(!t.createOnAllSeries,v),u?i.eachRawSeriesByType(u,d):f?f(i,n).each(d):(h=!1,M(i.getSeries(),d));function d(g){var p=g.uid,y=l.set(p,s&&s.get(p)||(c=!0,En({reset:_b,onDirty:wb})));y.context={model:g,overallProgress:h},y.agent=o,y.__block=h,a._pipe(g,y)}c&&o.dirty()},r.prototype._pipe=function(t,e){var i=t.uid,n=this._pipelineMap.get(i);!n.head&&(n.head=e),n.tail&&n.tail.pipe(e),n.tail=e,e.__idxInPipeline=n.count++,e.__pipeline=n},r.wrapStageHandler=function(t,e){return q(t)&&(t={overallReset:t,seriesType:Db(t)}),t.uid=Ko("stageHandler"),e&&(t.visualType=e),t},r}();function mb(r){r.overallReset(r.ecModel,r.api,r.payload)}function _b(r){return r.overallProgress&&Sb}function Sb(){this.agent.dirty(),this.getDownstream().dirty()}function wb(){this.agent&&this.agent.dirty()}function bb(r){return r.plan?r.plan(r.model,r.ecModel,r.api,r.payload):null}function xb(r){r.useClearVisual&&r.data.clearAllVisual();var t=r.resetDefines=Nt(r.reset(r.model,r.ecModel,r.api,r.payload));return t.length>1?V(t,function(e,i){return Cg(i)}):Tb}var Tb=Cg(0);function Cg(r){return function(t,e){var i=e.data,n=e.resetDefines[r];if(n&&n.dataEach)for(var a=t.start;a<t.end;a++)n.dataEach(i,a);else n&&n.progress&&n.progress(t,i)}}function Cb(r){return r.data.count()}function Db(r){Mo=null;try{r($n,Dg)}catch{}return Mo}var $n={},Dg={},Mo;Mg($n,bf);Mg(Dg,tg);$n.eachSeriesByType=$n.eachRawSeriesByType=function(r){Mo=r};$n.eachComponent=function(r){r.mainType==="series"&&r.subType&&(Mo=r.subType)};function Mg(r,t){for(var e in t.prototype)r[e]=Yt}var Yv=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"];const Mb={color:Yv,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],Yv]};var Rt="#B9B8CE",Xv="#100C2A",La=function(){return{axisLine:{lineStyle:{color:Rt}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},$v=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],Ag={darkMode:!0,color:$v,backgroundColor:Xv,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:Rt},pageTextStyle:{color:Rt}},textStyle:{color:Rt},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:Rt}},dataZoom:{borderColor:"#71708A",textStyle:{color:Rt},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:Rt}},timeline:{lineStyle:{color:Rt},label:{color:Rt},controlStyle:{color:Rt,borderColor:Rt}},calendar:{itemStyle:{color:Xv},dayLabel:{color:Rt},monthLabel:{color:Rt},yearLabel:{color:Rt}},timeAxis:La(),logAxis:La(),valueAxis:La(),categoryAxis:La(),line:{symbol:"circle"},graph:{color:$v},gauge:{title:{color:Rt},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:Rt},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};Ag.categoryAxis.splitLine.show=!1;var Ab=function(){function r(){}return r.prototype.normalizeQuery=function(t){var e={},i={},n={};if(G(t)){var a=Ie(t);e.mainType=a.main||null,e.subType=a.sub||null}else{var o=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1};M(t,function(l,u){for(var f=!1,h=0;h<o.length;h++){var c=o[h],v=u.lastIndexOf(c);if(v>0&&v===u.length-c.length){var d=u.slice(0,v);d!=="data"&&(e.mainType=d,e[c.toLowerCase()]=l,f=!0)}}s.hasOwnProperty(u)&&(i[u]=l,f=!0),f||(n[u]=l)})}return{cptQuery:e,dataQuery:i,otherQuery:n}},r.prototype.filter=function(t,e){var i=this.eventInfo;if(!i)return!0;var n=i.targetEl,a=i.packedEvent,o=i.model,s=i.view;if(!o||!s)return!0;var l=e.cptQuery,u=e.dataQuery;return f(l,o,"mainType")&&f(l,o,"subType")&&f(l,o,"index","componentIndex")&&f(l,o,"name")&&f(l,o,"id")&&f(u,a,"name")&&f(u,a,"dataIndex")&&f(u,a,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,n,a));function f(h,c,v,d){return h[v]==null||c[d||v]===h[v]}},r.prototype.afterTrigger=function(){this.eventInfo=null},r}(),gu=["symbol","symbolSize","symbolRotate","symbolOffset"],Zv=gu.concat(["symbolKeepAspect"]),Lb={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData();if(r.legendIcon&&e.setVisual("legendIcon",r.legendIcon),!r.hasSymbolVisual)return;for(var i={},n={},a=!1,o=0;o<gu.length;o++){var s=gu[o],l=r.get(s);q(l)?(a=!0,n[s]=l):i[s]=l}if(i.symbol=i.symbol||r.defaultSymbol,e.setVisual(O({legendIcon:r.legendIcon||i.symbol,symbolKeepAspect:r.get("symbolKeepAspect")},i)),t.isSeriesFiltered(r))return;var u=dt(n);function f(h,c){for(var v=r.getRawValue(c),d=r.getDataParams(c),g=0;g<u.length;g++){var p=u[g];h.setItemVisual(c,p,n[p](v,d))}}return{dataEach:a?f:null}}},Ib={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){if(!r.hasSymbolVisual||t.isSeriesFiltered(r))return;var e=r.getData();function i(n,a){for(var o=n.getItemModel(a),s=0;s<Zv.length;s++){var l=Zv[s],u=o.getShallow(l,!0);u!=null&&n.setItemVisual(a,l,u)}}return{dataEach:e.hasItemOption?i:null}}};function Pb(r,t,e){switch(e){case"color":var i=r.getItemVisual(t,"style");return i[r.getVisual("drawType")];case"opacity":return r.getItemVisual(t,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getItemVisual(t,e)}}function Rb(r,t){switch(t){case"color":var e=r.getVisual("style");return e[r.getVisual("drawType")];case"opacity":return r.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getVisual(t)}}function Eb(r,t){function e(i,n){var a=[];return i.eachComponent({mainType:"series",subType:r,query:n},function(o){a.push(o.seriesIndex)}),a}M([[r+"ToggleSelect","toggleSelect"],[r+"Select","select"],[r+"UnSelect","unselect"]],function(i){t(i[0],function(n,a,o){n=O({},n),o.dispatchAction(O(n,{type:i[1],seriesIndex:e(a,n)}))})})}function pi(r,t,e,i,n){var a=r+t;e.isSilent(a)||i.eachComponent({mainType:"series",subType:"pie"},function(o){for(var s=o.seriesIndex,l=o.option.selectedMap,u=n.selected,f=0;f<u.length;f++)if(u[f].seriesIndex===s){var h=o.getData(),c=Jr(h,n.fromActionPayload);e.trigger(a,{type:a,seriesId:o.id,name:F(c)?h.getName(c[0]):h.getName(c),selected:G(l)?l:O({},l)})}})}function Ob(r,t,e){r.on("selectchanged",function(i){var n=e.getModel();i.isFromClick?(pi("map","selectchanged",t,n,i),pi("pie","selectchanged",t,n,i)):i.fromAction==="select"?(pi("map","selected",t,n,i),pi("pie","selected",t,n,i)):i.fromAction==="unselect"&&(pi("map","unselected",t,n,i),pi("pie","unselected",t,n,i))})}function wn(r,t,e){for(var i;r&&!(t(r)&&(i=r,e));)r=r.__hostTarget||r.parent;return i}var kb=Math.round(Math.random()*9),Bb=typeof Object.defineProperty=="function",Nb=function(){function r(){this._id="__ec_inner_"+kb++}return r.prototype.get=function(t){return this._guard(t)[this._id]},r.prototype.set=function(t,e){var i=this._guard(t);return Bb?Object.defineProperty(i,this._id,{value:e,enumerable:!1,configurable:!0}):i[this._id]=e,this},r.prototype.delete=function(t){return this.has(t)?(delete this._guard(t)[this._id],!0):!1},r.prototype.has=function(t){return!!this._guard(t)[this._id]},r.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},r}(),Fb=ft.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,t){var e=t.cx,i=t.cy,n=t.width/2,a=t.height/2;r.moveTo(e,i-a),r.lineTo(e+n,i+a),r.lineTo(e-n,i+a),r.closePath()}}),zb=ft.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,t){var e=t.cx,i=t.cy,n=t.width/2,a=t.height/2;r.moveTo(e,i-a),r.lineTo(e+n,i),r.lineTo(e,i+a),r.lineTo(e-n,i),r.closePath()}}),Hb=ft.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,t){var e=t.x,i=t.y,n=t.width/5*3,a=Math.max(n,t.height),o=n/2,s=o*o/(a-o),l=i-a+o+s,u=Math.asin(s/o),f=Math.cos(u)*o,h=Math.sin(u),c=Math.cos(u),v=o*.6,d=o*.7;r.moveTo(e-f,l+s),r.arc(e,l,o,Math.PI-u,Math.PI*2+u),r.bezierCurveTo(e+f-h*v,l+s+c*v,e,i-d,e,i),r.bezierCurveTo(e,i-d,e-f+h*v,l+s+c*v,e-f,l+s),r.closePath()}}),Gb=ft.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,t){var e=t.height,i=t.width,n=t.x,a=t.y,o=i/3*2;r.moveTo(n,a),r.lineTo(n+o,a+e),r.lineTo(n,a+e/4*3),r.lineTo(n-o,a+e),r.lineTo(n,a),r.closePath()}}),Vb={line:mr,rect:xt,roundRect:xt,square:xt,circle:Uo,diamond:zb,pin:Hb,arrow:Gb,triangle:Fb},Wb={line:function(r,t,e,i,n){n.x1=r,n.y1=t+i/2,n.x2=r+e,n.y2=t+i/2},rect:function(r,t,e,i,n){n.x=r,n.y=t,n.width=e,n.height=i},roundRect:function(r,t,e,i,n){n.x=r,n.y=t,n.width=e,n.height=i,n.r=Math.min(e,i)/4},square:function(r,t,e,i,n){var a=Math.min(e,i);n.x=r,n.y=t,n.width=a,n.height=a},circle:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.r=Math.min(e,i)/2},diamond:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.width=e,n.height=i},pin:function(r,t,e,i,n){n.x=r+e/2,n.y=t+i/2,n.width=e,n.height=i},arrow:function(r,t,e,i,n){n.x=r+e/2,n.y=t+i/2,n.width=e,n.height=i},triangle:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.width=e,n.height=i}},yu={};M(Vb,function(r,t){yu[t]=new r});var Ub=ft.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(r,t,e){var i=Hd(r,t,e),n=this.shape;return n&&n.symbolType==="pin"&&t.position==="inside"&&(i.y=e.y+e.height*.4),i},buildPath:function(r,t,e){var i=t.symbolType;if(i!=="none"){var n=yu[i];n||(i="rect",n=yu[i]),Wb[i](t.x,t.y,t.width,t.height,n.shape),n.buildPath(r,n.shape,e)}}});function Yb(r,t){if(this.type!=="image"){var e=this.style;this.__isEmptyBrush?(e.stroke=r,e.fill=t||"#fff",e.lineWidth=2):this.shape.symbolType==="line"?e.stroke=r:e.fill=r,this.markRedraw()}}function Ni(r,t,e,i,n,a,o){var s=r.indexOf("empty")===0;s&&(r=r.substr(5,1).toLowerCase()+r.substr(6));var l;return r.indexOf("image://")===0?l=Mp(r.slice(8),new nt(t,e,i,n),o?"center":"cover"):r.indexOf("path://")===0?l=ff(r.slice(7),{},new nt(t,e,i,n),o?"center":"cover"):l=new Ub({shape:{symbolType:r,x:t,y:e,width:i,height:n}}),l.__isEmptyBrush=s,l.setColor=Yb,a&&l.setColor(a),l}function Xb(r){return F(r)||(r=[+r,+r]),[r[0]||0,r[1]||0]}function Lg(r,t){if(r!=null)return F(r)||(r=[r,r]),[ct(r[0],t[0])||0,ct(K(r[1],r[0]),t[1])||0]}function Yr(r){return isFinite(r)}function $b(r,t,e){var i=t.x==null?0:t.x,n=t.x2==null?1:t.x2,a=t.y==null?0:t.y,o=t.y2==null?0:t.y2;t.global||(i=i*e.width+e.x,n=n*e.width+e.x,a=a*e.height+e.y,o=o*e.height+e.y),i=Yr(i)?i:0,n=Yr(n)?n:1,a=Yr(a)?a:0,o=Yr(o)?o:0;var s=r.createLinearGradient(i,a,n,o);return s}function Zb(r,t,e){var i=e.width,n=e.height,a=Math.min(i,n),o=t.x==null?.5:t.x,s=t.y==null?.5:t.y,l=t.r==null?.5:t.r;t.global||(o=o*i+e.x,s=s*n+e.y,l=l*a),o=Yr(o)?o:.5,s=Yr(s)?s:.5,l=l>=0&&Yr(l)?l:.5;var u=r.createRadialGradient(o,s,0,o,s,l);return u}function mu(r,t,e){for(var i=t.type==="radial"?Zb(r,t,e):$b(r,t,e),n=t.colorStops,a=0;a<n.length;a++)i.addColorStop(n[a].offset,n[a].color);return i}function qb(r,t){if(r===t||!r&&!t)return!1;if(!r||!t||r.length!==t.length)return!0;for(var e=0;e<r.length;e++)if(r[e]!==t[e])return!0;return!1}function Ia(r){return parseInt(r,10)}function Pa(r,t,e){var i=["width","height"][t],n=["clientWidth","clientHeight"][t],a=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(e[i]!=null&&e[i]!=="auto")return parseFloat(e[i]);var s=document.defaultView.getComputedStyle(r);return(r[n]||Ia(s[i])||Ia(r.style[i]))-(Ia(s[a])||0)-(Ia(s[o])||0)|0}function Kb(r,t){return!r||r==="solid"||!(t>0)?null:r==="dashed"?[4*t,2*t]:r==="dotted"?[t]:vt(r)?[r]:F(r)?r:null}function Ig(r){var t=r.style,e=t.lineDash&&t.lineWidth>0&&Kb(t.lineDash,t.lineWidth),i=t.lineDashOffset;if(e){var n=t.strokeNoScale&&r.getLineScale?r.getLineScale():1;n&&n!==1&&(e=V(e,function(a){return a/n}),i/=n)}return[e,i]}var Qb=new jr(!0);function Ao(r){var t=r.stroke;return!(t==null||t==="none"||!(r.lineWidth>0))}function qv(r){return typeof r=="string"&&r!=="none"}function Lo(r){var t=r.fill;return t!=null&&t!=="none"}function Kv(r,t){if(t.fillOpacity!=null&&t.fillOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.fillOpacity*t.opacity,r.fill(),r.globalAlpha=e}else r.fill()}function Qv(r,t){if(t.strokeOpacity!=null&&t.strokeOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.strokeOpacity*t.opacity,r.stroke(),r.globalAlpha=e}else r.stroke()}function _u(r,t,e){var i=jd(t.image,t.__image,e);if(zo(i)){var n=r.createPattern(i,t.repeat||"repeat");if(typeof DOMMatrix=="function"&&n&&n.setTransform){var a=new DOMMatrix;a.translateSelf(t.x||0,t.y||0),a.rotateSelf(0,0,(t.rotation||0)*Mm),a.scaleSelf(t.scaleX||1,t.scaleY||1),n.setTransform(a)}return n}}function Jb(r,t,e,i){var n,a=Ao(e),o=Lo(e),s=e.strokePercent,l=s<1,u=!t.path;(!t.silent||l)&&u&&t.createPathProxy();var f=t.path||Qb,h=t.__dirty;if(!i){var c=e.fill,v=e.stroke,d=o&&!!c.colorStops,g=a&&!!v.colorStops,p=o&&!!c.image,y=a&&!!v.image,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0;(d||g)&&(w=t.getBoundingRect()),d&&(m=h?mu(r,c,w):t.__canvasFillGradient,t.__canvasFillGradient=m),g&&(_=h?mu(r,v,w):t.__canvasStrokeGradient,t.__canvasStrokeGradient=_),p&&(S=h||!t.__canvasFillPattern?_u(r,c,t):t.__canvasFillPattern,t.__canvasFillPattern=S),y&&(b=h||!t.__canvasStrokePattern?_u(r,v,t):t.__canvasStrokePattern,t.__canvasStrokePattern=S),d?r.fillStyle=m:p&&(S?r.fillStyle=S:o=!1),g?r.strokeStyle=_:y&&(b?r.strokeStyle=b:a=!1)}var x=t.getGlobalScale();f.setScale(x[0],x[1],t.segmentIgnoreThreshold);var C,T;r.setLineDash&&e.lineDash&&(n=Ig(t),C=n[0],T=n[1]);var D=!0;(u||h&Si)&&(f.setDPR(r.dpr),l?f.setContext(null):(f.setContext(r),D=!1),f.reset(),t.buildPath(f,t.shape,i),f.toStatic(),t.pathUpdated()),D&&f.rebuildPath(r,l?s:1),C&&(r.setLineDash(C),r.lineDashOffset=T),i||(e.strokeFirst?(a&&Qv(r,e),o&&Kv(r,e)):(o&&Kv(r,e),a&&Qv(r,e))),C&&r.setLineDash([])}function jb(r,t,e){var i=t.__image=jd(e.image,t.__image,t,t.onload);if(!(!i||!zo(i))){var n=e.x||0,a=e.y||0,o=t.getWidth(),s=t.getHeight(),l=i.width/i.height;if(o==null&&s!=null?o=s*l:s==null&&o!=null?s=o/l:o==null&&s==null&&(o=i.width,s=i.height),e.sWidth&&e.sHeight){var u=e.sx||0,f=e.sy||0;r.drawImage(i,u,f,e.sWidth,e.sHeight,n,a,o,s)}else if(e.sx&&e.sy){var u=e.sx,f=e.sy,h=o-u,c=s-f;r.drawImage(i,u,f,h,c,n,a,o,s)}else r.drawImage(i,n,a,o,s)}}function tx(r,t,e){var i,n=e.text;if(n!=null&&(n+=""),n){r.font=e.font||Kr,r.textAlign=e.textAlign,r.textBaseline=e.textBaseline;var a=void 0,o=void 0;r.setLineDash&&e.lineDash&&(i=Ig(t),a=i[0],o=i[1]),a&&(r.setLineDash(a),r.lineDashOffset=o),e.strokeFirst?(Ao(e)&&r.strokeText(n,e.x,e.y),Lo(e)&&r.fillText(n,e.x,e.y)):(Lo(e)&&r.fillText(n,e.x,e.y),Ao(e)&&r.strokeText(n,e.x,e.y)),a&&r.setLineDash([])}}var Jv=["shadowBlur","shadowOffsetX","shadowOffsetY"],jv=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Pg(r,t,e,i,n){var a=!1;if(!i&&(e=e||{},t===e))return!1;if(i||t.opacity!==e.opacity){Ut(r,n),a=!0;var o=Math.max(Math.min(t.opacity,1),0);r.globalAlpha=isNaN(o)?$r.opacity:o}(i||t.blend!==e.blend)&&(a||(Ut(r,n),a=!0),r.globalCompositeOperation=t.blend||$r.blend);for(var s=0;s<Jv.length;s++){var l=Jv[s];(i||t[l]!==e[l])&&(a||(Ut(r,n),a=!0),r[l]=r.dpr*(t[l]||0))}return(i||t.shadowColor!==e.shadowColor)&&(a||(Ut(r,n),a=!0),r.shadowColor=t.shadowColor||$r.shadowColor),a}function tc(r,t,e,i,n){var a=Zn(t,n.inHover),o=i?null:e&&Zn(e,n.inHover)||{};if(a===o)return!1;var s=Pg(r,a,o,i,n);if((i||a.fill!==o.fill)&&(s||(Ut(r,n),s=!0),qv(a.fill)&&(r.fillStyle=a.fill)),(i||a.stroke!==o.stroke)&&(s||(Ut(r,n),s=!0),qv(a.stroke)&&(r.strokeStyle=a.stroke)),(i||a.opacity!==o.opacity)&&(s||(Ut(r,n),s=!0),r.globalAlpha=a.opacity==null?1:a.opacity),t.hasStroke()){var l=a.lineWidth,u=l/(a.strokeNoScale&&t.getLineScale?t.getLineScale():1);r.lineWidth!==u&&(s||(Ut(r,n),s=!0),r.lineWidth=u)}for(var f=0;f<jv.length;f++){var h=jv[f],c=h[0];(i||a[c]!==o[c])&&(s||(Ut(r,n),s=!0),r[c]=a[c]||h[1])}return s}function ex(r,t,e,i,n){return Pg(r,Zn(t,n.inHover),e&&Zn(e,n.inHover),i,n)}function Rg(r,t){var e=t.transform,i=r.dpr||1;e?r.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):r.setTransform(i,0,0,i,0,0)}function rx(r,t,e){for(var i=!1,n=0;n<r.length;n++){var a=r[n];i=i||a.isZeroArea(),Rg(t,a),t.beginPath(),a.buildPath(t,a.shape),t.clip()}e.allClipped=i}function ix(r,t){return r&&t?r[0]!==t[0]||r[1]!==t[1]||r[2]!==t[2]||r[3]!==t[3]||r[4]!==t[4]||r[5]!==t[5]:!(!r&&!t)}var ec=1,rc=2,ic=3,nc=4;function nx(r){var t=Lo(r),e=Ao(r);return!(r.lineDash||!(+t^+e)||t&&typeof r.fill!="string"||e&&typeof r.stroke!="string"||r.strokePercent<1||r.strokeOpacity<1||r.fillOpacity<1)}function Ut(r,t){t.batchFill&&r.fill(),t.batchStroke&&r.stroke(),t.batchFill="",t.batchStroke=""}function Zn(r,t){return t&&r.__hoverStyle||r.style}function Eg(r,t){Xr(r,t,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function Xr(r,t,e,i){var n=t.transform;if(!t.shouldBePainted(e.viewWidth,e.viewHeight,!1,!1)){t.__dirty&=~Kt,t.__isRendered=!1;return}var a=t.__clipPaths,o=e.prevElClipPaths,s=!1,l=!1;if((!o||qb(a,o))&&(o&&o.length&&(Ut(r,e),r.restore(),l=s=!0,e.prevElClipPaths=null,e.allClipped=!1,e.prevEl=null),a&&a.length&&(Ut(r,e),r.save(),rx(a,r,e),s=!0),e.prevElClipPaths=a),e.allClipped){t.__isRendered=!1;return}t.beforeBrush&&t.beforeBrush(),t.innerBeforeBrush();var u=e.prevEl;u||(l=s=!0);var f=t instanceof ft&&t.autoBatch&&nx(t.style);s||ix(n,u.transform)?(Ut(r,e),Rg(r,t)):f||Ut(r,e);var h=Zn(t,e.inHover);t instanceof ft?(e.lastDrawType!==ec&&(l=!0,e.lastDrawType=ec),tc(r,t,u,l,e),(!f||!e.batchFill&&!e.batchStroke)&&r.beginPath(),Jb(r,t,h,f),f&&(e.batchFill=h.fill||"",e.batchStroke=h.stroke||"")):t instanceof go?(e.lastDrawType!==ic&&(l=!0,e.lastDrawType=ic),tc(r,t,u,l,e),tx(r,t,h)):t instanceof Sr?(e.lastDrawType!==rc&&(l=!0,e.lastDrawType=rc),ex(r,t,u,l,e),jb(r,t,h)):t.getTemporalDisplayables&&(e.lastDrawType!==nc&&(l=!0,e.lastDrawType=nc),ax(r,t,e)),f&&i&&Ut(r,e),t.innerAfterBrush(),t.afterBrush&&t.afterBrush(),e.prevEl=t,t.__dirty=0,t.__isRendered=!0}function ax(r,t,e){var i=t.getDisplayables(),n=t.getTemporalDisplayables();r.save();var a={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:e.viewWidth,viewHeight:e.viewHeight,inHover:e.inHover},o,s;for(o=t.getCursor(),s=i.length;o<s;o++){var l=i[o];l.beforeBrush&&l.beforeBrush(),l.innerBeforeBrush(),Xr(r,l,a,o===s-1),l.innerAfterBrush(),l.afterBrush&&l.afterBrush(),a.prevEl=l}for(var u=0,f=n.length;u<f;u++){var l=n[u];l.beforeBrush&&l.beforeBrush(),l.innerBeforeBrush(),Xr(r,l,a,u===f-1),l.innerAfterBrush(),l.afterBrush&&l.afterBrush(),a.prevEl=l}t.clearTemporalDisplayables(),t.notClear=!0,r.restore()}var sl=new Nb,ac=new jn(100),oc=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function Su(r,t){if(r==="none")return null;var e=t.getDevicePixelRatio(),i=t.getZr(),n=i.painter.type==="svg";r.dirty&&sl.delete(r);var a=sl.get(r);if(a)return a;var o=at(r,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});o.backgroundColor==="none"&&(o.backgroundColor=null);var s={repeat:"repeat"};return l(s),s.rotation=o.rotation,s.scaleX=s.scaleY=n?1:1/e,sl.set(r,s),r.dirty=!1,s;function l(u){for(var f=[e],h=!0,c=0;c<oc.length;++c){var v=o[oc[c]];if(v!=null&&!F(v)&&!G(v)&&!vt(v)&&typeof v!="boolean"){h=!1;break}f.push(v)}var d;if(h){d=f.join(",")+(n?"-svg":"");var g=ac.get(d);g&&(n?u.svgElement=g:u.image=g)}var p=kg(o.dashArrayX),y=ox(o.dashArrayY),m=Og(o.symbol),_=sx(p),S=Bg(y),b=!n&&Fi.createCanvas(),w=n&&{tag:"g",attrs:{},key:"dcl",children:[]},x=T(),C;b&&(b.width=x.width*e,b.height=x.height*e,C=b.getContext("2d")),D(),h&&ac.put(d,b||w),u.image=b,u.svgElement=w,u.svgWidth=x.width,u.svgHeight=x.height;function T(){for(var A=1,L=0,P=_.length;L<P;++L)A=Sh(A,_[L]);for(var I=1,L=0,P=m.length;L<P;++L)I=Sh(I,m[L].length);A*=I;var R=S*_.length*m.length;return{width:Math.max(1,Math.min(A,o.maxTileWidth)),height:Math.max(1,Math.min(R,o.maxTileHeight))}}function D(){C&&(C.clearRect(0,0,b.width,b.height),o.backgroundColor&&(C.fillStyle=o.backgroundColor,C.fillRect(0,0,b.width,b.height)));for(var A=0,L=0;L<y.length;++L)A+=y[L];if(A<=0)return;for(var P=-S,I=0,R=0,E=0;P<x.height;){if(I%2===0){for(var z=R/2%m.length,B=0,k=0,H=0;B<x.width*2;){for(var Y=0,L=0;L<p[E].length;++L)Y+=p[E][L];if(Y<=0)break;if(k%2===0){var U=(1-o.symbolSize)*.5,tt=B+p[E][k]*U,et=P+y[I]*U,ut=p[E][k]*o.symbolSize,Ft=y[I]*o.symbolSize,Jt=H/2%m[z].length;qt(tt,et,ut,Ft,m[z][Jt])}B+=p[E][k],++H,++k,k===p[E].length&&(k=0)}++E,E===p.length&&(E=0)}P+=y[I],++R,++I,I===y.length&&(I=0)}function qt(Tt,yt,X,J,be){var bt=n?1:e,qe=Ni(be,Tt*bt,yt*bt,X*bt,J*bt,o.color,o.symbolKeepAspect);if(n){var Ne=i.painter.renderOneToVNode(qe);Ne&&w.children.push(Ne)}else Eg(C,qe)}}}}function Og(r){if(!r||r.length===0)return[["rect"]];if(G(r))return[[r]];for(var t=!0,e=0;e<r.length;++e)if(!G(r[e])){t=!1;break}if(t)return Og([r]);for(var i=[],e=0;e<r.length;++e)G(r[e])?i.push([r[e]]):i.push(r[e]);return i}function kg(r){if(!r||r.length===0)return[[0,0]];if(vt(r)){var t=Math.ceil(r);return[[t,t]]}for(var e=!0,i=0;i<r.length;++i)if(!vt(r[i])){e=!1;break}if(e)return kg([r]);for(var n=[],i=0;i<r.length;++i)if(vt(r[i])){var t=Math.ceil(r[i]);n.push([t,t])}else{var t=V(r[i],function(s){return Math.ceil(s)});t.length%2===1?n.push(t.concat(t)):n.push(t)}return n}function ox(r){if(!r||typeof r=="object"&&r.length===0)return[0,0];if(vt(r)){var t=Math.ceil(r);return[t,t]}var e=V(r,function(i){return Math.ceil(i)});return r.length%2?e.concat(e):e}function sx(r){return V(r,function(t){return Bg(t)})}function Bg(r){for(var t=0,e=0;e<r.length;++e)t+=r[e];return r.length%2===1?t*2:t}function lx(r,t){r.eachRawSeries(function(e){if(!r.isSeriesFiltered(e)){var i=e.getData();i.hasItemVisual()&&i.each(function(o){var s=i.getItemVisual(o,"decal");if(s){var l=i.ensureUniqueItemVisual(o,"style");l.decal=Su(s,t)}});var n=i.getVisual("decal");if(n){var a=i.getVisual("style");a.decal=Su(n,t)}}})}var ge=new Oe,Ng={};function ux(r,t){Ng[r]=t}function fx(r){return Ng[r]}var hx=1,vx=800,cx=900,dx=1e3,px=2e3,gx=5e3,Fg=1e3,yx=1100,Af=2e3,zg=3e3,mx=4e3,os=4500,_x=4600,Sx=5e3,bx=6e3,Hg=7e3,xx={PROCESSOR:{FILTER:dx,SERIES_FILTER:vx,STATISTIC:gx},VISUAL:{LAYOUT:Fg,PROGRESSIVE_LAYOUT:yx,GLOBAL:Af,CHART:zg,POST_CHART_LAYOUT:_x,COMPONENT:mx,BRUSH:Sx,CHART_ITEM:os,ARIA:bx,DECAL:Hg}},Pt="__flagInMainProcess",Ht="__pendingUpdate",ll="__needsUpdateStatus",sc=/^[a-zA-Z0-9_]+$/,ul="__connectUpdateStatus",lc=0,Tx=1,Cx=2;function Gg(r){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(this.isDisposed()){this.id;return}return Wg(this,r,t)}}function Vg(r){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return Wg(this,r,t)}}function Wg(r,t,e){return e[0]=e[0]&&e[0].toLowerCase(),Oe.prototype[t].apply(r,e)}var Ug=function(r){N(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t}(Oe),Yg=Ug.prototype;Yg.on=Vg("on");Yg.off=Vg("off");var gi,fl,Ra,tr,hl,vl,cl,an,on,uc,fc,dl,hc,Ea,vc,Xg,jt,cc,$g=function(r){N(t,r);function t(e,i,n){var a=r.call(this,new Ab)||this;a._chartsViews=[],a._chartsMap={},a._componentsViews=[],a._componentsMap={},a._pendingActions=[],n=n||{},G(i)&&(i=Zg[i]),a._dom=e;var o="canvas",s="auto",l=!1;n.ssr;var u=a._zr=mh(e,{renderer:n.renderer||o,devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height,ssr:n.ssr,useDirtyRect:K(n.useDirtyRect,l),useCoarsePointer:K(n.useCoarsePointer,s),pointerSize:n.pointerSize});a._ssr=n.ssr,a._throttledZrFlush=Mf(ht(u.flush,u),17),i=j(i),i&&rg(i,!0),a._theme=i,a._locale=kS(n.locale||kp),a._coordSysMgr=new xf;var f=a._api=vc(a);function h(c,v){return c.__prio-v.__prio}return Ya(Po,h),Ya(wu,h),a._scheduler=new Tg(a,f,wu,Po),a._messageCenter=new Ug,a._initEvents(),a.resize=ht(a.resize,a),u.animation.on("frame",a._onframe,a),uc(u,a),fc(u,a),El(a),a}return t.prototype._onframe=function(){if(!this._disposed){cc(this);var e=this._scheduler;if(this[Ht]){var i=this[Ht].silent;this[Pt]=!0;try{gi(this),tr.update.call(this,null,this[Ht].updateParams)}catch(l){throw this[Pt]=!1,this[Ht]=null,l}this._zr.flush(),this[Pt]=!1,this[Ht]=null,an.call(this,i),on.call(this,i)}else if(e.unfinished){var n=hx,a=this._model,o=this._api;e.unfinished=!1;do{var s=+new Date;e.performSeriesTasks(a),e.performDataProcessorTasks(a),vl(this,a),e.performVisualTasks(a),Ea(this,this._model,o,"remain",{}),n-=+new Date-s}while(n>0&&e.unfinished);e.unfinished||this._zr.flush()}}},t.prototype.getDom=function(){return this._dom},t.prototype.getId=function(){return this.id},t.prototype.getZr=function(){return this._zr},t.prototype.isSSR=function(){return this._ssr},t.prototype.setOption=function(e,i,n){if(!this[Pt]){if(this._disposed){this.id;return}var a,o,s;if(W(i)&&(n=i.lazyUpdate,a=i.silent,o=i.replaceMerge,s=i.transition,i=i.notMerge),this[Pt]=!0,!this._model||i){var l=new lw(this._api),u=this._theme,f=this._model=new bf;f.scheduler=this._scheduler,f.ssr=this._ssr,f.init(null,null,null,u,this._locale,l)}this._model.setOption(e,{replaceMerge:o},bu);var h={seriesTransition:s,optionChanged:!0};if(n)this[Ht]={silent:a,updateParams:h},this[Pt]=!1,this.getZr().wakeUp();else{try{gi(this),tr.update.call(this,null,h)}catch(c){throw this[Ht]=null,this[Pt]=!1,c}this._ssr||this._zr.flush(),this[Ht]=null,this[Pt]=!1,an.call(this,a),on.call(this,a)}}},t.prototype.setTheme=function(){},t.prototype.getModel=function(){return this._model},t.prototype.getOption=function(){return this._model&&this._model.getOption()},t.prototype.getWidth=function(){return this._zr.getWidth()},t.prototype.getHeight=function(){return this._zr.getHeight()},t.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||$.hasGlobalWindow&&window.devicePixelRatio||1},t.prototype.getRenderedCanvas=function(e){return this.renderToCanvas(e)},t.prototype.renderToCanvas=function(e){e=e||{};var i=this._zr.painter;return i.getRenderedCanvas({backgroundColor:e.backgroundColor||this._model.get("backgroundColor"),pixelRatio:e.pixelRatio||this.getDevicePixelRatio()})},t.prototype.renderToSVGString=function(e){e=e||{};var i=this._zr.painter;return i.renderToString({useViewBox:e.useViewBox})},t.prototype.getSvgDataURL=function(){if($.svgSupported){var e=this._zr,i=e.storage.getDisplayList();return M(i,function(n){n.stopAnimation(null,!0)}),e.painter.toDataURL()}},t.prototype.getDataURL=function(e){if(this._disposed){this.id;return}e=e||{};var i=e.excludeComponents,n=this._model,a=[],o=this;M(i,function(l){n.eachComponent({mainType:l},function(u){var f=o._componentsMap[u.__viewId];f.group.ignore||(a.push(f),f.group.ignore=!0)})});var s=this._zr.painter.getType()==="svg"?this.getSvgDataURL():this.renderToCanvas(e).toDataURL("image/"+(e&&e.type||"png"));return M(a,function(l){l.group.ignore=!1}),s},t.prototype.getConnectedDataURL=function(e){if(this._disposed){this.id;return}var i=e.type==="svg",n=this.group,a=Math.min,o=Math.max,s=1/0;if(dc[n]){var l=s,u=s,f=-s,h=-s,c=[],v=e&&e.pixelRatio||this.getDevicePixelRatio();M(kn,function(_,S){if(_.group===n){var b=i?_.getZr().painter.getSvgDom().innerHTML:_.renderToCanvas(j(e)),w=_.getDom().getBoundingClientRect();l=a(w.left,l),u=a(w.top,u),f=o(w.right,f),h=o(w.bottom,h),c.push({dom:b,left:w.left,top:w.top})}}),l*=v,u*=v,f*=v,h*=v;var d=f-l,g=h-u,p=Fi.createCanvas(),y=mh(p,{renderer:i?"svg":"canvas"});if(y.resize({width:d,height:g}),i){var m="";return M(c,function(_){var S=_.left-l,b=_.top-u;m+='<g transform="translate('+S+","+b+')">'+_.dom+"</g>"}),y.painter.getSvgRoot().innerHTML=m,e.connectedBackgroundColor&&y.painter.setBackgroundColor(e.connectedBackgroundColor),y.refreshImmediately(),y.painter.toDataURL()}else return e.connectedBackgroundColor&&y.add(new xt({shape:{x:0,y:0,width:d,height:g},style:{fill:e.connectedBackgroundColor}})),M(c,function(_){var S=new Sr({style:{x:_.left*v-l,y:_.top*v-u,image:_.dom}});y.add(S)}),y.refreshImmediately(),p.toDataURL("image/"+(e&&e.type||"png"))}else return this.getDataURL(e)},t.prototype.convertToPixel=function(e,i){return hl(this,"convertToPixel",e,i)},t.prototype.convertFromPixel=function(e,i){return hl(this,"convertFromPixel",e,i)},t.prototype.containPixel=function(e,i){if(this._disposed){this.id;return}var n=this._model,a,o=Os(n,e);return M(o,function(s,l){l.indexOf("Models")>=0&&M(s,function(u){var f=u.coordinateSystem;if(f&&f.containPoint)a=a||!!f.containPoint(i);else if(l==="seriesModels"){var h=this._chartsMap[u.__viewId];h&&h.containPoint&&(a=a||h.containPoint(i,u))}},this)},this),!!a},t.prototype.getVisual=function(e,i){var n=this._model,a=Os(n,e,{defaultMainType:"series"}),o=a.seriesModel,s=o.getData(),l=a.hasOwnProperty("dataIndexInside")?a.dataIndexInside:a.hasOwnProperty("dataIndex")?s.indexOfRawIndex(a.dataIndex):null;return l!=null?Pb(s,l,i):Rb(s,i)},t.prototype.getViewOfComponentModel=function(e){return this._componentsMap[e.__viewId]},t.prototype.getViewOfSeriesModel=function(e){return this._chartsMap[e.__viewId]},t.prototype._initEvents=function(){var e=this;M(Dx,function(i){var n=function(a){var o=e.getModel(),s=a.target,l,u=i==="globalout";if(u?l={}:s&&wn(s,function(d){var g=st(d);if(g&&g.dataIndex!=null){var p=g.dataModel||o.getSeriesByIndex(g.seriesIndex);return l=p&&p.getDataParams(g.dataIndex,g.dataType,s)||{},!0}else if(g.eventData)return l=O({},g.eventData),!0},!0),l){var f=l.componentType,h=l.componentIndex;(f==="markLine"||f==="markPoint"||f==="markArea")&&(f="series",h=l.seriesIndex);var c=f&&h!=null&&o.getComponent(f,h),v=c&&e[c.mainType==="series"?"_chartsMap":"_componentsMap"][c.__viewId];l.event=a,l.type=i,e._$eventProcessor.eventInfo={targetEl:s,packedEvent:l,model:c,view:v},e.trigger(i,l)}};n.zrEventfulCallAtLast=!0,e._zr.on(i,n,e)}),M(On,function(i,n){e._messageCenter.on(n,function(a){this.trigger(n,a)},e)}),M(["selectchanged"],function(i){e._messageCenter.on(i,function(n){this.trigger(i,n)},e)}),Ob(this._messageCenter,this,this._api)},t.prototype.isDisposed=function(){return this._disposed},t.prototype.clear=function(){if(this._disposed){this.id;return}this.setOption({series:[]},!0)},t.prototype.dispose=function(){if(this._disposed){this.id;return}this._disposed=!0;var e=this.getDom();e&&Kd(this.getDom(),If,"");var i=this,n=i._api,a=i._model;M(i._componentsViews,function(o){o.dispose(a,n)}),M(i._chartsViews,function(o){o.dispose(a,n)}),i._zr.dispose(),i._dom=i._model=i._chartsMap=i._componentsMap=i._chartsViews=i._componentsViews=i._scheduler=i._api=i._zr=i._throttledZrFlush=i._theme=i._coordSysMgr=i._messageCenter=null,delete kn[i.id]},t.prototype.resize=function(e){if(!this[Pt]){if(this._disposed){this.id;return}this._zr.resize(e);var i=this._model;if(this._loadingFX&&this._loadingFX.resize(),!!i){var n=i.resetOption("media"),a=e&&e.silent;this[Ht]&&(a==null&&(a=this[Ht].silent),n=!0,this[Ht]=null),this[Pt]=!0;try{n&&gi(this),tr.update.call(this,{type:"resize",animation:O({duration:0},e&&e.animation)})}catch(o){throw this[Pt]=!1,o}this[Pt]=!1,an.call(this,a),on.call(this,a)}}},t.prototype.showLoading=function(e,i){if(this._disposed){this.id;return}if(W(e)&&(i=e,e=""),e=e||"default",this.hideLoading(),!!xu[e]){var n=xu[e](this._api,i),a=this._zr;this._loadingFX=n,a.add(n)}},t.prototype.hideLoading=function(){if(this._disposed){this.id;return}this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},t.prototype.makeActionFromEvent=function(e){var i=O({},e);return i.type=On[e.type],i},t.prototype.dispatchAction=function(e,i){if(this._disposed){this.id;return}if(W(i)||(i={silent:!!i}),!!Io[e.type]&&this._model){if(this[Pt]){this._pendingActions.push(e);return}var n=i.silent;cl.call(this,e,n);var a=i.flush;a?this._zr.flush():a!==!1&&$.browser.weChat&&this._throttledZrFlush(),an.call(this,n),on.call(this,n)}},t.prototype.updateLabelLayout=function(){ge.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},t.prototype.appendData=function(e){if(this._disposed){this.id;return}var i=e.seriesIndex,n=this.getModel(),a=n.getSeriesByIndex(i);a.appendData(e),this._scheduler.unfinished=!0,this.getZr().wakeUp()},t.internalField=function(){gi=function(h){var c=h._scheduler;c.restorePipelines(h._model),c.prepareStageTasks(),fl(h,!0),fl(h,!1),c.plan()},fl=function(h,c){for(var v=h._model,d=h._scheduler,g=c?h._componentsViews:h._chartsViews,p=c?h._componentsMap:h._chartsMap,y=h._zr,m=h._api,_=0;_<g.length;_++)g[_].__alive=!1;c?v.eachComponent(function(w,x){w!=="series"&&S(x)}):v.eachSeries(S);function S(w){var x=w.__requireNewView;w.__requireNewView=!1;var C="_ec_"+w.id+"_"+w.type,T=!x&&p[C];if(!T){var D=Ie(w.type),A=c?_e.getClass(D.main,D.sub):me.getClass(D.sub);T=new A,T.init(v,m),p[C]=T,g.push(T),y.add(T.group)}w.__viewId=T.__id=C,T.__alive=!0,T.__model=w,T.group.__ecComponentInfo={mainType:w.mainType,index:w.componentIndex},!c&&d.prepareView(T,w,v,m)}for(var _=0;_<g.length;){var b=g[_];b.__alive?_++:(!c&&b.renderTask.dispose(),y.remove(b.group),b.dispose(v,m),g.splice(_,1),p[b.__id]===b&&delete p[b.__id],b.__id=b.group.__ecComponentInfo=null)}},Ra=function(h,c,v,d,g){var p=h._model;if(p.setUpdatePayload(v),!d){M([].concat(h._componentsViews).concat(h._chartsViews),b);return}var y={};y[d+"Id"]=v[d+"Id"],y[d+"Index"]=v[d+"Index"],y[d+"Name"]=v[d+"Name"];var m={mainType:d,query:y};g&&(m.subType=g);var _=v.excludeSeriesId,S;_!=null&&(S=Q(),M(Nt(_),function(w){var x=Pe(w,null);x!=null&&S.set(x,!0)})),p&&p.eachComponent(m,function(w){var x=S&&S.get(w.id)!=null;if(!x)if($h(v))if(w instanceof $e)v.type===Zr&&!v.notBlur&&!w.get(["emphasis","disabled"])&&m1(w,v,h._api);else{var C=nf(w.mainType,w.componentIndex,v.name,h._api),T=C.focusSelf,D=C.dispatchers;v.type===Zr&&T&&!v.notBlur&&ru(w.mainType,w.componentIndex,h._api),D&&M(D,function(A){v.type===Zr?mo(A):_o(A)})}else ou(v)&&w instanceof $e&&(w1(w,v,h._api),Yh(w),jt(h))},h),p&&p.eachComponent(m,function(w){var x=S&&S.get(w.id)!=null;x||b(h[d==="series"?"_chartsMap":"_componentsMap"][w.__viewId])},h);function b(w){w&&w.__alive&&w[c]&&w[c](w.__model,p,h._api,v)}},tr={prepareAndUpdate:function(h){gi(this),tr.update.call(this,h,{optionChanged:h.newOption!=null})},update:function(h,c){var v=this._model,d=this._api,g=this._zr,p=this._coordSysMgr,y=this._scheduler;if(v){v.setUpdatePayload(h),y.restoreData(v,h),y.performSeriesTasks(v),p.create(v,d),y.performDataProcessorTasks(v,h),vl(this,v),p.update(v,d),e(v),y.performVisualTasks(v,h),dl(this,v,d,h,c);var m=v.get("backgroundColor")||"transparent",_=v.get("darkMode");g.setBackgroundColor(m),_!=null&&_!=="auto"&&g.setDarkMode(_),ge.trigger("afterupdate",v,d)}},updateTransform:function(h){var c=this,v=this._model,d=this._api;if(v){v.setUpdatePayload(h);var g=[];v.eachComponent(function(y,m){if(y!=="series"){var _=c.getViewOfComponentModel(m);if(_&&_.__alive)if(_.updateTransform){var S=_.updateTransform(m,v,d,h);S&&S.update&&g.push(_)}else g.push(_)}});var p=Q();v.eachSeries(function(y){var m=c._chartsMap[y.__viewId];if(m.updateTransform){var _=m.updateTransform(y,v,d,h);_&&_.update&&p.set(y.uid,1)}else p.set(y.uid,1)}),e(v),this._scheduler.performVisualTasks(v,h,{setDirty:!0,dirtyMap:p}),Ea(this,v,d,h,{},p),ge.trigger("afterupdate",v,d)}},updateView:function(h){var c=this._model;c&&(c.setUpdatePayload(h),me.markUpdateMethod(h,"updateView"),e(c),this._scheduler.performVisualTasks(c,h,{setDirty:!0}),dl(this,c,this._api,h,{}),ge.trigger("afterupdate",c,this._api))},updateVisual:function(h){var c=this,v=this._model;v&&(v.setUpdatePayload(h),v.eachSeries(function(d){d.getData().clearAllVisual()}),me.markUpdateMethod(h,"updateVisual"),e(v),this._scheduler.performVisualTasks(v,h,{visualType:"visual",setDirty:!0}),v.eachComponent(function(d,g){if(d!=="series"){var p=c.getViewOfComponentModel(g);p&&p.__alive&&p.updateVisual(g,v,c._api,h)}}),v.eachSeries(function(d){var g=c._chartsMap[d.__viewId];g.updateVisual(d,v,c._api,h)}),ge.trigger("afterupdate",v,this._api))},updateLayout:function(h){tr.update.call(this,h)}},hl=function(h,c,v,d){if(h._disposed){h.id;return}for(var g=h._model,p=h._coordSysMgr.getCoordinateSystems(),y,m=Os(g,v),_=0;_<p.length;_++){var S=p[_];if(S[c]&&(y=S[c](g,m,d))!=null)return y}},vl=function(h,c){var v=h._chartsMap,d=h._scheduler;c.eachSeries(function(g){d.updateStreamModes(g,v[g.__viewId])})},cl=function(h,c){var v=this,d=this.getModel(),g=h.type,p=h.escapeConnect,y=Io[g],m=y.actionInfo,_=(m.update||"update").split(":"),S=_.pop(),b=_[0]!=null&&Ie(_[0]);this[Pt]=!0;var w=[h],x=!1;h.batch&&(x=!0,w=V(h.batch,function(I){return I=at(O({},I),h),I.batch=null,I}));var C=[],T,D=ou(h),A=$h(h);if(A&&gp(this._api),M(w,function(I){if(T=y.action(I,v._model,v._api),T=T||O({},I),T.type=m.event||T.type,C.push(T),A){var R=Ju(h),E=R.queryOptionMap,z=R.mainTypeSpecified,B=z?E.keys()[0]:"series";Ra(v,S,I,B),jt(v)}else D?(Ra(v,S,I,"series"),jt(v)):b&&Ra(v,S,I,b.main,b.sub)}),S!=="none"&&!A&&!D&&!b)try{this[Ht]?(gi(this),tr.update.call(this,h),this[Ht]=null):tr[S].call(this,h)}catch(I){throw this[Pt]=!1,I}if(x?T={type:m.event||g,escapeConnect:p,batch:C}:T=C[0],this[Pt]=!1,!c){var L=this._messageCenter;if(L.trigger(T.type,T),D){var P={type:"selectchanged",escapeConnect:p,selected:b1(d),isFromClick:h.isFromClick||!1,fromAction:h.type,fromActionPayload:h};L.trigger(P.type,P)}}},an=function(h){for(var c=this._pendingActions;c.length;){var v=c.shift();cl.call(this,v,h)}},on=function(h){!h&&this.trigger("updated")},uc=function(h,c){h.on("rendered",function(v){c.trigger("rendered",v),h.animation.isFinished()&&!c[Ht]&&!c._scheduler.unfinished&&!c._pendingActions.length&&c.trigger("finished")})},fc=function(h,c){h.on("mouseover",function(v){var d=v.target,g=wn(d,au);g&&(_1(g,v,c._api),jt(c))}).on("mouseout",function(v){var d=v.target,g=wn(d,au);g&&(S1(g,v,c._api),jt(c))}).on("click",function(v){var d=v.target,g=wn(d,function(m){return st(m).dataIndex!=null},!0);if(g){var p=g.selected?"unselect":"select",y=st(g);c._api.dispatchAction({type:p,dataType:y.dataType,dataIndexInside:y.dataIndex,seriesIndex:y.seriesIndex,isFromClick:!0})}})};function e(h){h.clearColorPalette(),h.eachSeries(function(c){c.clearColorPalette()})}function i(h){var c=[],v=[],d=!1;if(h.eachComponent(function(m,_){var S=_.get("zlevel")||0,b=_.get("z")||0,w=_.getZLevelKey();d=d||!!w,(m==="series"?v:c).push({zlevel:S,z:b,idx:_.componentIndex,type:m,key:w})}),d){var g=c.concat(v),p,y;Ya(g,function(m,_){return m.zlevel===_.zlevel?m.z-_.z:m.zlevel-_.zlevel}),M(g,function(m){var _=h.getComponent(m.type,m.idx),S=m.zlevel,b=m.key;p!=null&&(S=Math.max(p,S)),b?(S===p&&b!==y&&S++,y=b):y&&(S===p&&S++,y=""),p=S,_.setZLevel(S)})}}dl=function(h,c,v,d,g){i(c),hc(h,c,v,d,g),M(h._chartsViews,function(p){p.__alive=!1}),Ea(h,c,v,d,g),M(h._chartsViews,function(p){p.__alive||p.remove(c,v)})},hc=function(h,c,v,d,g,p){M(p||h._componentsViews,function(y){var m=y.__model;u(m,y),y.render(m,c,v,d),s(m,y),f(m,y)})},Ea=function(h,c,v,d,g,p){var y=h._scheduler;g=O(g||{},{updatedSeries:c.getSeries()}),ge.trigger("series:beforeupdate",c,v,g);var m=!1;c.eachSeries(function(_){var S=h._chartsMap[_.__viewId];S.__alive=!0;var b=S.renderTask;y.updatePayload(b,d),u(_,S),p&&p.get(_.uid)&&b.dirty(),b.perform(y.getPerformArgs(b))&&(m=!0),S.group.silent=!!_.get("silent"),o(_,S),Yh(_)}),y.unfinished=m||y.unfinished,ge.trigger("series:layoutlabels",c,v,g),ge.trigger("series:transition",c,v,g),c.eachSeries(function(_){var S=h._chartsMap[_.__viewId];s(_,S),f(_,S)}),a(h,c),ge.trigger("series:afterupdate",c,v,g)},jt=function(h){h[ll]=!0,h.getZr().wakeUp()},cc=function(h){h[ll]&&(h.getZr().storage.traverse(function(c){In(c)||n(c)}),h[ll]=!1)};function n(h){for(var c=[],v=h.currentStates,d=0;d<v.length;d++){var g=v[d];g==="emphasis"||g==="blur"||g==="select"||c.push(g)}h.selected&&h.states.select&&c.push("select"),h.hoverState===Vo&&h.states.emphasis?c.push("emphasis"):h.hoverState===Go&&h.states.blur&&c.push("blur"),h.useStates(c)}function a(h,c){var v=h._zr,d=v.storage,g=0;d.traverse(function(p){p.isGroup||g++}),g>c.get("hoverLayerThreshold")&&!$.node&&!$.worker&&c.eachSeries(function(p){if(!p.preventUsingHoverLayer){var y=h._chartsMap[p.__viewId];y.__alive&&y.eachRendered(function(m){m.states.emphasis&&(m.states.emphasis.hoverLayer=!0)})}})}function o(h,c){var v=h.get("blendMode")||null;c.eachRendered(function(d){d.isGroup||(d.style.blend=v)})}function s(h,c){if(!h.preventAutoZ){var v=h.get("z")||0,d=h.get("zlevel")||0;c.eachRendered(function(g){return l(g,v,d,-1/0),!0})}}function l(h,c,v,d){var g=h.getTextContent(),p=h.getTextGuideLine(),y=h.isGroup;if(y)for(var m=h.childrenRef(),_=0;_<m.length;_++)d=Math.max(l(m[_],c,v,d),d);else h.z=c,h.zlevel=v,d=Math.max(h.z2,d);if(g&&(g.z=c,g.zlevel=v,isFinite(d)&&(g.z2=d+2)),p){var S=h.textGuideLineConfig;p.z=c,p.zlevel=v,isFinite(d)&&(p.z2=d+(S&&S.showAbove?1:-1))}return d}function u(h,c){c.eachRendered(function(v){if(!In(v)){var d=v.getTextContent(),g=v.getTextGuideLine();v.stateTransition&&(v.stateTransition=null),d&&d.stateTransition&&(d.stateTransition=null),g&&g.stateTransition&&(g.stateTransition=null),v.hasState()?(v.prevStates=v.currentStates,v.clearStates()):v.prevStates&&(v.prevStates=null)}})}function f(h,c){var v=h.getModel("stateAnimation"),d=h.isAnimationEnabled(),g=v.get("duration"),p=g>0?{duration:g,delay:v.get("delay"),easing:v.get("easing")}:null;c.eachRendered(function(y){if(y.states&&y.states.emphasis){if(In(y))return;if(y instanceof ft&&M1(y),y.__dirty){var m=y.prevStates;m&&y.useStates(m)}if(d){y.stateTransition=p;var _=y.getTextContent(),S=y.getTextGuideLine();_&&(_.stateTransition=p),S&&(S.stateTransition=p)}y.__dirty&&n(y)}})}vc=function(h){return new(function(c){N(v,c);function v(){return c!==null&&c.apply(this,arguments)||this}return v.prototype.getCoordinateSystems=function(){return h._coordSysMgr.getCoordinateSystems()},v.prototype.getComponentByElement=function(d){for(;d;){var g=d.__ecComponentInfo;if(g!=null)return h._model.getComponent(g.mainType,g.index);d=d.parent}},v.prototype.enterEmphasis=function(d,g){mo(d,g),jt(h)},v.prototype.leaveEmphasis=function(d,g){_o(d,g),jt(h)},v.prototype.enterBlur=function(d){y1(d),jt(h)},v.prototype.leaveBlur=function(d){vp(d),jt(h)},v.prototype.enterSelect=function(d){cp(d),jt(h)},v.prototype.leaveSelect=function(d){dp(d),jt(h)},v.prototype.getModel=function(){return h.getModel()},v.prototype.getViewOfComponentModel=function(d){return h.getViewOfComponentModel(d)},v.prototype.getViewOfSeriesModel=function(d){return h.getViewOfSeriesModel(d)},v}(tg))(h)},Xg=function(h){function c(v,d){for(var g=0;g<v.length;g++){var p=v[g];p[ul]=d}}M(On,function(v,d){h._messageCenter.on(d,function(g){if(dc[h.group]&&h[ul]!==lc){if(g&&g.escapeConnect)return;var p=h.makeActionFromEvent(g),y=[];M(kn,function(m){m!==h&&m.group===h.group&&y.push(m)}),c(y,lc),M(y,function(m){m[ul]!==Tx&&m.dispatchAction(p)}),c(y,Cx)}})})}}(),t}(Oe),Lf=$g.prototype;Lf.on=Gg("on");Lf.off=Gg("off");Lf.one=function(r,t,e){var i=this;function n(){for(var a=[],o=0;o<arguments.length;o++)a[o]=arguments[o];t&&t.apply&&t.apply(this,a),i.off(r,n)}this.on.call(this,r,n,e)};var Dx=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];var Io={},On={},wu=[],bu=[],Po=[],Zg={},xu={},kn={},dc={},Mx=+new Date-0,If="_echarts_instance_";function Ax(r,t,e){var i=!(e&&e.ssr);if(i){var n=Lx(r);if(n)return n}var a=new $g(r,t,e);return a.id="ec_"+Mx++,kn[a.id]=a,i&&Kd(r,If,a.id),Xg(a),ge.trigger("afterinit",a),a}function Lx(r){return kn[s_(r,If)]}function qg(r,t){Zg[r]=t}function Kg(r){lt(bu,r)<0&&bu.push(r)}function Qg(r,t){Rf(wu,r,t,px)}function Ix(r){Pf("afterinit",r)}function Px(r){Pf("afterupdate",r)}function Pf(r,t){ge.on(r,t)}function Vi(r,t,e){q(t)&&(e=t,t="");var i=W(r)?r.type:[r,r={event:t}][0];r.event=(r.event||i).toLowerCase(),t=r.event,!On[t]&&(Ye(sc.test(i)&&sc.test(t)),Io[i]||(Io[i]={action:e,actionInfo:r}),On[t]=i)}function Rx(r,t){xf.register(r,t)}function Ex(r,t){Rf(Po,r,t,Fg,"layout")}function ri(r,t){Rf(Po,r,t,zg,"visual")}var pc=[];function Rf(r,t,e,i,n){if((q(t)||W(t))&&(e=t,t=i),!(lt(pc,e)>=0)){pc.push(e);var a=Tg.wrapStageHandler(e,n);a.__prio=t,a.__raw=e,r.push(a)}}function Jg(r,t){xu[r]=t}function Ox(r,t,e){var i=fx("registerMap");i&&i(r,t,e)}var kx=Fw;ri(Af,db);ri(os,pb);ri(os,gb);ri(Af,Lb);ri(os,Ib);ri(Hg,lx);Kg(rg);Qg(cx,Sw);Jg("default",yb);Vi({type:Zr,event:Zr,update:Zr},Yt);Vi({type:Qa,event:Qa,update:Qa},Yt);Vi({type:Mn,event:Mn,update:Mn},Yt);Vi({type:Ja,event:Ja,update:Ja},Yt);Vi({type:An,event:An,update:An},Yt);qg("light",Mb);qg("dark",Ag);function sn(r){return r==null?0:r.length||1}function gc(r){return r}var Bx=function(){function r(t,e,i,n,a,o){this._old=t,this._new=e,this._oldKeyGetter=i||gc,this._newKeyGetter=n||gc,this.context=a,this._diffModeMultiple=o==="multiple"}return r.prototype.add=function(t){return this._add=t,this},r.prototype.update=function(t){return this._update=t,this},r.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},r.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},r.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},r.prototype.remove=function(t){return this._remove=t,this},r.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},r.prototype._executeOneToOne=function(){var t=this._old,e=this._new,i={},n=new Array(t.length),a=new Array(e.length);this._initIndexMap(t,null,n,"_oldKeyGetter"),this._initIndexMap(e,i,a,"_newKeyGetter");for(var o=0;o<t.length;o++){var s=n[o],l=i[s],u=sn(l);if(u>1){var f=l.shift();l.length===1&&(i[s]=l[0]),this._update&&this._update(f,o)}else u===1?(i[s]=null,this._update&&this._update(l,o)):this._remove&&this._remove(o)}this._performRestAdd(a,i)},r.prototype._executeMultiple=function(){var t=this._old,e=this._new,i={},n={},a=[],o=[];this._initIndexMap(t,i,a,"_oldKeyGetter"),this._initIndexMap(e,n,o,"_newKeyGetter");for(var s=0;s<a.length;s++){var l=a[s],u=i[l],f=n[l],h=sn(u),c=sn(f);if(h>1&&c===1)this._updateManyToOne&&this._updateManyToOne(f,u),n[l]=null;else if(h===1&&c>1)this._updateOneToMany&&this._updateOneToMany(f,u),n[l]=null;else if(h===1&&c===1)this._update&&this._update(f,u),n[l]=null;else if(h>1&&c>1)this._updateManyToMany&&this._updateManyToMany(f,u),n[l]=null;else if(h>1)for(var v=0;v<h;v++)this._remove&&this._remove(u[v]);else this._remove&&this._remove(u)}this._performRestAdd(o,n)},r.prototype._performRestAdd=function(t,e){for(var i=0;i<t.length;i++){var n=t[i],a=e[n],o=sn(a);if(o>1)for(var s=0;s<o;s++)this._add&&this._add(a[s]);else o===1&&this._add&&this._add(a);e[n]=null}},r.prototype._initIndexMap=function(t,e,i,n){for(var a=this._diffModeMultiple,o=0;o<t.length;o++){var s="_ec_"+this[n](t[o],o);if(a||(i[o]=s),!!e){var l=e[s],u=sn(l);u===0?(e[s]=o,a&&i.push(s)):u===1?e[s]=[l,o]:l.push(o)}}},r}(),Nx=function(){function r(t,e){this._encode=t,this._schema=e}return r.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},r.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},r}();function Fx(r,t){var e={},i=e.encode={},n=Q(),a=[],o=[],s={};M(r.dimensions,function(c){var v=r.getDimensionInfo(c),d=v.coordDim;if(d){var g=v.coordDimIndex;pl(i,d)[g]=c,v.isExtraCoord||(n.set(d,1),Hx(v.type)&&(a[0]=c),pl(s,d)[g]=r.getDimensionIndex(v.name)),v.defaultTooltip&&o.push(c)}qp.each(function(p,y){var m=pl(i,y),_=v.otherDims[y];_!=null&&_!==!1&&(m[_]=v.name)})});var l=[],u={};n.each(function(c,v){var d=i[v];u[v]=d[0],l=l.concat(d)}),e.dataDimsOnCoord=l,e.dataDimIndicesOnCoord=V(l,function(c){return r.getDimensionInfo(c).storeDimIndex}),e.encodeFirstDimNotExtra=u;var f=i.label;f&&f.length&&(a=f.slice());var h=i.tooltip;return h&&h.length?o=h.slice():o.length||(o=a.slice()),i.defaultedLabel=a,i.defaultedTooltip=o,e.userOutput=new Nx(s,t),e}function pl(r,t){return r.hasOwnProperty(t)||(r[t]=[]),r[t]}function zx(r){return r==="category"?"ordinal":r==="time"?"time":"float"}function Hx(r){return!(r==="ordinal"||r==="time")}var ro=function(){function r(t){this.otherDims={},t!=null&&O(this,t)}return r}(),Gx=_t(),Vx={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},jg=function(){function r(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}return r.prototype.isDimensionOmitted=function(){return this._dimOmitted},r.prototype._updateDimOmitted=function(t){this._dimOmitted=t,t&&(this._dimNameMap||(this._dimNameMap=ry(this.source)))},r.prototype.getSourceDimensionIndex=function(t){return K(this._dimNameMap.get(t),-1)},r.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},r.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=ag(this.source),i=!iy(t),n="",a=[],o=0,s=0;o<t;o++){var l=void 0,u=void 0,f=void 0,h=this.dimensions[s];if(h&&h.storeDimIndex===o)l=e?h.name:null,u=h.type,f=h.ordinalMeta,s++;else{var c=this.getSourceDimension(o);c&&(l=e?c.name:null,u=c.type)}a.push({property:l,type:u,ordinalMeta:f}),e&&l!=null&&(!h||!h.isCalculationCoord)&&(n+=i?l.replace(/\`/g,"`1").replace(/\$/g,"`2"):l),n+="$",n+=Vx[u]||"f",f&&(n+=f.uid),n+="$"}var v=this.source,d=[v.seriesLayoutBy,v.startIndex,n].join("$$");return{dimensions:a,hash:d}},r.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,i=0;e<this._fullDimCount;e++){var n=void 0,a=this.dimensions[i];if(a&&a.storeDimIndex===e)a.isCalculationCoord||(n=a.name),i++;else{var o=this.getSourceDimension(e);o&&(n=o.name)}t.push(n)}return t},r.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},r}();function ty(r){return r instanceof jg}function ey(r){for(var t=Q(),e=0;e<(r||[]).length;e++){var i=r[e],n=W(i)?i.name:i;n!=null&&t.get(n)==null&&t.set(n,e)}return t}function ry(r){var t=Gx(r);return t.dimNameMap||(t.dimNameMap=ey(r.dimensionsDefine))}function iy(r){return r>30}var ln=W,er=V,Wx=typeof Int32Array>"u"?Array:Int32Array,Ux="e\0\0",yc=-1,Yx=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],Xx=["_approximateExtent"],mc,Oa,un,fn,gl,hn,yl,ny=function(){function r(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","minmaxDownSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","minmaxDownSample","lttbDownSample"];var i,n=!1;ty(t)?(i=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(n=!0,i=t),i=i||["x","y"];for(var a={},o=[],s={},l=!1,u={},f=0;f<i.length;f++){var h=i[f],c=G(h)?new ro({name:h}):h instanceof ro?h:new ro(h),v=c.name;c.type=c.type||"float",c.coordDim||(c.coordDim=v,c.coordDimIndex=0);var d=c.otherDims=c.otherDims||{};o.push(v),a[v]=c,u[v]!=null&&(l=!0),c.createInvertedIndices&&(s[v]=[]),d.itemName===0&&(this._nameDimIdx=f),d.itemId===0&&(this._idDimIdx=f),n&&(c.storeDimIndex=f)}if(this.dimensions=o,this._dimInfos=a,this._initGetDimensionInfo(l),this.hostModel=e,this._invertedIndicesMap=s,this._dimOmitted){var g=this._dimIdxToName=Q();M(o,function(p){g.set(a[p].storeDimIndex,p)})}}return r.prototype.getDimension=function(t){var e=this._recognizeDimIndex(t);if(e==null)return t;if(e=t,!this._dimOmitted)return this.dimensions[e];var i=this._dimIdxToName.get(e);if(i!=null)return i;var n=this._schema.getSourceDimension(e);if(n)return n.name},r.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);if(e!=null)return e;if(t==null)return-1;var i=this._getDimInfo(t);return i?i.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},r.prototype._recognizeDimIndex=function(t){if(vt(t)||t!=null&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},r.prototype._getStoreDimIndex=function(t){var e=this.getDimensionIndex(t);return e},r.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},r.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(i){return e.hasOwnProperty(i)?e[i]:void 0}:function(i){return e[i]}},r.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},r.prototype.mapDimension=function(t,e){var i=this._dimSummary;if(e==null)return i.encodeFirstDimNotExtra[t];var n=i.encode[t];return n?n[e]:null},r.prototype.mapDimensionsAll=function(t){var e=this._dimSummary,i=e.encode[t];return(i||[]).slice()},r.prototype.getStore=function(){return this._store},r.prototype.initData=function(t,e,i){var n=this,a;if(t instanceof hu&&(a=t),!a){var o=this.dimensions,s=Tf(t)||Xt(t)?new og(t,o.length):t;a=new hu;var l=er(o,function(u){return{type:n._dimInfos[u].type,property:u}});a.initData(s,l,i)}this._store=a,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,a.count()),this._dimSummary=Fx(this,this._schema),this.userOutput=this._dimSummary.userOutput},r.prototype.appendData=function(t){var e=this._store.appendData(t);this._doInit(e[0],e[1])},r.prototype.appendValues=function(t,e){var i=this._store.appendValues(t,e&&e.length),n=i.start,a=i.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var s=n;s<a;s++){var l=s-n;this._nameList[s]=e[l],o&&yl(this,s)}},r.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,i=0;i<e.length;i++){var n=this._dimInfos[e[i]];n.ordinalMeta&&t.collectOrdinalMeta(n.storeDimIndex,n.ordinalMeta)}},r.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return this._idDimIdx==null&&t.getSource().sourceFormat!==dr&&!t.fillStorage},r.prototype._doInit=function(t,e){if(!(t>=e)){var i=this._store,n=i.getProvider();this._updateOrdinalMeta();var a=this._nameList,o=this._idList,s=n.getSource().sourceFormat,l=s===he;if(l&&!n.pure)for(var u=[],f=t;f<e;f++){var h=n.getItem(f,u);if(!this.hasItemOption&&K0(h)&&(this.hasItemOption=!0),h){var c=h.name;a[f]==null&&c!=null&&(a[f]=Pe(c,null));var v=h.id;o[f]==null&&v!=null&&(o[f]=Pe(v,null))}}if(this._shouldMakeIdFromName())for(var f=t;f<e;f++)yl(this,f);mc(this)}},r.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},r.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},r.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},r.prototype.setCalculationInfo=function(t,e){ln(t)?O(this._calculationInfo,t):this._calculationInfo[t]=e},r.prototype.getName=function(t){var e=this.getRawIndex(t),i=this._nameList[e];return i==null&&this._nameDimIdx!=null&&(i=un(this,this._nameDimIdx,e)),i==null&&(i=""),i},r.prototype._getCategory=function(t,e){var i=this._store.get(t,e),n=this._store.getOrdinalMeta(t);return n?n.categories[i]:i},r.prototype.getId=function(t){return Oa(this,this.getRawIndex(t))},r.prototype.count=function(){return this._store.count()},r.prototype.get=function(t,e){var i=this._store,n=this._dimInfos[t];if(n)return i.get(n.storeDimIndex,e)},r.prototype.getByRawIndex=function(t,e){var i=this._store,n=this._dimInfos[t];if(n)return i.getByRawIndex(n.storeDimIndex,e)},r.prototype.getIndices=function(){return this._store.getIndices()},r.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},r.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},r.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},r.prototype.getValues=function(t,e){var i=this,n=this._store;return F(t)?n.getValues(er(t,function(a){return i._getStoreDimIndex(a)}),e):n.getValues(t)},r.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,i=0,n=e.length;i<n;i++)if(isNaN(this._store.get(e[i],t)))return!1;return!0},r.prototype.indexOfName=function(t){for(var e=0,i=this._store.count();e<i;e++)if(this.getName(e)===t)return e;return-1},r.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},r.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},r.prototype.rawIndexOf=function(t,e){var i=t&&this._invertedIndicesMap[t],n=i&&i[e];return n==null||isNaN(n)?yc:n},r.prototype.indicesOfNearest=function(t,e,i){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,i)},r.prototype.each=function(t,e,i){q(t)&&(i=e,e=t,t=[]);var n=i||this,a=er(fn(t),this._getStoreDimIndex,this);this._store.each(a,n?ht(e,n):e)},r.prototype.filterSelf=function(t,e,i){q(t)&&(i=e,e=t,t=[]);var n=i||this,a=er(fn(t),this._getStoreDimIndex,this);return this._store=this._store.filter(a,n?ht(e,n):e),this},r.prototype.selectRange=function(t){var e=this,i={},n=dt(t);return M(n,function(a){var o=e._getStoreDimIndex(a);i[o]=t[a]}),this._store=this._store.selectRange(i),this},r.prototype.mapArray=function(t,e,i){q(t)&&(i=e,e=t,t=[]),i=i||this;var n=[];return this.each(t,function(){n.push(e&&e.apply(this,arguments))},i),n},r.prototype.map=function(t,e,i,n){var a=i||n||this,o=er(fn(t),this._getStoreDimIndex,this),s=hn(this);return s._store=this._store.map(o,a?ht(e,a):e),s},r.prototype.modify=function(t,e,i,n){var a=i||n||this,o=er(fn(t),this._getStoreDimIndex,this);this._store.modify(o,a?ht(e,a):e)},r.prototype.downSample=function(t,e,i,n){var a=hn(this);return a._store=this._store.downSample(this._getStoreDimIndex(t),e,i,n),a},r.prototype.minmaxDownSample=function(t,e){var i=hn(this);return i._store=this._store.minmaxDownSample(this._getStoreDimIndex(t),e),i},r.prototype.lttbDownSample=function(t,e){var i=hn(this);return i._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),i},r.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},r.prototype.getItemModel=function(t){var e=this.hostModel,i=this.getRawDataItem(t);return new gt(i,e,e&&e.ecModel)},r.prototype.diff=function(t){var e=this;return new Bx(t?t.getStore().getIndices():[],this.getStore().getIndices(),function(i){return Oa(t,i)},function(i){return Oa(e,i)})},r.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},r.prototype.setVisual=function(t,e){this._visual=this._visual||{},ln(t)?O(this._visual,t):this._visual[t]=e},r.prototype.getItemVisual=function(t,e){var i=this._itemVisuals[t],n=i&&i[e];return n??this.getVisual(e)},r.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},r.prototype.ensureUniqueItemVisual=function(t,e){var i=this._itemVisuals,n=i[t];n||(n=i[t]={});var a=n[e];return a==null&&(a=this.getVisual(e),F(a)?a=a.slice():ln(a)&&(a=O({},a)),n[e]=a),a},r.prototype.setItemVisual=function(t,e,i){var n=this._itemVisuals[t]||{};this._itemVisuals[t]=n,ln(e)?O(n,e):n[e]=i},r.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},r.prototype.setLayout=function(t,e){ln(t)?O(this._layout,t):this._layout[t]=e},r.prototype.getLayout=function(t){return this._layout[t]},r.prototype.getItemLayout=function(t){return this._itemLayouts[t]},r.prototype.setItemLayout=function(t,e,i){this._itemLayouts[t]=i?O(this._itemLayouts[t]||{},e):e},r.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},r.prototype.setItemGraphicEl=function(t,e){var i=this.hostModel&&this.hostModel.seriesIndex;s1(i,this.dataType,t,e),this._graphicEls[t]=e},r.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},r.prototype.eachItemGraphicEl=function(t,e){M(this._graphicEls,function(i,n){i&&t&&t.call(e,i,n)})},r.prototype.cloneShallow=function(t){return t||(t=new r(this._schema?this._schema:er(this.dimensions,this._getDimInfo,this),this.hostModel)),gl(t,this),t._store=this._store,t},r.prototype.wrapMethod=function(t,e){var i=this[t];q(i)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var n=i.apply(this,arguments);return e.apply(this,[n].concat(Gu(arguments)))})},r.internalField=function(){mc=function(t){var e=t._invertedIndicesMap;M(e,function(i,n){var a=t._dimInfos[n],o=a.ordinalMeta,s=t._store;if(o){i=e[n]=new Wx(o.categories.length);for(var l=0;l<i.length;l++)i[l]=yc;for(var l=0;l<s.count();l++)i[s.get(a.storeDimIndex,l)]=l}})},un=function(t,e,i){return Pe(t._getCategory(e,i),null)},Oa=function(t,e){var i=t._idList[e];return i==null&&t._idDimIdx!=null&&(i=un(t,t._idDimIdx,e)),i==null&&(i=Ux+e),i},fn=function(t){return F(t)||(t=t!=null?[t]:[]),t},hn=function(t){var e=new r(t._schema?t._schema:er(t.dimensions,t._getDimInfo,t),t.hostModel);return gl(e,t),e},gl=function(t,e){M(Yx.concat(e.__wrappedMethods||[]),function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t.__wrappedMethods=e.__wrappedMethods,M(Xx,function(i){t[i]=j(e[i])}),t._calculationInfo=O({},e._calculationInfo)},yl=function(t,e){var i=t._nameList,n=t._idList,a=t._nameDimIdx,o=t._idDimIdx,s=i[e],l=n[e];if(s==null&&a!=null&&(i[e]=s=un(t,a,e)),l==null&&o!=null&&(n[e]=l=un(t,o,e)),l==null&&s!=null){var u=t._nameRepeatCount,f=u[s]=(u[s]||0)+1;l=s,f>1&&(l+="__ec__"+f),n[e]=l}}}(),r}();function ay(r,t){Tf(r)||(r=ig(r)),t=t||{};var e=t.coordDimensions||[],i=t.dimensionsDefine||r.dimensionsDefine||[],n=Q(),a=[],o=Zx(r,e,i,t.dimensionsCount),s=t.canOmitUnusedDimensions&&iy(o),l=i===r.dimensionsDefine,u=l?ry(r):ey(i),f=t.encodeDefine;!f&&t.encodeDefaulter&&(f=t.encodeDefaulter(r,o));for(var h=Q(f),c=new hg(o),v=0;v<c.length;v++)c[v]=-1;function d(T){var D=c[T];if(D<0){var A=i[T],L=W(A)?A:{name:A},P=new ro,I=L.name;I!=null&&u.get(I)!=null&&(P.name=P.displayName=I),L.type!=null&&(P.type=L.type),L.displayName!=null&&(P.displayName=L.displayName);var R=a.length;return c[T]=R,P.storeDimIndex=T,a.push(P),P}return a[D]}if(!s)for(var v=0;v<o;v++)d(v);h.each(function(T,D){var A=Nt(T).slice();if(A.length===1&&!G(A[0])&&A[0]<0){h.set(D,!1);return}var L=h.set(D,[]);M(A,function(P,I){var R=G(P)?u.get(P):P;R!=null&&R<o&&(L[I]=R,p(d(R),D,I))})});var g=0;M(e,function(T){var D,A,L,P;if(G(T))D=T,P={};else{P=T,D=P.name;var I=P.ordinalMeta;P.ordinalMeta=null,P=O({},P),P.ordinalMeta=I,A=P.dimsDef,L=P.otherDims,P.name=P.coordDim=P.coordDimIndex=P.dimsDef=P.otherDims=null}var R=h.get(D);if(R!==!1){if(R=Nt(R),!R.length)for(var E=0;E<(A&&A.length||1);E++){for(;g<o&&d(g).coordDim!=null;)g++;g<o&&R.push(g++)}M(R,function(z,B){var k=d(z);if(l&&P.type!=null&&(k.type=P.type),p(at(k,P),D,B),k.name==null&&A){var H=A[B];!W(H)&&(H={name:H}),k.name=k.displayName=H.name,k.defaultTooltip=H.defaultTooltip}L&&at(k.otherDims,L)})}});function p(T,D,A){qp.get(D)!=null?T.otherDims[D]=A:(T.coordDim=D,T.coordDimIndex=A,n.set(D,!0))}var y=t.generateCoord,m=t.generateCoordCount,_=m!=null;m=y?m||1:0;var S=y||"value";function b(T){T.name==null&&(T.name=T.coordDim)}if(s)M(a,function(T){b(T)}),a.sort(function(T,D){return T.storeDimIndex-D.storeDimIndex});else for(var w=0;w<o;w++){var x=d(w),C=x.coordDim;C==null&&(x.coordDim=qx(S,n,_),x.coordDimIndex=0,(!y||m<=0)&&(x.isExtraCoord=!0),m--),b(x),x.type==null&&(Jp(r,w)===Ct.Must||x.isExtraCoord&&(x.otherDims.itemName!=null||x.otherDims.seriesName!=null))&&(x.type="ordinal")}return $x(a),new jg({source:r,dimensions:a,fullDimensionCount:o,dimensionOmitted:s})}function $x(r){for(var t=Q(),e=0;e<r.length;e++){var i=r[e],n=i.name,a=t.get(n)||0;a>0&&(i.name=n+(a-1)),a++,t.set(n,a)}}function Zx(r,t,e,i){var n=Math.max(r.dimensionsDetectedCount||1,t.length,e.length,i||0);return M(t,function(a){var o;W(a)&&(o=a.dimsDef)&&(n=Math.max(n,o.length))}),n}function qx(r,t,e){if(e||t.hasKey(r)){for(var i=0;t.hasKey(r+i);)i++;r+=i}return t.set(r,!0),r}var Kx=function(){function r(t){this.coordSysDims=[],this.axisMap=Q(),this.categoryAxisMap=Q(),this.coordSysName=t}return r}();function Qx(r){var t=r.get("coordinateSystem"),e=new Kx(t),i=Jx[t];if(i)return i(r,e,e.axisMap,e.categoryAxisMap),e}var Jx={cartesian2d:function(r,t,e,i){var n=r.getReferringComponents("xAxis",ye).models[0],a=r.getReferringComponents("yAxis",ye).models[0];t.coordSysDims=["x","y"],e.set("x",n),e.set("y",a),yi(n)&&(i.set("x",n),t.firstCategoryDimIndex=0),yi(a)&&(i.set("y",a),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=1))},singleAxis:function(r,t,e,i){var n=r.getReferringComponents("singleAxis",ye).models[0];t.coordSysDims=["single"],e.set("single",n),yi(n)&&(i.set("single",n),t.firstCategoryDimIndex=0)},polar:function(r,t,e,i){var n=r.getReferringComponents("polar",ye).models[0],a=n.findAxisModel("radiusAxis"),o=n.findAxisModel("angleAxis");t.coordSysDims=["radius","angle"],e.set("radius",a),e.set("angle",o),yi(a)&&(i.set("radius",a),t.firstCategoryDimIndex=0),yi(o)&&(i.set("angle",o),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=1))},geo:function(r,t,e,i){t.coordSysDims=["lng","lat"]},parallel:function(r,t,e,i){var n=r.ecModel,a=n.getComponent("parallel",r.get("parallelIndex")),o=t.coordSysDims=a.dimensions.slice();M(a.parallelAxisIndex,function(s,l){var u=n.getComponent("parallelAxis",s),f=o[l];e.set(f,u),yi(u)&&(i.set(f,u),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=l))})}};function yi(r){return r.get("type")==="category"}function jx(r,t,e){e=e||{};var i=e.byIndex,n=e.stackedCoordDimension,a,o,s;tT(t)?a=t:(o=t.schema,a=o.dimensions,s=t.store);var l=!!(r&&r.get("stack")),u,f,h,c;if(M(a,function(m,_){G(m)&&(a[_]=m={name:m}),l&&!m.isExtraCoord&&(!i&&!u&&m.ordinalMeta&&(u=m),!f&&m.type!=="ordinal"&&m.type!=="time"&&(!n||n===m.coordDim)&&(f=m))}),f&&!i&&!u&&(i=!0),f){h="__\0ecstackresult_"+r.id,c="__\0ecstackedover_"+r.id,u&&(u.createInvertedIndices=!0);var v=f.coordDim,d=f.type,g=0;M(a,function(m){m.coordDim===v&&g++});var p={name:h,coordDim:v,coordDimIndex:g,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:a.length},y={name:c,coordDim:c,coordDimIndex:g+1,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:a.length+1};o?(s&&(p.storeDimIndex=s.ensureCalculationDimension(c,d),y.storeDimIndex=s.ensureCalculationDimension(h,d)),o.appendCalculationDimension(p),o.appendCalculationDimension(y)):(a.push(p),a.push(y))}return{stackedDimension:f&&f.name,stackedByDimension:u&&u.name,isStackedByIndex:i,stackedOverDimension:c,stackResultDimension:h}}function tT(r){return!ty(r.schema)}function qn(r,t){return!!t&&t===r.getCalculationInfo("stackedDimension")}function eT(r,t){return qn(r,t)?r.getCalculationInfo("stackResultDimension"):t}function rT(r,t){var e=r.get("coordinateSystem"),i=xf.get(e),n;return t&&t.coordSysDims&&(n=V(t.coordSysDims,function(a){var o={name:a},s=t.axisMap.get(a);if(s){var l=s.get("type");o.type=zx(l)}return o})),n||(n=i&&(i.getDimensionsInfo?i.getDimensionsInfo():i.dimensions.slice())||["x","y"]),n}function iT(r,t,e){var i,n;return e&&M(r,function(a,o){var s=a.coordDim,l=e.categoryAxisMap.get(s);l&&(i==null&&(i=o),a.ordinalMeta=l.getOrdinalMeta(),t&&(a.createInvertedIndices=!0)),a.otherDims.itemName!=null&&(n=!0)}),!n&&i!=null&&(r[i].otherDims.itemName=0),i}function nT(r,t,e){e=e||{};var i=t.getSourceManager(),n,a=!1;n=i.getSource(),a=n.sourceFormat===he;var o=Qx(t),s=rT(t,o),l=e.useEncodeDefaulter,u=q(l)?l:l?St(qS,s,t):null,f={coordDimensions:s,generateCoord:e.generateCoord,encodeDefine:t.getEncode(),encodeDefaulter:u,canOmitUnusedDimensions:!a},h=ay(n,f),c=iT(h.dimensions,e.createInvertedIndices,o),v=a?null:i.getSharedDataStore(h),d=jx(t,{schema:h,store:v}),g=new ny(h,t);g.setCalculationInfo(d);var p=c!=null&&aT(n)?function(y,m,_,S){return S===c?_:this.defaultDimValueGetter(y,m,_,S)}:null;return g.hasItemOption=!1,g.initData(a?n:v,null,p),g}function aT(r){if(r.sourceFormat===he){var t=oT(r.data||[]);return!F(ta(t))}}function oT(r){for(var t=0;t<r.length&&r[t]==null;)t++;return r[t]}var Be=function(){function r(t){this._setting=t||{},this._extent=[1/0,-1/0]}return r.prototype.getSetting=function(t){return this._setting[t]},r.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},r.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.setExtent=function(t,e){var i=this._extent;isNaN(t)||(i[0]=t),isNaN(e)||(i[1]=e)},r.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},r.prototype.isBlank=function(){return this._isBlank},r.prototype.setBlank=function(t){this._isBlank=t},r}();Fo(Be);var sT=0,Tu=function(){function r(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++sT}return r.createByAxisModel=function(t){var e=t.option,i=e.data,n=i&&V(i,lT);return new r({categories:n,needCollect:!n,deduplication:e.dedplication!==!1})},r.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},r.prototype.parseAndCollect=function(t){var e,i=this._needCollect;if(!G(t)&&!i)return t;if(i&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var n=this._getOrCreateMap();return e=n.get(t),e==null&&(i?(e=this.categories.length,this.categories[e]=t,n.set(t,e)):e=NaN),e},r.prototype._getOrCreateMap=function(){return this._map||(this._map=Q(this.categories))},r}();function lT(r){return W(r)&&r.value!=null?r.value:r+""}function Cu(r){return r.type==="interval"||r.type==="log"}function uT(r,t,e,i){var n={},a=r[1]-r[0],o=n.interval=Yd(a/t);e!=null&&o<e&&(o=n.interval=e),i!=null&&o>i&&(o=n.interval=i);var s=n.intervalPrecision=oy(o),l=n.niceTickExtent=[wt(Math.ceil(r[0]/o)*o,s),wt(Math.floor(r[1]/o)*o,s)];return fT(l,r),n}function ml(r){var t=Math.pow(10,Ku(r)),e=r/t;return e?e===2?e=3:e===3?e=5:e*=2:e=1,wt(e*t)}function oy(r){return Ge(r)+2}function _c(r,t,e){r[t]=Math.max(Math.min(r[t],e[1]),e[0])}function fT(r,t){!isFinite(r[0])&&(r[0]=t[0]),!isFinite(r[1])&&(r[1]=t[1]),_c(r,0,t),_c(r,1,t),r[0]>r[1]&&(r[0]=r[1])}function ss(r,t){return r>=t[0]&&r<=t[1]}function ls(r,t){return t[1]===t[0]?.5:(r-t[0])/(t[1]-t[0])}function us(r,t){return r*(t[1]-t[0])+t[0]}var Ef=function(r){N(t,r);function t(e){var i=r.call(this,e)||this;i.type="ordinal";var n=i.getSetting("ordinalMeta");return n||(n=new Tu({})),F(n)&&(n=new Tu({categories:V(n,function(a){return W(a)?a.value:a})})),i._ordinalMeta=n,i._extent=i.getSetting("extent")||[0,n.categories.length-1],i}return t.prototype.parse=function(e){return e==null?NaN:G(e)?this._ordinalMeta.getOrdinal(e):Math.round(e)},t.prototype.contain=function(e){return e=this.parse(e),ss(e,this._extent)&&this._ordinalMeta.categories[e]!=null},t.prototype.normalize=function(e){return e=this._getTickNumber(this.parse(e)),ls(e,this._extent)},t.prototype.scale=function(e){return e=Math.round(us(e,this._extent)),this.getRawOrdinalNumber(e)},t.prototype.getTicks=function(){for(var e=[],i=this._extent,n=i[0];n<=i[1];)e.push({value:n}),n++;return e},t.prototype.getMinorTicks=function(e){},t.prototype.setSortInfo=function(e){if(e==null){this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;return}for(var i=e.ordinalNumbers,n=this._ordinalNumbersByTick=[],a=this._ticksByOrdinalNumber=[],o=0,s=this._ordinalMeta.categories.length,l=Math.min(s,i.length);o<l;++o){var u=i[o];n[o]=u,a[u]=o}for(var f=0;o<s;++o){for(;a[f]!=null;)f++;n.push(f),a[f]=o}},t.prototype._getTickNumber=function(e){var i=this._ticksByOrdinalNumber;return i&&e>=0&&e<i.length?i[e]:e},t.prototype.getRawOrdinalNumber=function(e){var i=this._ordinalNumbersByTick;return i&&e>=0&&e<i.length?i[e]:e},t.prototype.getLabel=function(e){if(!this.isBlank()){var i=this.getRawOrdinalNumber(e.value),n=this._ordinalMeta.categories[i];return n==null?"":n+""}},t.prototype.count=function(){return this._extent[1]-this._extent[0]+1},t.prototype.unionExtentFromData=function(e,i){this.unionExtent(e.getApproximateExtent(i))},t.prototype.isInExtentRange=function(e){return e=this._getTickNumber(e),this._extent[0]<=e&&this._extent[1]>=e},t.prototype.getOrdinalMeta=function(){return this._ordinalMeta},t.prototype.calcNiceTicks=function(){},t.prototype.calcNiceExtent=function(){},t.type="ordinal",t}(Be);Be.registerClass(Ef);var Gr=wt,Wi=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="interval",e._interval=0,e._intervalPrecision=2,e}return t.prototype.parse=function(e){return e},t.prototype.contain=function(e){return ss(e,this._extent)},t.prototype.normalize=function(e){return ls(e,this._extent)},t.prototype.scale=function(e){return us(e,this._extent)},t.prototype.setExtent=function(e,i){var n=this._extent;isNaN(e)||(n[0]=parseFloat(e)),isNaN(i)||(n[1]=parseFloat(i))},t.prototype.unionExtent=function(e){var i=this._extent;e[0]<i[0]&&(i[0]=e[0]),e[1]>i[1]&&(i[1]=e[1]),this.setExtent(i[0],i[1])},t.prototype.getInterval=function(){return this._interval},t.prototype.setInterval=function(e){this._interval=e,this._niceExtent=this._extent.slice(),this._intervalPrecision=oy(e)},t.prototype.getTicks=function(e){var i=this._interval,n=this._extent,a=this._niceExtent,o=this._intervalPrecision,s=[];if(!i)return s;var l=1e4;n[0]<a[0]&&(e?s.push({value:Gr(a[0]-i,o)}):s.push({value:n[0]}));for(var u=a[0];u<=a[1]&&(s.push({value:u}),u=Gr(u+i,o),u!==s[s.length-1].value);)if(s.length>l)return[];var f=s.length?s[s.length-1].value:a[1];return n[1]>f&&(e?s.push({value:Gr(f+i,o)}):s.push({value:n[1]})),s},t.prototype.getMinorTicks=function(e){for(var i=this.getTicks(!0),n=[],a=this.getExtent(),o=1;o<i.length;o++){for(var s=i[o],l=i[o-1],u=0,f=[],h=s.value-l.value,c=h/e;u<e-1;){var v=Gr(l.value+(u+1)*c);v>a[0]&&v<a[1]&&f.push(v),u++}n.push(f)}return n},t.prototype.getLabel=function(e,i){if(e==null)return"";var n=i&&i.precision;n==null?n=Ge(e.value)||0:n==="auto"&&(n=this._intervalPrecision);var a=Gr(e.value,n,!0);return Yp(a)},t.prototype.calcNiceTicks=function(e,i,n){e=e||5;var a=this._extent,o=a[1]-a[0];if(isFinite(o)){o<0&&(o=-o,a.reverse());var s=uT(a,e,i,n);this._intervalPrecision=s.intervalPrecision,this._interval=s.interval,this._niceExtent=s.niceTickExtent}},t.prototype.calcNiceExtent=function(e){var i=this._extent;if(i[0]===i[1])if(i[0]!==0){var n=Math.abs(i[0]);e.fixMax||(i[1]+=n/2),i[0]-=n/2}else i[1]=1;var a=i[1]-i[0];isFinite(a)||(i[0]=0,i[1]=1),this.calcNiceTicks(e.splitNumber,e.minInterval,e.maxInterval);var o=this._interval;e.fixMin||(i[0]=Gr(Math.floor(i[0]/o)*o)),e.fixMax||(i[1]=Gr(Math.ceil(i[1]/o)*o))},t.prototype.setNiceExtent=function(e,i){this._niceExtent=[e,i]},t.type="interval",t}(Be);Be.registerClass(Wi);var sy=typeof Float32Array<"u",hT=sy?Float32Array:Array;function Di(r){return F(r)?sy?new Float32Array(r):r:new hT(r)}var vT="__ec_stack_";function cT(r){return r.get("stack")||vT+r.seriesIndex}function ly(r){return r.dim+r.index}function dT(r,t){var e=[];return t.eachSeriesByType(r,function(i){_T(i)&&e.push(i)}),e}function pT(r){var t={};M(r,function(l){var u=l.coordinateSystem,f=u.getBaseAxis();if(!(f.type!=="time"&&f.type!=="value"))for(var h=l.getData(),c=f.dim+"_"+f.index,v=h.getDimensionIndex(h.mapDimension(f.dim)),d=h.getStore(),g=0,p=d.count();g<p;++g){var y=d.get(v,g);t[c]?t[c].push(y):t[c]=[y]}});var e={};for(var i in t)if(t.hasOwnProperty(i)){var n=t[i];if(n){n.sort(function(l,u){return l-u});for(var a=null,o=1;o<n.length;++o){var s=n[o]-n[o-1];s>0&&(a=a===null?s:Math.min(a,s))}e[i]=a}}return e}function gT(r){var t=pT(r),e=[];return M(r,function(i){var n=i.coordinateSystem,a=n.getBaseAxis(),o=a.getExtent(),s;if(a.type==="category")s=a.getBandWidth();else if(a.type==="value"||a.type==="time"){var l=a.dim+"_"+a.index,u=t[l],f=Math.abs(o[1]-o[0]),h=a.scale.getExtent(),c=Math.abs(h[1]-h[0]);s=u?f/c*u:f}else{var v=i.getData();s=Math.abs(o[1]-o[0])/v.count()}var d=ct(i.get("barWidth"),s),g=ct(i.get("barMaxWidth"),s),p=ct(i.get("barMinWidth")||(ST(i)?.5:1),s),y=i.get("barGap"),m=i.get("barCategoryGap");e.push({bandWidth:s,barWidth:d,barMaxWidth:g,barMinWidth:p,barGap:y,barCategoryGap:m,axisKey:ly(a),stackId:cT(i)})}),yT(e)}function yT(r){var t={};M(r,function(i,n){var a=i.axisKey,o=i.bandWidth,s=t[a]||{bandWidth:o,remainedWidth:o,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},l=s.stacks;t[a]=s;var u=i.stackId;l[u]||s.autoWidthCount++,l[u]=l[u]||{width:0,maxWidth:0};var f=i.barWidth;f&&!l[u].width&&(l[u].width=f,f=Math.min(s.remainedWidth,f),s.remainedWidth-=f);var h=i.barMaxWidth;h&&(l[u].maxWidth=h);var c=i.barMinWidth;c&&(l[u].minWidth=c);var v=i.barGap;v!=null&&(s.gap=v);var d=i.barCategoryGap;d!=null&&(s.categoryGap=d)});var e={};return M(t,function(i,n){e[n]={};var a=i.stacks,o=i.bandWidth,s=i.categoryGap;if(s==null){var l=dt(a).length;s=Math.max(35-l*4,15)+"%"}var u=ct(s,o),f=ct(i.gap,1),h=i.remainedWidth,c=i.autoWidthCount,v=(h-u)/(c+(c-1)*f);v=Math.max(v,0),M(a,function(y){var m=y.maxWidth,_=y.minWidth;if(y.width){var S=y.width;m&&(S=Math.min(S,m)),_&&(S=Math.max(S,_)),y.width=S,h-=S+f*S,c--}else{var S=v;m&&m<S&&(S=Math.min(m,h)),_&&_>S&&(S=_),S!==v&&(y.width=S,h-=S+f*S,c--)}}),v=(h-u)/(c+(c-1)*f),v=Math.max(v,0);var d=0,g;M(a,function(y,m){y.width||(y.width=v),g=y,d+=y.width*(1+f)}),g&&(d-=g.width*f);var p=-d/2;M(a,function(y,m){e[n][m]=e[n][m]||{bandWidth:o,offset:p,width:y.width},p+=y.width*(1+f)})}),e}function mT(r,t,e){if(r&&t){var i=r[ly(t)];return i}}function _T(r){return r.coordinateSystem&&r.coordinateSystem.type==="cartesian2d"}function ST(r){return r.pipelineContext&&r.pipelineContext.large}var wT=function(r,t,e,i){for(;e<i;){var n=e+i>>>1;r[n][1]<t?e=n+1:i=n}return e},uy=function(r){N(t,r);function t(e){var i=r.call(this,e)||this;return i.type="time",i}return t.prototype.getLabel=function(e){var i=this.getSetting("useUTC");return Qo(e.value,fv[zS(Ri(this._minLevelUnit))]||fv.second,i,this.getSetting("locale"))},t.prototype.getFormattedLabel=function(e,i,n){var a=this.getSetting("useUTC"),o=this.getSetting("locale");return HS(e,i,n,o,a)},t.prototype.getTicks=function(){var e=this._interval,i=this._extent,n=[];if(!e)return n;n.push({value:i[0],level:0});var a=this.getSetting("useUTC"),o=AT(this._minLevelUnit,this._approxInterval,a,i);return n=n.concat(o),n.push({value:i[1],level:0}),n},t.prototype.calcNiceExtent=function(e){var i=this._extent;if(i[0]===i[1]&&(i[0]-=le,i[1]+=le),i[1]===-1/0&&i[0]===1/0){var n=new Date;i[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),i[0]=i[1]-le}this.calcNiceTicks(e.splitNumber,e.minInterval,e.maxInterval)},t.prototype.calcNiceTicks=function(e,i,n){e=e||10;var a=this._extent,o=a[1]-a[0];this._approxInterval=o/e,i!=null&&this._approxInterval<i&&(this._approxInterval=i),n!=null&&this._approxInterval>n&&(this._approxInterval=n);var s=ka.length,l=Math.min(wT(ka,this._approxInterval,0,s),s-1);this._interval=ka[l][1],this._minLevelUnit=ka[Math.max(l-1,0)][0]},t.prototype.parse=function(e){return vt(e)?e:+Xe(e)},t.prototype.contain=function(e){return ss(this.parse(e),this._extent)},t.prototype.normalize=function(e){return ls(this.parse(e),this._extent)},t.prototype.scale=function(e){return us(e,this._extent)},t.type="time",t}(Wi),ka=[["second",gf],["minute",yf],["hour",Pn],["quarter-day",Pn*6],["half-day",Pn*12],["day",le*1.2],["half-week",le*3.5],["week",le*7],["month",le*31],["quarter",le*95],["half-year",uv/2],["year",uv]];function bT(r,t,e,i){var n=Xe(t),a=Xe(e),o=function(d){return hv(n,d,i)===hv(a,d,i)},s=function(){return o("year")},l=function(){return s()&&o("month")},u=function(){return l()&&o("day")},f=function(){return u()&&o("hour")},h=function(){return f()&&o("minute")},c=function(){return h()&&o("second")},v=function(){return c()&&o("millisecond")};switch(r){case"year":return s();case"month":return l();case"day":return u();case"hour":return f();case"minute":return h();case"second":return c();case"millisecond":return v()}}function xT(r,t){return r/=le,r>16?16:r>7.5?7:r>3.5?4:r>1.5?2:1}function TT(r){var t=30*le;return r/=t,r>6?6:r>3?3:r>2?2:1}function CT(r){return r/=Pn,r>12?12:r>6?6:r>3.5?4:r>2?2:1}function Sc(r,t){return r/=t?yf:gf,r>30?30:r>20?20:r>15?15:r>10?10:r>5?5:r>2?2:1}function DT(r){return Yd(r)}function MT(r,t,e){var i=new Date(r);switch(Ri(t)){case"year":case"month":i[zp(e)](0);case"day":i[Hp(e)](1);case"hour":i[Gp(e)](0);case"minute":i[Vp(e)](0);case"second":i[Wp(e)](0),i[Up(e)](0)}return i.getTime()}function AT(r,t,e,i){var n=1e4,a=Np,o=0;function s(D,A,L,P,I,R,E){for(var z=new Date(A),B=A,k=z[P]();B<L&&B<=i[1];)E.push({value:B}),k+=D,z[I](k),B=z.getTime();E.push({value:B,notAdd:!0})}function l(D,A,L){var P=[],I=!A.length;if(!bT(Ri(D),i[0],i[1],e)){I&&(A=[{value:MT(new Date(i[0]),D,e)},{value:i[1]}]);for(var R=0;R<A.length-1;R++){var E=A[R].value,z=A[R+1].value;if(E!==z){var B=void 0,k=void 0,H=void 0,Y=!1;switch(D){case"year":B=Math.max(1,Math.round(t/le/365)),k=mf(e),H=GS(e);break;case"half-year":case"quarter":case"month":B=TT(t),k=Ei(e),H=zp(e);break;case"week":case"half-week":case"day":B=xT(t),k=Jo(e),H=Hp(e),Y=!0;break;case"half-day":case"quarter-day":case"hour":B=CT(t),k=Wn(e),H=Gp(e);break;case"minute":B=Sc(t,!0),k=jo(e),H=Vp(e);break;case"second":B=Sc(t,!1),k=ts(e),H=Wp(e);break;case"millisecond":B=DT(t),k=es(e),H=Up(e);break}s(B,E,z,k,H,Y,P),D==="year"&&L.length>1&&R===0&&L.unshift({value:L[0].value-B})}}for(var R=0;R<P.length;R++)L.push(P[R]);return P}}for(var u=[],f=[],h=0,c=0,v=0;v<a.length&&o++<n;++v){var d=Ri(a[v]);if(FS(a[v])){l(a[v],u[u.length-1]||[],f);var g=a[v+1]?Ri(a[v+1]):null;if(d!==g){if(f.length){c=h,f.sort(function(D,A){return D.value-A.value});for(var p=[],y=0;y<f.length;++y){var m=f[y].value;(y===0||f[y-1].value!==m)&&(p.push(f[y]),m>=i[0]&&m<=i[1]&&h++)}var _=(i[1]-i[0])/t;if(h>_*1.5&&c>_/1.5||(u.push(p),h>_||r===a[v]))break}f=[]}}}for(var S=Mt(V(u,function(D){return Mt(D,function(A){return A.value>=i[0]&&A.value<=i[1]&&!A.notAdd})}),function(D){return D.length>0}),b=[],w=S.length-1,v=0;v<S.length;++v)for(var x=S[v],C=0;C<x.length;++C)b.push({value:x[C].value,level:w-v});b.sort(function(D,A){return D.value-A.value});for(var T=[],v=0;v<b.length;++v)(v===0||b[v].value!==b[v-1].value)&&T.push(b[v]);return T}Be.registerClass(uy);var wc=Be.prototype,Bn=Wi.prototype,LT=wt,IT=Math.floor,PT=Math.ceil,Ba=Math.pow,ce=Math.log,Of=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="log",e.base=10,e._originalScale=new Wi,e._interval=0,e}return t.prototype.getTicks=function(e){var i=this._originalScale,n=this._extent,a=i.getExtent(),o=Bn.getTicks.call(this,e);return V(o,function(s){var l=s.value,u=wt(Ba(this.base,l));return u=l===n[0]&&this._fixMin?Na(u,a[0]):u,u=l===n[1]&&this._fixMax?Na(u,a[1]):u,{value:u}},this)},t.prototype.setExtent=function(e,i){var n=ce(this.base);e=ce(Math.max(0,e))/n,i=ce(Math.max(0,i))/n,Bn.setExtent.call(this,e,i)},t.prototype.getExtent=function(){var e=this.base,i=wc.getExtent.call(this);i[0]=Ba(e,i[0]),i[1]=Ba(e,i[1]);var n=this._originalScale,a=n.getExtent();return this._fixMin&&(i[0]=Na(i[0],a[0])),this._fixMax&&(i[1]=Na(i[1],a[1])),i},t.prototype.unionExtent=function(e){this._originalScale.unionExtent(e);var i=this.base;e[0]=ce(e[0])/ce(i),e[1]=ce(e[1])/ce(i),wc.unionExtent.call(this,e)},t.prototype.unionExtentFromData=function(e,i){this.unionExtent(e.getApproximateExtent(i))},t.prototype.calcNiceTicks=function(e){e=e||10;var i=this._extent,n=i[1]-i[0];if(!(n===1/0||n<=0)){var a=$0(n),o=e/n*a;for(o<=.5&&(a*=10);!isNaN(a)&&Math.abs(a)<1&&Math.abs(a)>0;)a*=10;var s=[wt(PT(i[0]/a)*a),wt(IT(i[1]/a)*a)];this._interval=a,this._niceExtent=s}},t.prototype.calcNiceExtent=function(e){Bn.calcNiceExtent.call(this,e),this._fixMin=e.fixMin,this._fixMax=e.fixMax},t.prototype.parse=function(e){return e},t.prototype.contain=function(e){return e=ce(e)/ce(this.base),ss(e,this._extent)},t.prototype.normalize=function(e){return e=ce(e)/ce(this.base),ls(e,this._extent)},t.prototype.scale=function(e){return e=us(e,this._extent),Ba(this.base,e)},t.type="log",t}(Be),fy=Of.prototype;fy.getMinorTicks=Bn.getMinorTicks;fy.getLabel=Bn.getLabel;function Na(r,t){return LT(r,Ge(t))}Be.registerClass(Of);var RT=function(){function r(t,e,i){this._prepareParams(t,e,i)}return r.prototype._prepareParams=function(t,e,i){i[1]<i[0]&&(i=[NaN,NaN]),this._dataMin=i[0],this._dataMax=i[1];var n=this._isOrdinal=t.type==="ordinal";this._needCrossZero=t.type==="interval"&&e.getNeedCrossZero&&e.getNeedCrossZero();var a=e.get("min",!0);a==null&&(a=e.get("startValue",!0));var o=this._modelMinRaw=a;q(o)?this._modelMinNum=Fa(t,o({min:i[0],max:i[1]})):o!=="dataMin"&&(this._modelMinNum=Fa(t,o));var s=this._modelMaxRaw=e.get("max",!0);if(q(s)?this._modelMaxNum=Fa(t,s({min:i[0],max:i[1]})):s!=="dataMax"&&(this._modelMaxNum=Fa(t,s)),n)this._axisDataLen=e.getCategories().length;else{var l=e.get("boundaryGap"),u=F(l)?l:[l||0,l||0];typeof u[0]=="boolean"||typeof u[1]=="boolean"?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[yr(u[0],1),yr(u[1],1)]}},r.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,i=this._dataMax,n=this._axisDataLen,a=this._boundaryGapInner,o=t?null:i-e||Math.abs(e),s=this._modelMinRaw==="dataMin"?e:this._modelMinNum,l=this._modelMaxRaw==="dataMax"?i:this._modelMaxNum,u=s!=null,f=l!=null;s==null&&(s=t?n?0:NaN:e-a[0]*o),l==null&&(l=t?n?n-1:NaN:i+a[1]*o),(s==null||!isFinite(s))&&(s=NaN),(l==null||!isFinite(l))&&(l=NaN);var h=ao(s)||ao(l)||t&&!n;this._needCrossZero&&(s>0&&l>0&&!u&&(s=0),s<0&&l<0&&!f&&(l=0));var c=this._determinedMin,v=this._determinedMax;return c!=null&&(s=c,u=!0),v!=null&&(l=v,f=!0),{min:s,max:l,minFixed:u,maxFixed:f,isBlank:h}},r.prototype.modifyDataMinMax=function(t,e){this[OT[t]]=e},r.prototype.setDeterminedMinMax=function(t,e){var i=ET[t];this[i]=e},r.prototype.freeze=function(){this.frozen=!0},r}(),ET={min:"_determinedMin",max:"_determinedMax"},OT={min:"_dataMin",max:"_dataMax"};function kT(r,t,e){var i=r.rawExtentInfo;return i||(i=new RT(r,t,e),r.rawExtentInfo=i,i)}function Fa(r,t){return t==null?null:ao(t)?NaN:r.parse(t)}function hy(r,t){var e=r.type,i=kT(r,t,r.getExtent()).calculate();r.setBlank(i.isBlank);var n=i.min,a=i.max,o=t.ecModel;if(o&&e==="time"){var s=dT("bar",o),l=!1;if(M(s,function(h){l=l||h.getBaseAxis()===t.axis}),l){var u=gT(s),f=BT(n,a,t,u);n=f.min,a=f.max}}return{extent:[n,a],fixMin:i.minFixed,fixMax:i.maxFixed}}function BT(r,t,e,i){var n=e.axis.getExtent(),a=Math.abs(n[1]-n[0]),o=mT(i,e.axis);if(o===void 0)return{min:r,max:t};var s=1/0;M(o,function(v){s=Math.min(v.offset,s)});var l=-1/0;M(o,function(v){l=Math.max(v.offset+v.width,l)}),s=Math.abs(s),l=Math.abs(l);var u=s+l,f=t-r,h=1-(s+l)/a,c=f/h-f;return t+=c*(l/u),r-=c*(s/u),{min:r,max:t}}function bc(r,t){var e=t,i=hy(r,e),n=i.extent,a=e.get("splitNumber");r instanceof Of&&(r.base=e.get("logBase"));var o=r.type,s=e.get("interval"),l=o==="interval"||o==="time";r.setExtent(n[0],n[1]),r.calcNiceExtent({splitNumber:a,fixMin:i.fixMin,fixMax:i.fixMax,minInterval:l?e.get("minInterval"):null,maxInterval:l?e.get("maxInterval"):null}),s!=null&&r.setInterval&&r.setInterval(s)}function NT(r,t){if(t=t||r.get("type"),t)switch(t){case"category":return new Ef({ordinalMeta:r.getOrdinalMeta?r.getOrdinalMeta():r.getCategories(),extent:[1/0,-1/0]});case"time":return new uy({locale:r.ecModel.getLocaleModel(),useUTC:r.ecModel.get("useUTC")});default:return new(Be.getClass(t)||Wi)}}function FT(r){var t=r.scale.getExtent(),e=t[0],i=t[1];return!(e>0&&i>0||e<0&&i<0)}function Ui(r){var t=r.getLabelModel().get("formatter"),e=r.type==="category"?r.scale.getExtent()[0]:null;return r.scale.type==="time"?function(i){return function(n,a){return r.scale.getFormattedLabel(n,a,i)}}(t):G(t)?function(i){return function(n){var a=r.scale.getLabel(n),o=i.replace("{value}",a??"");return o}}(t):q(t)?function(i){return function(n,a){return e!=null&&(a=n.value-e),i(kf(r,n),a,n.level!=null?{level:n.level}:null)}}(t):function(i){return r.scale.getLabel(i)}}function kf(r,t){return r.type==="category"?r.scale.getLabel(t):t.value}function zT(r){var t=r.model,e=r.scale;if(!(!t.get(["axisLabel","show"])||e.isBlank())){var i,n,a=e.getExtent();e instanceof Ef?n=e.count():(i=e.getTicks(),n=i.length);var o=r.getLabelModel(),s=Ui(r),l,u=1;n>40&&(u=Math.ceil(n/40));for(var f=0;f<n;f+=u){var h=i?i[f]:{value:a[0]+f},c=s(h,f),v=o.getTextRect(c),d=HT(v,o.get("rotate")||0);l?l.union(d):l=d}return l}}function HT(r,t){var e=t*Math.PI/180,i=r.width,n=r.height,a=i*Math.abs(Math.cos(e))+Math.abs(n*Math.sin(e)),o=i*Math.abs(Math.sin(e))+Math.abs(n*Math.cos(e)),s=new nt(r.x,r.y,a,o);return s}function Bf(r){var t=r.get("interval");return t??"auto"}function vy(r){return r.type==="category"&&Bf(r.getLabelModel())===0}function GT(r,t){var e={};return M(r.mapDimensionsAll(t),function(i){e[eT(r,i)]=!0}),dt(e)}var VT=function(){function r(){}return r.prototype.getNeedCrossZero=function(){var t=this.option;return!t.scale},r.prototype.getCoordSysModel=function(){},r}(),xc=[],WT={registerPreprocessor:Kg,registerProcessor:Qg,registerPostInit:Ix,registerPostUpdate:Px,registerUpdateLifecycle:Pf,registerAction:Vi,registerCoordinateSystem:Rx,registerLayout:Ex,registerVisual:ri,registerTransform:kx,registerLoading:Jg,registerMap:Ox,registerImpl:ux,PRIORITY:xx,ComponentModel:ot,ComponentView:_e,SeriesModel:$e,ChartView:me,registerComponentModel:function(r){ot.registerClass(r)},registerComponentView:function(r){_e.registerClass(r)},registerSeriesModel:function(r){$e.registerClass(r)},registerChartView:function(r){me.registerClass(r)},registerSubTypeDefaulter:function(r,t){ot.registerSubTypeDefaulter(r,t)},registerPainter:function(r,t){H0(r,t)}};function ei(r){if(F(r)){M(r,function(t){ei(t)});return}lt(xc,r)>=0||(xc.push(r),q(r)&&(r={install:r}),r.install(WT))}var Kn=_t();function cy(r,t){var e=V(t,function(i){return r.scale.parse(i)});return r.type==="time"&&e.length>0&&(e.sort(),e.unshift(e[0]),e.push(e[e.length-1])),e}function UT(r){var t=r.getLabelModel().get("customValues");if(t){var e=Ui(r),i=r.scale.getExtent(),n=cy(r,t),a=Mt(n,function(o){return o>=i[0]&&o<=i[1]});return{labels:V(a,function(o){var s={value:o};return{formattedLabel:e(s),rawLabel:r.scale.getLabel(s),tickValue:o}})}}return r.type==="category"?XT(r):ZT(r)}function YT(r,t){var e=r.getTickModel().get("customValues");if(e){var i=r.scale.getExtent(),n=cy(r,e);return{ticks:Mt(n,function(a){return a>=i[0]&&a<=i[1]})}}return r.type==="category"?$T(r,t):{ticks:V(r.scale.getTicks(),function(a){return a.value})}}function XT(r){var t=r.getLabelModel(),e=dy(r,t);return!t.get("show")||r.scale.isBlank()?{labels:[],labelCategoryInterval:e.labelCategoryInterval}:e}function dy(r,t){var e=py(r,"labels"),i=Bf(t),n=gy(e,i);if(n)return n;var a,o;return q(i)?a=_y(r,i):(o=i==="auto"?qT(r):i,a=my(r,o)),yy(e,i,{labels:a,labelCategoryInterval:o})}function $T(r,t){var e=py(r,"ticks"),i=Bf(t),n=gy(e,i);if(n)return n;var a,o;if((!t.get("show")||r.scale.isBlank())&&(a=[]),q(i))a=_y(r,i,!0);else if(i==="auto"){var s=dy(r,r.getLabelModel());o=s.labelCategoryInterval,a=V(s.labels,function(l){return l.tickValue})}else o=i,a=my(r,o,!0);return yy(e,i,{ticks:a,tickCategoryInterval:o})}function ZT(r){var t=r.scale.getTicks(),e=Ui(r);return{labels:V(t,function(i,n){return{level:i.level,formattedLabel:e(i,n),rawLabel:r.scale.getLabel(i),tickValue:i.value}})}}function py(r,t){return Kn(r)[t]||(Kn(r)[t]=[])}function gy(r,t){for(var e=0;e<r.length;e++)if(r[e].key===t)return r[e].value}function yy(r,t,e){return r.push({key:t,value:e}),e}function qT(r){var t=Kn(r).autoInterval;return t??(Kn(r).autoInterval=r.calculateCategoryInterval())}function KT(r){var t=QT(r),e=Ui(r),i=(t.axisRotate-t.labelRotate)/180*Math.PI,n=r.scale,a=n.getExtent(),o=n.count();if(a[1]-a[0]<1)return 0;var s=1;o>40&&(s=Math.max(1,Math.floor(o/40)));for(var l=a[0],u=r.dataToCoord(l+1)-r.dataToCoord(l),f=Math.abs(u*Math.cos(i)),h=Math.abs(u*Math.sin(i)),c=0,v=0;l<=a[1];l+=s){var d=0,g=0,p=Zu(e({value:l}),t.font,"center","top");d=p.width*1.3,g=p.height*1.3,c=Math.max(c,d,7),v=Math.max(v,g,7)}var y=c/f,m=v/h;isNaN(y)&&(y=1/0),isNaN(m)&&(m=1/0);var _=Math.max(0,Math.floor(Math.min(y,m))),S=Kn(r.model),b=r.getExtent(),w=S.lastAutoInterval,x=S.lastTickCount;return w!=null&&x!=null&&Math.abs(w-_)<=1&&Math.abs(x-o)<=1&&w>_&&S.axisExtent0===b[0]&&S.axisExtent1===b[1]?_=w:(S.lastTickCount=o,S.lastAutoInterval=_,S.axisExtent0=b[0],S.axisExtent1=b[1]),_}function QT(r){var t=r.getLabelModel();return{axisRotate:r.getRotate?r.getRotate():r.isHorizontal&&!r.isHorizontal()?90:0,labelRotate:t.get("rotate")||0,font:t.getFont()}}function my(r,t,e){var i=Ui(r),n=r.scale,a=n.getExtent(),o=r.getLabelModel(),s=[],l=Math.max((t||0)+1,1),u=a[0],f=n.count();u!==0&&l>1&&f/l>2&&(u=Math.round(Math.ceil(u/l)*l));var h=vy(r),c=o.get("showMinLabel")||h,v=o.get("showMaxLabel")||h;c&&u!==a[0]&&g(a[0]);for(var d=u;d<=a[1];d+=l)g(d);v&&d-l!==a[1]&&g(a[1]);function g(p){var y={value:p};s.push(e?p:{formattedLabel:i(y),rawLabel:n.getLabel(y),tickValue:p})}return s}function _y(r,t,e){var i=r.scale,n=Ui(r),a=[];return M(i.getTicks(),function(o){var s=i.getLabel(o),l=o.value;t(o.value,s)&&a.push(e?l:{formattedLabel:n(o),rawLabel:s,tickValue:l})}),a}var Tc=[0,1],JT=function(){function r(t,e,i){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=i||[0,0]}return r.prototype.contain=function(t){var e=this._extent,i=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]);return t>=i&&t<=n},r.prototype.containData=function(t){return this.scale.contain(t)},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.getPixelPrecision=function(t){return W0(t||this.scale.getExtent(),this._extent)},r.prototype.setExtent=function(t,e){var i=this._extent;i[0]=t,i[1]=e},r.prototype.dataToCoord=function(t,e){var i=this._extent,n=this.scale;return t=n.normalize(t),this.onBand&&n.type==="ordinal"&&(i=i.slice(),Cc(i,n.count())),Kl(t,Tc,i,e)},r.prototype.coordToData=function(t,e){var i=this._extent,n=this.scale;this.onBand&&n.type==="ordinal"&&(i=i.slice(),Cc(i,n.count()));var a=Kl(t,i,Tc,e);return this.scale.scale(a)},r.prototype.pointToData=function(t,e){},r.prototype.getTicksCoords=function(t){t=t||{};var e=t.tickModel||this.getTickModel(),i=YT(this,e),n=i.ticks,a=V(n,function(s){return{coord:this.dataToCoord(this.scale.type==="ordinal"?this.scale.getRawOrdinalNumber(s):s),tickValue:s}},this),o=e.get("alignWithLabel");return jT(this,a,o,t.clamp),a},r.prototype.getMinorTicksCoords=function(){if(this.scale.type==="ordinal")return[];var t=this.model.getModel("minorTick"),e=t.get("splitNumber");e>0&&e<100||(e=5);var i=this.scale.getMinorTicks(e),n=V(i,function(a){return V(a,function(o){return{coord:this.dataToCoord(o),tickValue:o}},this)},this);return n},r.prototype.getViewLabels=function(){return UT(this).labels},r.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},r.prototype.getTickModel=function(){return this.model.getModel("axisTick")},r.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),i=e[1]-e[0]+(this.onBand?1:0);i===0&&(i=1);var n=Math.abs(t[1]-t[0]);return Math.abs(n)/i},r.prototype.calculateCategoryInterval=function(){return KT(this)},r}();function Cc(r,t){var e=r[1]-r[0],i=t,n=e/i/2;r[0]+=n,r[1]-=n}function jT(r,t,e,i){var n=t.length;if(!r.onBand||e||!n)return;var a=r.getExtent(),o,s;if(n===1)t[0].coord=a[0],o=t[1]={coord:a[1],tickValue:t[0].tickValue};else{var l=t[n-1].tickValue-t[0].tickValue,u=(t[n-1].coord-t[0].coord)/l;M(t,function(v){v.coord-=u/2});var f=r.scale.getExtent();s=1+f[1]-t[n-1].tickValue,o={coord:t[n-1].coord+u*s,tickValue:f[1]+1},t.push(o)}var h=a[0]>a[1];c(t[0].coord,a[0])&&(i?t[0].coord=a[0]:t.shift()),i&&c(a[0],t[0].coord)&&t.unshift({coord:a[0]}),c(a[1],o.coord)&&(i?o.coord=a[1]:t.pop()),i&&c(o.coord,a[1])&&t.push({coord:a[1]});function c(v,d){return v=wt(v),d=wt(d),h?v>d:v<d}}function Sy(r,t,e,i,n,a,o,s){var l=n-r,u=a-t,f=e-r,h=i-t,c=Math.sqrt(f*f+h*h);f/=c,h/=c;var v=l*f+u*h,d=v/c;d*=c;var g=o[0]=r+d*f,p=o[1]=t+d*h;return Math.sqrt((g-n)*(g-n)+(p-a)*(p-a))}var lr=new Z,pt=new Z,Dt=new Z,ur=new Z,Ae=new Z,Ro=[],Bt=new Z;function tC(r,t){if(t<=180&&t>0){t=t/180*Math.PI,lr.fromArray(r[0]),pt.fromArray(r[1]),Dt.fromArray(r[2]),Z.sub(ur,lr,pt),Z.sub(Ae,Dt,pt);var e=ur.len(),i=Ae.len();if(!(e<.001||i<.001)){ur.scale(1/e),Ae.scale(1/i);var n=ur.dot(Ae),a=Math.cos(t);if(a<n){var o=Sy(pt.x,pt.y,Dt.x,Dt.y,lr.x,lr.y,Ro);Bt.fromArray(Ro),Bt.scaleAndAdd(Ae,o/Math.tan(Math.PI-t));var s=Dt.x!==pt.x?(Bt.x-pt.x)/(Dt.x-pt.x):(Bt.y-pt.y)/(Dt.y-pt.y);if(isNaN(s))return;s<0?Z.copy(Bt,pt):s>1&&Z.copy(Bt,Dt),Bt.toArray(r[1])}}}}function eC(r,t,e){if(e<=180&&e>0){e=e/180*Math.PI,lr.fromArray(r[0]),pt.fromArray(r[1]),Dt.fromArray(r[2]),Z.sub(ur,pt,lr),Z.sub(Ae,Dt,pt);var i=ur.len(),n=Ae.len();if(!(i<.001||n<.001)){ur.scale(1/i),Ae.scale(1/n);var a=ur.dot(t),o=Math.cos(e);if(a<o){var s=Sy(pt.x,pt.y,Dt.x,Dt.y,lr.x,lr.y,Ro);Bt.fromArray(Ro);var l=Math.PI/2,u=Math.acos(Ae.dot(t)),f=l+u-e;if(f>=l)Z.copy(Bt,Dt);else{Bt.scaleAndAdd(Ae,s/Math.tan(Math.PI/2-f));var h=Dt.x!==pt.x?(Bt.x-pt.x)/(Dt.x-pt.x):(Bt.y-pt.y)/(Dt.y-pt.y);if(isNaN(h))return;h<0?Z.copy(Bt,pt):h>1&&Z.copy(Bt,Dt)}Bt.toArray(r[1])}}}}function _l(r,t,e,i){var n=e==="normal",a=n?r:r.ensureState(e);a.ignore=t;var o=i.get("smooth");o&&o===!0&&(o=.3),a.shape=a.shape||{},o>0&&(a.shape.smooth=o);var s=i.getModel("lineStyle").getLineStyle();n?r.useStyle(s):a.style=s}function rC(r,t){var e=t.smooth,i=t.points;if(i)if(r.moveTo(i[0][0],i[0][1]),e>0&&i.length>=3){var n=kl(i[0],i[1]),a=kl(i[1],i[2]);if(!n||!a){r.lineTo(i[1][0],i[1][1]),r.lineTo(i[2][0],i[2][1]);return}var o=Math.min(n,a)*e,s=cs([],i[1],i[0],o/n),l=cs([],i[1],i[2],o/a),u=cs([],s,l,.5);r.bezierCurveTo(s[0],s[1],s[0],s[1],u[0],u[1]),r.bezierCurveTo(l[0],l[1],l[0],l[1],i[2][0],i[2][1])}else for(var f=1;f<i.length;f++)r.lineTo(i[f][0],i[f][1])}function iC(r,t,e){var i=r.getTextGuideLine(),n=r.getTextContent();if(!n){i&&r.removeTextGuideLine();return}for(var a=t.normal,o=a.get("show"),s=n.ignore,l=0;l<yo.length;l++){var u=yo[l],f=t[u],h=u==="normal";if(f){var c=f.get("show"),v=h?s:K(n.states[u]&&n.states[u].ignore,s);if(v||!K(c,o)){var d=h?i:i&&i.states[u];d&&(d.ignore=!0),i&&_l(i,!0,u,f);continue}i||(i=new ia,r.setTextGuideLine(i),!h&&(s||!o)&&_l(i,!0,"normal",t.normal),r.stateProxy&&(i.stateProxy=r.stateProxy)),_l(i,!1,u,f)}}if(i){at(i.style,e),i.style.fill=null;var g=a.get("showAbove"),p=r.textGuideLineConfig=r.textGuideLineConfig||{};p.showAbove=g||!1,i.buildPath=rC}}function nC(r,t){t=t||"labelLine";for(var e={normal:r.getModel(t)},i=0;i<fe.length;i++){var n=fe[i];e[n]=r.getModel([n,t])}return e}function aC(r){for(var t=[],e=0;e<r.length;e++){var i=r[e];if(!i.defaultAttr.ignore){var n=i.label,a=n.getComputedTransform(),o=n.getBoundingRect(),s=!a||a[1]<1e-5&&a[2]<1e-5,l=n.style.margin||0,u=o.clone();u.applyTransform(a),u.x-=l/2,u.y-=l/2,u.width+=l,u.height+=l;var f=s?new wo(o,a):null;t.push({label:n,labelLine:i.labelLine,rect:u,localRect:o,obb:f,priority:i.priority,defaultAttr:i.defaultAttr,layoutOption:i.computedLayoutOption,axisAligned:s,transform:a})}}return t}function oC(r,t,e,i,n,a){var o=r.length;if(o<2)return;r.sort(function(w,x){return w.rect[t]-x.rect[t]});for(var s=0,l,u=!1,f=0;f<o;f++){var h=r[f],c=h.rect;l=c[t]-s,l<0&&(c[t]-=l,h.label[t]-=l,u=!0),s=c[t]+c[e]}var v=r[0],d=r[o-1],g,p;y(),g<0&&S(-g,.8),p<0&&S(p,.8),y(),m(g,p,1),m(p,g,-1),y(),g<0&&b(-g),p<0&&b(p);function y(){g=v.rect[t]-i,p=n-d.rect[t]-d.rect[e]}function m(w,x,C){if(w<0){var T=Math.min(x,-w);if(T>0){_(T*C,0,o);var D=T+w;D<0&&S(-D*C,1)}else S(-w*C,1)}}function _(w,x,C){w!==0&&(u=!0);for(var T=x;T<C;T++){var D=r[T],A=D.rect;A[t]+=w,D.label[t]+=w}}function S(w,x){for(var C=[],T=0,D=1;D<o;D++){var A=r[D-1].rect,L=Math.max(r[D].rect[t]-A[t]-A[e],0);C.push(L),T+=L}if(T){var P=Math.min(Math.abs(w)/T,x);if(w>0)for(var D=0;D<o-1;D++){var I=C[D]*P;_(I,0,D+1)}else for(var D=o-1;D>0;D--){var I=C[D-1]*P;_(-I,D,o)}}}function b(w){var x=w<0?-1:1;w=Math.abs(w);for(var C=Math.ceil(w/(o-1)),T=0;T<o-1;T++)if(x>0?_(C,0,T+1):_(-C,o-T-1,o),w-=C,w<=0)return}return u}function sC(r,t,e,i){return oC(r,"y","height",t,e)}function lC(r){var t=[];r.sort(function(g,p){return p.priority-g.priority});var e=new nt(0,0,0,0);function i(g){if(!g.ignore){var p=g.ensureState("emphasis");p.ignore==null&&(p.ignore=!1)}g.ignore=!0}for(var n=0;n<r.length;n++){var a=r[n],o=a.axisAligned,s=a.localRect,l=a.transform,u=a.label,f=a.labelLine;e.copy(a.rect),e.width-=.1,e.height-=.1,e.x+=.05,e.y+=.05;for(var h=a.obb,c=!1,v=0;v<t.length;v++){var d=t[v];if(e.intersect(d.rect)){if(o&&d.axisAligned){c=!0;break}if(d.obb||(d.obb=new wo(d.localRect,d.transform)),h||(h=new wo(s,l)),h.intersect(d.obb)){c=!0;break}}}c?(i(u),f&&i(f)):(u.attr("ignore",a.defaultAttr.ignore),f&&f.attr("ignore",a.defaultAttr.labelGuideIgnore),t.push(a))}}function Dc(r,t,e){var i=Fi.createCanvas(),n=t.getWidth(),a=t.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=n+"px",o.height=a+"px",i.setAttribute("data-zr-dom-id",r)),i.width=n*e,i.height=a*e,i}var Sl=function(r){N(t,r);function t(e,i,n){var a=r.call(this)||this;a.motionBlur=!1,a.lastFrameAlpha=.7,a.dpr=1,a.virtual=!1,a.config={},a.incremental=!1,a.zlevel=0,a.maxRepaintRectCount=5,a.__dirty=!0,a.__firstTimePaint=!0,a.__used=!1,a.__drawIndex=0,a.__startIndex=0,a.__endIndex=0,a.__prevStartIndex=null,a.__prevEndIndex=null;var o;n=n||vo,typeof e=="string"?o=Dc(e,i,n):W(e)&&(o=e,e=o.id),a.id=e,a.dom=o;var s=o.style;return s&&(xd(o),o.onselectstart=function(){return!1},s.padding="0",s.margin="0",s.borderWidth="0"),a.painter=i,a.dpr=n,a}return t.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},t.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},t.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},t.prototype.setUnpainted=function(){this.__firstTimePaint=!0},t.prototype.createBackBuffer=function(){var e=this.dpr;this.domBack=Dc("back-"+this.id,this.painter,e),this.ctxBack=this.domBack.getContext("2d"),e!==1&&this.ctxBack.scale(e,e)},t.prototype.createRepaintRects=function(e,i,n,a){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var o=[],s=this.maxRepaintRectCount,l=!1,u=new nt(0,0,0,0);function f(m){if(!(!m.isFinite()||m.isZero()))if(o.length===0){var _=new nt(0,0,0,0);_.copy(m),o.push(_)}else{for(var S=!1,b=1/0,w=0,x=0;x<o.length;++x){var C=o[x];if(C.intersect(m)){var T=new nt(0,0,0,0);T.copy(C),T.union(m),o[x]=T,S=!0;break}else if(l){u.copy(m),u.union(C);var D=m.width*m.height,A=C.width*C.height,L=u.width*u.height,P=L-D-A;P<b&&(b=P,w=x)}}if(l&&(o[w].union(m),S=!0),!S){var _=new nt(0,0,0,0);_.copy(m),o.push(_)}l||(l=o.length>=s)}}for(var h=this.__startIndex;h<this.__endIndex;++h){var c=e[h];if(c){var v=c.shouldBePainted(n,a,!0,!0),d=c.__isRendered&&(c.__dirty&Kt||!v)?c.getPrevPaintRect():null;d&&f(d);var g=v&&(c.__dirty&Kt||!c.__isRendered)?c.getPaintRect():null;g&&f(g)}}for(var h=this.__prevStartIndex;h<this.__prevEndIndex;++h){var c=i[h],v=c&&c.shouldBePainted(n,a,!0,!0);if(c&&(!v||!c.__zr)&&c.__isRendered){var d=c.getPrevPaintRect();d&&f(d)}}var p;do{p=!1;for(var h=0;h<o.length;){if(o[h].isZero()){o.splice(h,1);continue}for(var y=h+1;y<o.length;)o[h].intersect(o[y])?(p=!0,o[h].union(o[y]),o.splice(y,1)):y++;h++}}while(p);return this._paintRects=o,o},t.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},t.prototype.resize=function(e,i){var n=this.dpr,a=this.dom,o=a.style,s=this.domBack;o&&(o.width=e+"px",o.height=i+"px"),a.width=e*n,a.height=i*n,s&&(s.width=e*n,s.height=i*n,n!==1&&this.ctxBack.scale(n,n))},t.prototype.clear=function(e,i,n){var a=this.dom,o=this.ctx,s=a.width,l=a.height;i=i||this.clearColor;var u=this.motionBlur&&!e,f=this.lastFrameAlpha,h=this.dpr,c=this;u&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(a,0,0,s/h,l/h));var v=this.domBack;function d(g,p,y,m){if(o.clearRect(g,p,y,m),i&&i!=="transparent"){var _=void 0;if(Oo(i)){var S=i.global||i.__width===y&&i.__height===m;_=S&&i.__canvasGradient||mu(o,i,{x:0,y:0,width:y,height:m}),i.__canvasGradient=_,i.__width=y,i.__height=m}else bm(i)&&(i.scaleX=i.scaleX||h,i.scaleY=i.scaleY||h,_=_u(o,i,{dirty:function(){c.setUnpainted(),c.painter.refresh()}}));o.save(),o.fillStyle=_||i,o.fillRect(g,p,y,m),o.restore()}u&&(o.save(),o.globalAlpha=f,o.drawImage(v,g,p,y,m),o.restore())}!n||u?d(0,0,s,l):n.length&&M(n,function(g){d(g.x*h,g.y*h,g.width*h,g.height*h)})},t}(Oe),Mc=1e5,Vr=314159,za=.01,uC=.001;function fC(r){return r?r.__builtin__?!0:!(typeof r.resize!="function"||typeof r.refresh!="function"):!1}function hC(r,t){var e=document.createElement("div");return e.style.cssText=["position:relative","width:"+r+"px","height:"+t+"px","padding:0","margin:0","border-width:0"].join(";")+";",e}var vC=function(){function r(t,e,i,n){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var a=!t.nodeName||t.nodeName.toUpperCase()==="CANVAS";this._opts=i=O({},i||{}),this.dpr=i.devicePixelRatio||vo,this._singleCanvas=a,this.root=t;var o=t.style;o&&(xd(t),t.innerHTML=""),this.storage=e;var s=this._zlevelList;this._prevDisplayList=[];var l=this._layers;if(a){var f=t,h=f.width,c=f.height;i.width!=null&&(h=i.width),i.height!=null&&(c=i.height),this.dpr=i.devicePixelRatio||1,f.width=h*this.dpr,f.height=c*this.dpr,this._width=h,this._height=c;var v=new Sl(f,this,this.dpr);v.__builtin__=!0,v.initContext(),l[Vr]=v,v.zlevel=Vr,s.push(Vr),this._domRoot=t}else{this._width=Pa(t,0,i),this._height=Pa(t,1,i);var u=this._domRoot=hC(this._width,this._height);t.appendChild(u)}}return r.prototype.getType=function(){return"canvas"},r.prototype.isSingleCanvas=function(){return this._singleCanvas},r.prototype.getViewportRoot=function(){return this._domRoot},r.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},r.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),i=this._prevDisplayList,n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,i,t,this._redrawId);for(var a=0;a<n.length;a++){var o=n[a],s=this._layers[o];if(!s.__builtin__&&s.refresh){var l=a===0?this._backgroundColor:null;s.refresh(l)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},r.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},r.prototype._paintHoverList=function(t){var e=t.length,i=this._hoverlayer;if(i&&i.clear(),!!e){for(var n={inHover:!0,viewWidth:this._width,viewHeight:this._height},a,o=0;o<e;o++){var s=t[o];s.__inHover&&(i||(i=this._hoverlayer=this.getLayer(Mc)),a||(a=i.ctx,a.save()),Xr(a,s,n,o===e-1))}a&&a.restore()}},r.prototype.getHoverLayer=function(){return this.getLayer(Mc)},r.prototype.paintOne=function(t,e){Eg(t,e)},r.prototype._paintList=function(t,e,i,n){if(this._redrawId===n){i=i||!1,this._updateLayerStatus(t);var a=this._doPaintList(t,e,i),o=a.finished,s=a.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),s&&this._paintHoverList(t),o)this.eachLayer(function(u){u.afterBrush&&u.afterBrush()});else{var l=this;oo(function(){l._paintList(t,e,i,n)})}}},r.prototype._compositeManually=function(){var t=this.getLayer(Vr).ctx,e=this._domRoot.width,i=this._domRoot.height;t.clearRect(0,0,e,i),this.eachBuiltinLayer(function(n){n.virtual&&t.drawImage(n.dom,0,0,e,i)})},r.prototype._doPaintList=function(t,e,i){for(var n=this,a=[],o=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var l=this._zlevelList[s],u=this._layers[l];u.__builtin__&&u!==this._hoverlayer&&(u.__dirty||i)&&a.push(u)}for(var f=!0,h=!1,c=function(g){var p=a[g],y=p.ctx,m=o&&p.createRepaintRects(t,e,v._width,v._height),_=i?p.__startIndex:p.__drawIndex,S=!i&&p.incremental&&Date.now,b=S&&Date.now(),w=p.zlevel===v._zlevelList[0]?v._backgroundColor:null;if(p.__startIndex===p.__endIndex)p.clear(!1,w,m);else if(_===p.__startIndex){var x=t[_];(!x.incremental||!x.notClear||i)&&p.clear(!1,w,m)}_===-1&&(console.error("For some unknown reason. drawIndex is -1"),_=p.__startIndex);var C,T=function(P){var I={inHover:!1,allClipped:!1,prevEl:null,viewWidth:n._width,viewHeight:n._height};for(C=_;C<p.__endIndex;C++){var R=t[C];if(R.__inHover&&(h=!0),n._doPaintEl(R,p,o,P,I,C===p.__endIndex-1),S){var E=Date.now()-b;if(E>15)break}}I.prevElClipPaths&&y.restore()};if(m)if(m.length===0)C=p.__endIndex;else for(var D=v.dpr,A=0;A<m.length;++A){var L=m[A];y.save(),y.beginPath(),y.rect(L.x*D,L.y*D,L.width*D,L.height*D),y.clip(),T(L),y.restore()}else y.save(),T(),y.restore();p.__drawIndex=C,p.__drawIndex<p.__endIndex&&(f=!1)},v=this,d=0;d<a.length;d++)c(d);return $.wxa&&M(this._layers,function(g){g&&g.ctx&&g.ctx.draw&&g.ctx.draw()}),{finished:f,needsRefreshHover:h}},r.prototype._doPaintEl=function(t,e,i,n,a,o){var s=e.ctx;if(i){var l=t.getPaintRect();(!n||l&&l.intersect(n))&&(Xr(s,t,a,o),t.setPrevPaintRect(l))}else Xr(s,t,a,o)},r.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=Vr);var i=this._layers[t];return i||(i=new Sl("zr_"+t,this,this.dpr),i.zlevel=t,i.__builtin__=!0,this._layerConfig[t]?it(i,this._layerConfig[t],!0):this._layerConfig[t-za]&&it(i,this._layerConfig[t-za],!0),e&&(i.virtual=e),this.insertLayer(t,i),i.initContext()),i},r.prototype.insertLayer=function(t,e){var i=this._layers,n=this._zlevelList,a=n.length,o=this._domRoot,s=null,l=-1;if(!i[t]&&fC(e)){if(a>0&&t>n[0]){for(l=0;l<a-1&&!(n[l]<t&&n[l+1]>t);l++);s=i[n[l]]}if(n.splice(l+1,0,t),i[t]=e,!e.virtual)if(s){var u=s.dom;u.nextSibling?o.insertBefore(e.dom,u.nextSibling):o.appendChild(e.dom)}else o.firstChild?o.insertBefore(e.dom,o.firstChild):o.appendChild(e.dom);e.painter||(e.painter=this)}},r.prototype.eachLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n];t.call(e,this._layers[a],a)}},r.prototype.eachBuiltinLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],o=this._layers[a];o.__builtin__&&t.call(e,o,a)}},r.prototype.eachOtherLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],o=this._layers[a];o.__builtin__||t.call(e,o,a)}},r.prototype.getLayers=function(){return this._layers},r.prototype._updateLayerStatus=function(t){this.eachBuiltinLayer(function(h,c){h.__dirty=h.__used=!1});function e(h){a&&(a.__endIndex!==h&&(a.__dirty=!0),a.__endIndex=h)}if(this._singleCanvas)for(var i=1;i<t.length;i++){var n=t[i];if(n.zlevel!==t[i-1].zlevel||n.incremental){this._needsManuallyCompositing=!0;break}}var a=null,o=0,s,l;for(l=0;l<t.length;l++){var n=t[l],u=n.zlevel,f=void 0;s!==u&&(s=u,o=0),n.incremental?(f=this.getLayer(u+uC,this._needsManuallyCompositing),f.incremental=!0,o=1):f=this.getLayer(u+(o>0?za:0),this._needsManuallyCompositing),f.__builtin__||Hu("ZLevel "+u+" has been used by unkown layer "+f.id),f!==a&&(f.__used=!0,f.__startIndex!==l&&(f.__dirty=!0),f.__startIndex=l,f.incremental?f.__drawIndex=-1:f.__drawIndex=l,e(l),a=f),n.__dirty&Kt&&!n.__inHover&&(f.__dirty=!0,f.incremental&&f.__drawIndex<0&&(f.__drawIndex=l))}e(l),this.eachBuiltinLayer(function(h,c){!h.__used&&h.getElementCount()>0&&(h.__dirty=!0,h.__startIndex=h.__endIndex=h.__drawIndex=0),h.__dirty&&h.__drawIndex<0&&(h.__drawIndex=h.__startIndex)})},r.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},r.prototype._clearLayer=function(t){t.clear()},r.prototype.setBackgroundColor=function(t){this._backgroundColor=t,M(this._layers,function(e){e.setUnpainted()})},r.prototype.configLayer=function(t,e){if(e){var i=this._layerConfig;i[t]?it(i[t],e,!0):i[t]=e;for(var n=0;n<this._zlevelList.length;n++){var a=this._zlevelList[n];if(a===t||a===t+za){var o=this._layers[a];it(o,i[t],!0)}}}},r.prototype.delLayer=function(t){var e=this._layers,i=this._zlevelList,n=e[t];n&&(n.dom.parentNode.removeChild(n.dom),delete e[t],i.splice(lt(i,t),1))},r.prototype.resize=function(t,e){if(this._domRoot.style){var i=this._domRoot;i.style.display="none";var n=this._opts,a=this.root;if(t!=null&&(n.width=t),e!=null&&(n.height=e),t=Pa(a,0,n),e=Pa(a,1,n),i.style.display="",this._width!==t||e!==this._height){i.style.width=t+"px",i.style.height=e+"px";for(var o in this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(t==null||e==null)return;this._width=t,this._height=e,this.getLayer(Vr).resize(t,e)}return this},r.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},r.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},r.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[Vr].dom;var e=new Sl("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var i=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,a=e.dom.height;this.eachLayer(function(h){h.__builtin__?i.drawImage(h.dom,0,0,n,a):h.renderToCanvas&&(i.save(),h.renderToCanvas(i),i.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},s=this.storage.getDisplayList(!0),l=0,u=s.length;l<u;l++){var f=s[l];Xr(i,f,o,l===u-1)}return e.dom},r.prototype.getWidth=function(){return this._width},r.prototype.getHeight=function(){return this._height},r}();function YM(r){r.registerPainter("canvas",vC)}var cC=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.hasSymbolVisual=!0,e}return t.prototype.getInitialData=function(e){return nT(null,this,{useEncodeDefaulter:!0})},t.prototype.getLegendIcon=function(e){var i=new Ot,n=Ni("line",0,e.itemHeight/2,e.itemWidth,0,e.lineStyle.stroke,!1);i.add(n),n.setStyle(e.lineStyle);var a=this.getData().getVisual("symbol"),o=this.getData().getVisual("symbolRotate"),s=a==="none"?"circle":a,l=e.itemHeight*.8,u=Ni(s,(e.itemWidth-l)/2,(e.itemHeight-l)/2,l,l,e.itemStyle.fill);i.add(u),u.setStyle(e.itemStyle);var f=e.iconRotate==="inherit"?o:e.iconRotate||0;return u.rotation=f*Math.PI/180,u.setOrigin([e.itemWidth/2,e.itemHeight/2]),s.indexOf("empty")>-1&&(u.style.stroke=u.style.fill,u.style.fill="#fff",u.style.lineWidth=2),i},t.type="series.line",t.dependencies=["grid","polar"],t.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1},t}($e);function wy(r,t){var e=r.mapDimensionsAll("defaultedLabel"),i=e.length;if(i===1){var n=Bi(r,t,e[0]);return n!=null?n+"":null}else if(i){for(var a=[],o=0;o<e.length;o++)a.push(Bi(r,t,e[o]));return a.join(" ")}}function dC(r,t){var e=r.mapDimensionsAll("defaultedLabel");if(!F(t))return t+"";for(var i=[],n=0;n<e.length;n++){var a=r.getDimensionIndex(e[n]);a>=0&&i.push(t[a])}return i.join(" ")}var Nf=function(r){N(t,r);function t(e,i,n,a){var o=r.call(this)||this;return o.updateData(e,i,n,a),o}return t.prototype._createSymbol=function(e,i,n,a,o){this.removeAll();var s=Ni(e,-1,-1,2,2,null,o);s.attr({z2:100,culling:!0,scaleX:a[0]/2,scaleY:a[1]/2}),s.drift=pC,this._symbolType=e,this.add(s)},t.prototype.stopSymbolAnimation=function(e){this.childAt(0).stopAnimation(null,e)},t.prototype.getSymbolType=function(){return this._symbolType},t.prototype.getSymbolPath=function(){return this.childAt(0)},t.prototype.highlight=function(){mo(this.childAt(0))},t.prototype.downplay=function(){_o(this.childAt(0))},t.prototype.setZ=function(e,i){var n=this.childAt(0);n.zlevel=e,n.z=i},t.prototype.setDraggable=function(e,i){var n=this.childAt(0);n.draggable=e,n.cursor=!i&&e?"move":n.cursor},t.prototype.updateData=function(e,i,n,a){this.silent=!1;var o=e.getItemVisual(i,"symbol")||"circle",s=e.hostModel,l=t.getSymbolSize(e,i),u=o!==this._symbolType,f=a&&a.disableAnimation;if(u){var h=e.getItemVisual(i,"symbolKeepAspect");this._createSymbol(o,e,i,l,h)}else{var c=this.childAt(0);c.silent=!1;var v={scaleX:l[0]/2,scaleY:l[1]/2};f?c.attr(v):Re(c,v,s,i),Dp(c)}if(this._updateCommon(e,i,l,n,a),u){var c=this.childAt(0);if(!f){var v={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:c.style.opacity}};c.scaleX=c.scaleY=0,c.style.opacity=0,cr(c,v,s,i)}}f&&this.childAt(0).stopAnimation("leave")},t.prototype._updateCommon=function(e,i,n,a,o){var s=this.childAt(0),l=e.hostModel,u,f,h,c,v,d,g,p,y;if(a&&(u=a.emphasisItemStyle,f=a.blurItemStyle,h=a.selectItemStyle,c=a.focus,v=a.blurScope,g=a.labelStatesModels,p=a.hoverScale,y=a.cursorStyle,d=a.emphasisDisabled),!a||e.hasItemOption){var m=a&&a.itemModel?a.itemModel:e.getItemModel(i),_=m.getModel("emphasis");u=_.getModel("itemStyle").getItemStyle(),h=m.getModel(["select","itemStyle"]).getItemStyle(),f=m.getModel(["blur","itemStyle"]).getItemStyle(),c=_.get("focus"),v=_.get("blurScope"),d=_.get("disabled"),g=qo(m),p=_.getShallow("scale"),y=m.getShallow("cursor")}var S=e.getItemVisual(i,"symbolRotate");s.attr("rotation",(S||0)*Math.PI/180||0);var b=Lg(e.getItemVisual(i,"symbolOffset"),n);b&&(s.x=b[0],s.y=b[1]),y&&s.attr("cursor",y);var w=e.getItemVisual(i,"style"),x=w.fill;if(s instanceof Sr){var C=s.style;s.useStyle(O({image:C.image,x:C.x,y:C.y,width:C.width,height:C.height},w))}else s.__isEmptyBrush?s.useStyle(O({},w)):s.useStyle(w),s.style.decal=null,s.setColor(x,o&&o.symbolInnerColor),s.style.strokeNoScale=!0;var T=e.getItemVisual(i,"liftZ"),D=this._z2;T!=null?D==null&&(this._z2=s.z2,s.z2+=T):D!=null&&(s.z2=D,this._z2=null);var A=o&&o.useNameLabel;Zo(s,g,{labelFetcher:l,labelDataIndex:i,defaultText:L,inheritColor:x,defaultOpacity:w.opacity});function L(R){return A?e.getName(R):wy(e,R)}this._sizeX=n[0]/2,this._sizeY=n[1]/2;var P=s.ensureState("emphasis");P.style=u,s.ensureState("select").style=h,s.ensureState("blur").style=f;var I=p==null||p===!0?Math.max(1.1,3/this._sizeY):isFinite(p)&&p>0?+p:1;P.scaleX=this._sizeX*I,P.scaleY=this._sizeY*I,this.setSymbolScale(1),So(this,c,v,d)},t.prototype.setSymbolScale=function(e){this.scaleX=this.scaleY=e},t.prototype.fadeOut=function(e,i,n){var a=this.childAt(0),o=st(this).dataIndex,s=n&&n.animation;if(this.silent=a.silent=!0,n&&n.fadeLabel){var l=a.getTextContent();l&&bo(l,{style:{opacity:0}},i,{dataIndex:o,removeOpt:s,cb:function(){a.removeTextContent()}})}else a.removeTextContent();bo(a,{style:{opacity:0},scaleX:0,scaleY:0},i,{dataIndex:o,cb:e,removeOpt:s})},t.getSymbolSize=function(e,i){return Xb(e.getItemVisual(i,"symbolSize"))},t}(Ot);function pC(r,t){this.parent.drift(r,t)}function wl(r,t,e,i){return t&&!isNaN(t[0])&&!isNaN(t[1])&&!(i.isIgnore&&i.isIgnore(e))&&!(i.clipShape&&!i.clipShape.contain(t[0],t[1]))&&r.getItemVisual(e,"symbol")!=="none"}function Ac(r){return r!=null&&!W(r)&&(r={isIgnore:r}),r||{}}function Lc(r){var t=r.hostModel,e=t.getModel("emphasis");return{emphasisItemStyle:e.getModel("itemStyle").getItemStyle(),blurItemStyle:t.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:t.getModel(["select","itemStyle"]).getItemStyle(),focus:e.get("focus"),blurScope:e.get("blurScope"),emphasisDisabled:e.get("disabled"),hoverScale:e.get("scale"),labelStatesModels:qo(t),cursorStyle:t.get("cursor")}}var gC=function(){function r(t){this.group=new Ot,this._SymbolCtor=t||Nf}return r.prototype.updateData=function(t,e){this._progressiveEls=null,e=Ac(e);var i=this.group,n=t.hostModel,a=this._data,o=this._SymbolCtor,s=e.disableAnimation,l=Lc(t),u={disableAnimation:s},f=e.getSymbolPoint||function(h){return t.getItemLayout(h)};a||i.removeAll(),t.diff(a).add(function(h){var c=f(h);if(wl(t,c,h,e)){var v=new o(t,h,l,u);v.setPosition(c),t.setItemGraphicEl(h,v),i.add(v)}}).update(function(h,c){var v=a.getItemGraphicEl(c),d=f(h);if(!wl(t,d,h,e)){i.remove(v);return}var g=t.getItemVisual(h,"symbol")||"circle",p=v&&v.getSymbolType&&v.getSymbolType();if(!v||p&&p!==g)i.remove(v),v=new o(t,h,l,u),v.setPosition(d);else{v.updateData(t,h,l,u);var y={x:d[0],y:d[1]};s?v.attr(y):Re(v,y,n)}i.add(v),t.setItemGraphicEl(h,v)}).remove(function(h){var c=a.getItemGraphicEl(h);c&&c.fadeOut(function(){i.remove(c)},n)}).execute(),this._getSymbolPoint=f,this._data=t},r.prototype.updateLayout=function(){var t=this,e=this._data;e&&e.eachItemGraphicEl(function(i,n){var a=t._getSymbolPoint(n);i.setPosition(a),i.markRedraw()})},r.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=Lc(t),this._data=null,this.group.removeAll()},r.prototype.incrementalUpdate=function(t,e,i){this._progressiveEls=[],i=Ac(i);function n(l){l.isGroup||(l.incremental=!0,l.ensureState("emphasis").hoverLayer=!0)}for(var a=t.start;a<t.end;a++){var o=e.getItemLayout(a);if(wl(e,o,a,i)){var s=new this._SymbolCtor(e,a,this._seriesScope);s.traverse(n),s.setPosition(o),this.group.add(s),e.setItemGraphicEl(a,s),this._progressiveEls.push(s)}}},r.prototype.eachRendered=function(t){cf(this._progressiveEls||this.group,t)},r.prototype.remove=function(t){var e=this.group,i=this._data;i&&t?i.eachItemGraphicEl(function(n){n.fadeOut(function(){e.remove(n)},i.hostModel)}):e.removeAll()},r}();function by(r,t,e){var i=r.getBaseAxis(),n=r.getOtherAxis(i),a=yC(n,e),o=i.dim,s=n.dim,l=t.mapDimension(s),u=t.mapDimension(o),f=s==="x"||s==="radius"?1:0,h=V(r.dimensions,function(d){return t.mapDimension(d)}),c=!1,v=t.getCalculationInfo("stackResultDimension");return qn(t,h[0])&&(c=!0,h[0]=v),qn(t,h[1])&&(c=!0,h[1]=v),{dataDimsForPoint:h,valueStart:a,valueAxisDim:s,baseAxisDim:o,stacked:!!c,valueDim:l,baseDim:u,baseDataOffset:f,stackedOverDimension:t.getCalculationInfo("stackedOverDimension")}}function yC(r,t){var e=0,i=r.scale.getExtent();return t==="start"?e=i[0]:t==="end"?e=i[1]:vt(t)&&!isNaN(t)?e=t:i[0]>0?e=i[0]:i[1]<0&&(e=i[1]),e}function xy(r,t,e,i){var n=NaN;r.stacked&&(n=e.get(e.getCalculationInfo("stackedOverDimension"),i)),isNaN(n)&&(n=r.valueStart);var a=r.baseDataOffset,o=[];return o[a]=e.get(r.baseDim,i),o[1-a]=n,t.dataToPoint(o)}function mC(r,t){var e=[];return t.diff(r).add(function(i){e.push({cmd:"+",idx:i})}).update(function(i,n){e.push({cmd:"=",idx:n,idx1:i})}).remove(function(i){e.push({cmd:"-",idx:i})}).execute(),e}function _C(r,t,e,i,n,a,o,s){for(var l=mC(r,t),u=[],f=[],h=[],c=[],v=[],d=[],g=[],p=by(n,t,o),y=r.getLayout("points")||[],m=t.getLayout("points")||[],_=0;_<l.length;_++){var S=l[_],b=!0,w=void 0,x=void 0;switch(S.cmd){case"=":w=S.idx*2,x=S.idx1*2;var C=y[w],T=y[w+1],D=m[x],A=m[x+1];(isNaN(C)||isNaN(T))&&(C=D,T=A),u.push(C,T),f.push(D,A),h.push(e[w],e[w+1]),c.push(i[x],i[x+1]),g.push(t.getRawIndex(S.idx1));break;case"+":var L=S.idx,P=p.dataDimsForPoint,I=n.dataToPoint([t.get(P[0],L),t.get(P[1],L)]);x=L*2,u.push(I[0],I[1]),f.push(m[x],m[x+1]);var R=xy(p,n,t,L);h.push(R[0],R[1]),c.push(i[x],i[x+1]),g.push(t.getRawIndex(L));break;case"-":b=!1}b&&(v.push(S),d.push(d.length))}d.sort(function(ut,Ft){return g[ut]-g[Ft]});for(var E=u.length,z=Di(E),B=Di(E),k=Di(E),H=Di(E),Y=[],_=0;_<d.length;_++){var U=d[_],tt=_*2,et=U*2;z[tt]=u[et],z[tt+1]=u[et+1],B[tt]=f[et],B[tt+1]=f[et+1],k[tt]=h[et],k[tt+1]=h[et+1],H[tt]=c[et],H[tt+1]=c[et+1],Y[_]=v[U]}return{current:z,next:B,stackedOnCurrent:k,stackedOnNext:H,status:Y}}var rr=Math.min,ir=Math.max;function qr(r,t){return isNaN(r)||isNaN(t)}function Du(r,t,e,i,n,a,o,s,l){for(var u,f,h,c,v,d,g=e,p=0;p<i;p++){var y=t[g*2],m=t[g*2+1];if(g>=n||g<0)break;if(qr(y,m)){if(l){g+=a;continue}break}if(g===e)r[a>0?"moveTo":"lineTo"](y,m),h=y,c=m;else{var _=y-u,S=m-f;if(_*_+S*S<.5){g+=a;continue}if(o>0){for(var b=g+a,w=t[b*2],x=t[b*2+1];w===y&&x===m&&p<i;)p++,b+=a,g+=a,w=t[b*2],x=t[b*2+1],y=t[g*2],m=t[g*2+1],_=y-u,S=m-f;var C=p+1;if(l)for(;qr(w,x)&&C<i;)C++,b+=a,w=t[b*2],x=t[b*2+1];var T=.5,D=0,A=0,L=void 0,P=void 0;if(C>=i||qr(w,x))v=y,d=m;else{D=w-u,A=x-f;var I=y-u,R=w-y,E=m-f,z=x-m,B=void 0,k=void 0;if(s==="x"){B=Math.abs(I),k=Math.abs(R);var H=D>0?1:-1;v=y-H*B*o,d=m,L=y+H*k*o,P=m}else if(s==="y"){B=Math.abs(E),k=Math.abs(z);var Y=A>0?1:-1;v=y,d=m-Y*B*o,L=y,P=m+Y*k*o}else B=Math.sqrt(I*I+E*E),k=Math.sqrt(R*R+z*z),T=k/(k+B),v=y-D*o*(1-T),d=m-A*o*(1-T),L=y+D*o*T,P=m+A*o*T,L=rr(L,ir(w,y)),P=rr(P,ir(x,m)),L=ir(L,rr(w,y)),P=ir(P,rr(x,m)),D=L-y,A=P-m,v=y-D*B/k,d=m-A*B/k,v=rr(v,ir(u,y)),d=rr(d,ir(f,m)),v=ir(v,rr(u,y)),d=ir(d,rr(f,m)),D=y-v,A=m-d,L=y+D*k/B,P=m+A*k/B}r.bezierCurveTo(h,c,v,d,y,m),h=L,c=P}else r.lineTo(y,m)}u=y,f=m,g+=a}return p}var Ty=function(){function r(){this.smooth=0,this.smoothConstraint=!0}return r}(),SC=function(r){N(t,r);function t(e){var i=r.call(this,e)||this;return i.type="ec-polyline",i}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Ty},t.prototype.buildPath=function(e,i){var n=i.points,a=0,o=n.length/2;if(i.connectNulls){for(;o>0&&qr(n[o*2-2],n[o*2-1]);o--);for(;a<o&&qr(n[a*2],n[a*2+1]);a++);}for(;a<o;)a+=Du(e,n,a,o,o,1,i.smooth,i.smoothMonotone,i.connectNulls)+1},t.prototype.getPointOn=function(e,i){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var n=this.path,a=n.data,o=jr.CMD,s,l,u=i==="x",f=[],h=0;h<a.length;){var c=a[h++],v=void 0,d=void 0,g=void 0,p=void 0,y=void 0,m=void 0,_=void 0;switch(c){case o.M:s=a[h++],l=a[h++];break;case o.L:if(v=a[h++],d=a[h++],_=u?(e-s)/(v-s):(e-l)/(d-l),_<=1&&_>=0){var S=u?(d-l)*_+l:(v-s)*_+s;return u?[e,S]:[S,e]}s=v,l=d;break;case o.C:v=a[h++],d=a[h++],g=a[h++],p=a[h++],y=a[h++],m=a[h++];var b=u?lo(s,v,g,y,e,f):lo(l,d,p,m,e,f);if(b>0)for(var w=0;w<b;w++){var x=f[w];if(x<=1&&x>=0){var S=u?At(l,d,p,m,x):At(s,v,g,y,x);return u?[e,S]:[S,e]}}s=y,l=m;break}}},t}(ft),wC=function(r){N(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t}(Ty),bC=function(r){N(t,r);function t(e){var i=r.call(this,e)||this;return i.type="ec-polygon",i}return t.prototype.getDefaultShape=function(){return new wC},t.prototype.buildPath=function(e,i){var n=i.points,a=i.stackedOnPoints,o=0,s=n.length/2,l=i.smoothMonotone;if(i.connectNulls){for(;s>0&&qr(n[s*2-2],n[s*2-1]);s--);for(;o<s&&qr(n[o*2],n[o*2+1]);o++);}for(;o<s;){var u=Du(e,n,o,s,s,1,i.smooth,l,i.connectNulls);Du(e,a,o+u-1,u,s,-1,i.stackedOnSmooth,l,i.connectNulls),o+=u+1,e.closePath()}},t}(ft);function xC(r,t,e,i,n){var a=r.getArea(),o=a.x,s=a.y,l=a.width,u=a.height,f=e.get(["lineStyle","width"])||0;o-=f/2,s-=f/2,l+=f,u+=f,l=Math.ceil(l),o!==Math.floor(o)&&(o=Math.floor(o),l++);var h=new xt({shape:{x:o,y:s,width:l,height:u}});if(t){var c=r.getBaseAxis(),v=c.isHorizontal(),d=c.inverse;v?(d&&(h.shape.x+=l),h.shape.width=0):(d||(h.shape.y+=u),h.shape.height=0);var g=q(n)?function(p){n(p,h)}:null;cr(h,{shape:{width:l,height:u,x:o,y:s}},e,null,i,g)}return h}function TC(r,t,e){var i=r.getArea(),n=wt(i.r0,1),a=wt(i.r,1),o=new Hi({shape:{cx:wt(r.cx,1),cy:wt(r.cy,1),r0:n,r:a,startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}});if(t){var s=r.getBaseAxis().dim==="angle";s?o.shape.endAngle=i.startAngle:o.shape.r=n,cr(o,{shape:{endAngle:i.endAngle,r:a}},e)}return o}function CC(r,t){return r.type===t}function Ic(r,t){if(r.length===t.length){for(var e=0;e<r.length;e++)if(r[e]!==t[e])return;return!0}}function Pc(r){for(var t=1/0,e=1/0,i=-1/0,n=-1/0,a=0;a<r.length;){var o=r[a++],s=r[a++];isNaN(o)||(t=Math.min(o,t),i=Math.max(o,i)),isNaN(s)||(e=Math.min(s,e),n=Math.max(s,n))}return[[t,e],[i,n]]}function Rc(r,t){var e=Pc(r),i=e[0],n=e[1],a=Pc(t),o=a[0],s=a[1];return Math.max(Math.abs(i[0]-o[0]),Math.abs(i[1]-o[1]),Math.abs(n[0]-s[0]),Math.abs(n[1]-s[1]))}function Ec(r){return vt(r)?r:r?.5:0}function DC(r,t,e){if(!e.valueDim)return[];for(var i=t.count(),n=Di(i*2),a=0;a<i;a++){var o=xy(e,r,t,a);n[a*2]=o[0],n[a*2+1]=o[1]}return n}function nr(r,t,e,i,n){var a=e.getBaseAxis(),o=a.dim==="x"||a.dim==="radius"?0:1,s=[],l=0,u=[],f=[],h=[],c=[];if(n){for(l=0;l<r.length;l+=2){var v=t||r;!isNaN(v[l])&&!isNaN(v[l+1])&&c.push(r[l],r[l+1])}r=c}for(l=0;l<r.length-2;l+=2)switch(h[0]=r[l+2],h[1]=r[l+3],f[0]=r[l],f[1]=r[l+1],s.push(f[0],f[1]),i){case"end":u[o]=h[o],u[1-o]=f[1-o],s.push(u[0],u[1]);break;case"middle":var d=(f[o]+h[o])/2,g=[];u[o]=g[o]=d,u[1-o]=f[1-o],g[1-o]=h[1-o],s.push(u[0],u[1]),s.push(g[0],g[1]);break;default:u[o]=f[o],u[1-o]=h[1-o],s.push(u[0],u[1])}return s.push(r[l++],r[l++]),s}function MC(r,t){var e=[],i=r.length,n,a;function o(f,h,c){var v=f.coord,d=(c-v)/(h.coord-v),g=c0(d,[f.color,h.color]);return{coord:c,color:g}}for(var s=0;s<i;s++){var l=r[s],u=l.coord;if(u<0)n=l;else if(u>t){a?e.push(o(a,l,t)):n&&e.push(o(n,l,0),o(n,l,t));break}else n&&(e.push(o(n,l,0)),n=null),e.push(l),a=l}return e}function AC(r,t,e){var i=r.getVisual("visualMeta");if(!(!i||!i.length||!r.count())&&t.type==="cartesian2d"){for(var n,a,o=i.length-1;o>=0;o--){var s=r.getDimensionInfo(i[o].dimension);if(n=s&&s.coordDim,n==="x"||n==="y"){a=i[o];break}}if(a){var l=t.getAxis(n),u=V(a.stops,function(_){return{coord:l.toGlobalCoord(l.dataToCoord(_.value)),color:_.color}}),f=u.length,h=a.outerColors.slice();f&&u[0].coord>u[f-1].coord&&(u.reverse(),h.reverse());var c=MC(u,n==="x"?e.getWidth():e.getHeight()),v=c.length;if(!v&&f)return u[0].coord<0?h[1]?h[1]:u[f-1].color:h[0]?h[0]:u[0].color;var d=10,g=c[0].coord-d,p=c[v-1].coord+d,y=p-g;if(y<.001)return"transparent";M(c,function(_){_.offset=(_.coord-g)/y}),c.push({offset:v?c[v-1].offset:.5,color:h[1]||"transparent"}),c.unshift({offset:v?c[0].offset:.5,color:h[0]||"transparent"});var m=new Tp(0,0,0,0,c,!0);return m[n]=g,m[n+"2"]=p,m}}}function LC(r,t,e){var i=r.get("showAllSymbol"),n=i==="auto";if(!(i&&!n)){var a=e.getAxesByScale("ordinal")[0];if(a&&!(n&&IC(a,t))){var o=t.mapDimension(a.dim),s={};return M(a.getViewLabels(),function(l){var u=a.scale.getRawOrdinalNumber(l.tickValue);s[u]=1}),function(l){return!s.hasOwnProperty(t.get(o,l))}}}}function IC(r,t){var e=r.getExtent(),i=Math.abs(e[1]-e[0])/r.scale.count();isNaN(i)&&(i=0);for(var n=t.count(),a=Math.max(1,Math.round(n/5)),o=0;o<n;o+=a)if(Nf.getSymbolSize(t,o)[r.isHorizontal()?1:0]*1.5>i)return!1;return!0}function PC(r,t){return isNaN(r)||isNaN(t)}function RC(r){for(var t=r.length/2;t>0&&PC(r[t*2-2],r[t*2-1]);t--);return t-1}function Oc(r,t){return[r[t*2],r[t*2+1]]}function EC(r,t,e){for(var i=r.length/2,n=e==="x"?0:1,a,o,s=0,l=-1,u=0;u<i;u++)if(o=r[u*2+n],!(isNaN(o)||isNaN(r[u*2+1-n]))){if(u===0){a=o;continue}if(a<=t&&o>=t||a>=t&&o<=t){l=u;break}s=u,a=o}return{range:[s,l],t:(t-a)/(o-a)}}function Cy(r){if(r.get(["endLabel","show"]))return!0;for(var t=0;t<fe.length;t++)if(r.get([fe[t],"endLabel","show"]))return!0;return!1}function bl(r,t,e,i){if(CC(t,"cartesian2d")){var n=i.getModel("endLabel"),a=n.get("valueAnimation"),o=i.getData(),s={lastFrameIndex:0},l=Cy(i)?function(v,d){r._endLabelOnDuring(v,d,o,s,a,n,t)}:null,u=t.getBaseAxis().isHorizontal(),f=xC(t,e,i,function(){var v=r._endLabel;v&&e&&s.originalX!=null&&v.attr({x:s.originalX,y:s.originalY})},l);if(!i.get("clip",!0)){var h=f.shape,c=Math.max(h.width,h.height);u?(h.y-=c,h.height+=c*2):(h.x-=c,h.width+=c*2)}return l&&l(1,f),f}else return TC(t,e,i)}function OC(r,t){var e=t.getBaseAxis(),i=e.isHorizontal(),n=e.inverse,a=i?n?"right":"left":"center",o=i?"middle":n?"top":"bottom";return{normal:{align:r.get("align")||a,verticalAlign:r.get("verticalAlign")||o}}}var kC=function(r){N(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.init=function(){var e=new Ot,i=new gC;this.group.add(i.group),this._symbolDraw=i,this._lineGroup=e,this._changePolyState=ht(this._changePolyState,this)},t.prototype.render=function(e,i,n){var a=e.coordinateSystem,o=this.group,s=e.getData(),l=e.getModel("lineStyle"),u=e.getModel("areaStyle"),f=s.getLayout("points")||[],h=a.type==="polar",c=this._coordSys,v=this._symbolDraw,d=this._polyline,g=this._polygon,p=this._lineGroup,y=!i.ssr&&e.get("animation"),m=!u.isEmpty(),_=u.get("origin"),S=by(a,s,_),b=m&&DC(a,s,S),w=e.get("showSymbol"),x=e.get("connectNulls"),C=w&&!h&&LC(e,s,a),T=this._data;T&&T.eachItemGraphicEl(function(ut,Ft){ut.__temp&&(o.remove(ut),T.setItemGraphicEl(Ft,null))}),w||v.remove(),o.add(p);var D=h?!1:e.get("step"),A;a&&a.getArea&&e.get("clip",!0)&&(A=a.getArea(),A.width!=null?(A.x-=.1,A.y-=.1,A.width+=.2,A.height+=.2):A.r0&&(A.r0-=.5,A.r+=.5)),this._clipShapeForSymbol=A;var L=AC(s,a,n)||s.getVisual("style")[s.getVisual("drawType")];if(!(d&&c.type===a.type&&D===this._step))w&&v.updateData(s,{isIgnore:C,clipShape:A,disableAnimation:!0,getSymbolPoint:function(ut){return[f[ut*2],f[ut*2+1]]}}),y&&this._initSymbolLabelAnimation(s,a,A),D&&(b&&(b=nr(b,f,a,D,x)),f=nr(f,null,a,D,x)),d=this._newPolyline(f),m?g=this._newPolygon(f,b):g&&(p.remove(g),g=this._polygon=null),h||this._initOrUpdateEndLabel(e,a,ti(L)),p.setClipPath(bl(this,a,!0,e));else{m&&!g?g=this._newPolygon(f,b):g&&!m&&(p.remove(g),g=this._polygon=null),h||this._initOrUpdateEndLabel(e,a,ti(L));var P=p.getClipPath();if(P){var I=bl(this,a,!1,e);cr(P,{shape:I.shape},e)}else p.setClipPath(bl(this,a,!0,e));w&&v.updateData(s,{isIgnore:C,clipShape:A,disableAnimation:!0,getSymbolPoint:function(ut){return[f[ut*2],f[ut*2+1]]}}),(!Ic(this._stackedOnPoints,b)||!Ic(this._points,f))&&(y?this._doUpdateAnimation(s,b,a,n,D,_,x):(D&&(b&&(b=nr(b,f,a,D,x)),f=nr(f,null,a,D,x)),d.setShape({points:f}),g&&g.setShape({points:f,stackedOnPoints:b})))}var R=e.getModel("emphasis"),E=R.get("focus"),z=R.get("blurScope"),B=R.get("disabled");if(d.useStyle(at(l.getLineStyle(),{fill:"none",stroke:L,lineJoin:"bevel"})),nu(d,e,"lineStyle"),d.style.lineWidth>0&&e.get(["emphasis","lineStyle","width"])==="bolder"){var k=d.getState("emphasis").style;k.lineWidth=+d.style.lineWidth+1}st(d).seriesIndex=e.seriesIndex,So(d,E,z,B);var H=Ec(e.get("smooth")),Y=e.get("smoothMonotone");if(d.setShape({smooth:H,smoothMonotone:Y,connectNulls:x}),g){var U=s.getCalculationInfo("stackedOnSeries"),tt=0;g.useStyle(at(u.getAreaStyle(),{fill:L,opacity:.7,lineJoin:"bevel",decal:s.getVisual("style").decal})),U&&(tt=Ec(U.get("smooth"))),g.setShape({smooth:H,stackedOnSmooth:tt,smoothMonotone:Y,connectNulls:x}),nu(g,e,"areaStyle"),st(g).seriesIndex=e.seriesIndex,So(g,E,z,B)}var et=this._changePolyState;s.eachItemGraphicEl(function(ut){ut&&(ut.onHoverStateChange=et)}),this._polyline.onHoverStateChange=et,this._data=s,this._coordSys=a,this._stackedOnPoints=b,this._points=f,this._step=D,this._valueOrigin=_,e.get("triggerLineEvent")&&(this.packEventData(e,d),g&&this.packEventData(e,g))},t.prototype.packEventData=function(e,i){st(i).eventData={componentType:"series",componentSubType:"line",componentIndex:e.componentIndex,seriesIndex:e.seriesIndex,seriesName:e.name,seriesType:"line"}},t.prototype.highlight=function(e,i,n,a){var o=e.getData(),s=Jr(o,a);if(this._changePolyState("emphasis"),!(s instanceof Array)&&s!=null&&s>=0){var l=o.getLayout("points"),u=o.getItemGraphicEl(s);if(!u){var f=l[s*2],h=l[s*2+1];if(isNaN(f)||isNaN(h)||this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(f,h))return;var c=e.get("zlevel")||0,v=e.get("z")||0;u=new Nf(o,s),u.x=f,u.y=h,u.setZ(c,v);var d=u.getSymbolPath().getTextContent();d&&(d.zlevel=c,d.z=v,d.z2=this._polyline.z2+1),u.__temp=!0,o.setItemGraphicEl(s,u),u.stopSymbolAnimation(!0),this.group.add(u)}u.highlight()}else me.prototype.highlight.call(this,e,i,n,a)},t.prototype.downplay=function(e,i,n,a){var o=e.getData(),s=Jr(o,a);if(this._changePolyState("normal"),s!=null&&s>=0){var l=o.getItemGraphicEl(s);l&&(l.__temp?(o.setItemGraphicEl(s,null),this.group.remove(l)):l.downplay())}else me.prototype.downplay.call(this,e,i,n,a)},t.prototype._changePolyState=function(e){var i=this._polygon;Vh(this._polyline,e),i&&Vh(i,e)},t.prototype._newPolyline=function(e){var i=this._polyline;return i&&this._lineGroup.remove(i),i=new SC({shape:{points:e},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(i),this._polyline=i,i},t.prototype._newPolygon=function(e,i){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new bC({shape:{points:e,stackedOnPoints:i},segmentIgnoreThreshold:2}),this._lineGroup.add(n),this._polygon=n,n},t.prototype._initSymbolLabelAnimation=function(e,i,n){var a,o,s=i.getBaseAxis(),l=s.inverse;i.type==="cartesian2d"?(a=s.isHorizontal(),o=!1):i.type==="polar"&&(a=s.dim==="angle",o=!0);var u=e.hostModel,f=u.get("animationDuration");q(f)&&(f=f(null));var h=u.get("animationDelay")||0,c=q(h)?h(null):h;e.eachItemGraphicEl(function(v,d){var g=v;if(g){var p=[v.x,v.y],y=void 0,m=void 0,_=void 0;if(n)if(o){var S=n,b=i.pointToCoord(p);a?(y=S.startAngle,m=S.endAngle,_=-b[1]/180*Math.PI):(y=S.r0,m=S.r,_=b[0])}else{var w=n;a?(y=w.x,m=w.x+w.width,_=v.x):(y=w.y+w.height,m=w.y,_=v.y)}var x=m===y?0:(_-y)/(m-y);l&&(x=1-x);var C=q(h)?h(d):f*x+c,T=g.getSymbolPath(),D=T.getTextContent();g.attr({scaleX:0,scaleY:0}),g.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:C}),D&&D.animateFrom({style:{opacity:0}},{duration:300,delay:C}),T.disableLabelAnimation=!0}})},t.prototype._initOrUpdateEndLabel=function(e,i,n){var a=e.getModel("endLabel");if(Cy(e)){var o=e.getData(),s=this._polyline,l=o.getLayout("points");if(!l){s.removeTextContent(),this._endLabel=null;return}var u=this._endLabel;u||(u=this._endLabel=new Lt({z2:200}),u.ignoreClip=!0,s.setTextContent(this._endLabel),s.disableLabelAnimation=!0);var f=RC(l);f>=0&&(Zo(s,qo(e,"endLabel"),{inheritColor:n,labelFetcher:e,labelDataIndex:f,defaultText:function(h,c,v){return v!=null?dC(o,v):wy(o,h)},enableTextSetter:!0},OC(a,i)),s.textConfig.position=null)}else this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},t.prototype._endLabelOnDuring=function(e,i,n,a,o,s,l){var u=this._endLabel,f=this._polyline;if(u){e<1&&a.originalX==null&&(a.originalX=u.x,a.originalY=u.y);var h=n.getLayout("points"),c=n.hostModel,v=c.get("connectNulls"),d=s.get("precision"),g=s.get("distance")||0,p=l.getBaseAxis(),y=p.isHorizontal(),m=p.inverse,_=i.shape,S=m?y?_.x:_.y+_.height:y?_.x+_.width:_.y,b=(y?g:0)*(m?-1:1),w=(y?0:-g)*(m?-1:1),x=y?"x":"y",C=EC(h,S,x),T=C.range,D=T[1]-T[0],A=void 0;if(D>=1){if(D>1&&!v){var L=Oc(h,T[0]);u.attr({x:L[0]+b,y:L[1]+w}),o&&(A=c.getRawValue(T[0]))}else{var L=f.getPointOn(S,x);L&&u.attr({x:L[0]+b,y:L[1]+w});var P=c.getRawValue(T[0]),I=c.getRawValue(T[1]);o&&(A=u_(n,d,P,I,C.t))}a.lastFrameIndex=T[0]}else{var R=e===1||a.lastFrameIndex>0?T[0]:0,L=Oc(h,R);o&&(A=c.getRawValue(R)),u.attr({x:L[0]+b,y:L[1]+w})}if(o){var E=Rp(u);typeof E.setLabelText=="function"&&E.setLabelText(A)}}},t.prototype._doUpdateAnimation=function(e,i,n,a,o,s,l){var u=this._polyline,f=this._polygon,h=e.hostModel,c=_C(this._data,e,this._stackedOnPoints,i,this._coordSys,n,this._valueOrigin),v=c.current,d=c.stackedOnCurrent,g=c.next,p=c.stackedOnNext;if(o&&(d=nr(c.stackedOnCurrent,c.current,n,o,l),v=nr(c.current,null,n,o,l),p=nr(c.stackedOnNext,c.next,n,o,l),g=nr(c.next,null,n,o,l)),Rc(v,g)>3e3||f&&Rc(d,p)>3e3){u.stopAnimation(),u.setShape({points:g}),f&&(f.stopAnimation(),f.setShape({points:g,stackedOnPoints:p}));return}u.shape.__points=c.current,u.shape.points=v;var y={shape:{points:g}};c.current!==v&&(y.shape.__points=c.next),u.stopAnimation(),Re(u,y,h),f&&(f.setShape({points:v,stackedOnPoints:d}),f.stopAnimation(),Re(f,{shape:{stackedOnPoints:p}},h),u.shape.points!==f.shape.points&&(f.shape.points=u.shape.points));for(var m=[],_=c.status,S=0;S<_.length;S++){var b=_[S].cmd;if(b==="="){var w=e.getItemGraphicEl(_[S].idx1);w&&m.push({el:w,ptIdx:S})}}u.animators&&u.animators.length&&u.animators[0].during(function(){f&&f.dirtyShape();for(var x=u.shape.__points,C=0;C<m.length;C++){var T=m[C].el,D=m[C].ptIdx*2;T.x=x[D],T.y=x[D+1],T.markRedraw()}})},t.prototype.remove=function(e){var i=this.group,n=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),n&&n.eachItemGraphicEl(function(a,o){a.__temp&&(i.remove(a),n.setItemGraphicEl(o,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},t.type="line",t}(me);function BC(r,t){return{seriesType:r,plan:_g(),reset:function(e){var i=e.getData(),n=e.coordinateSystem;if(e.pipelineContext,!!n){var a=V(n.dimensions,function(h){return i.mapDimension(h)}).slice(0,2),o=a.length,s=i.getCalculationInfo("stackResultDimension");qn(i,a[0])&&(a[0]=s),qn(i,a[1])&&(a[1]=s);var l=i.getStore(),u=i.getDimensionIndex(a[0]),f=i.getDimensionIndex(a[1]);return o&&{progress:function(h,c){for(var v=h.end-h.start,d=Di(v*o),g=[],p=[],y=h.start,m=0;y<h.end;y++){var _=void 0;if(o===1){var S=l.get(u,y);_=n.dataToPoint(S,null,p)}else g[0]=l.get(u,y),g[1]=l.get(f,y),_=n.dataToPoint(g,null,p);d[m++]=_[0],d[m++]=_[1]}c.setLayout("points",d)}}}}}}var NC={average:function(r){for(var t=0,e=0,i=0;i<r.length;i++)isNaN(r[i])||(t+=r[i],e++);return e===0?NaN:t/e},sum:function(r){for(var t=0,e=0;e<r.length;e++)t+=r[e]||0;return t},max:function(r){for(var t=-1/0,e=0;e<r.length;e++)r[e]>t&&(t=r[e]);return isFinite(t)?t:NaN},min:function(r){for(var t=1/0,e=0;e<r.length;e++)r[e]<t&&(t=r[e]);return isFinite(t)?t:NaN},nearest:function(r){return r[0]}},FC=function(r){return Math.round(r.length/2)};function zC(r){return{seriesType:r,reset:function(t,e,i){var n=t.getData(),a=t.get("sampling"),o=t.coordinateSystem,s=n.count();if(s>10&&o.type==="cartesian2d"&&a){var l=o.getBaseAxis(),u=o.getOtherAxis(l),f=l.getExtent(),h=i.getDevicePixelRatio(),c=Math.abs(f[1]-f[0])*(h||1),v=Math.round(s/c);if(isFinite(v)&&v>1){a==="lttb"?t.setData(n.lttbDownSample(n.mapDimension(u.dim),1/v)):a==="minmax"&&t.setData(n.minmaxDownSample(n.mapDimension(u.dim),1/v));var d=void 0;G(a)?d=NC[a]:q(a)&&(d=a),d&&t.setData(n.downSample(n.mapDimension(u.dim),1/v,d,FC))}}}}}function XM(r){r.registerChartView(kC),r.registerSeriesModel(cC),r.registerLayout(BC("line")),r.registerVisual({seriesType:"line",reset:function(t){var e=t.getData(),i=t.getModel("lineStyle").getLineStyle();i&&!i.stroke&&(i.stroke=e.getVisual("style").fill),e.setVisual("legendLineStyle",i)}}),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,zC("line"))}function Ha(r,t,e){var i=r.get("borderRadius");if(i==null)return e?{cornerRadius:0}:null;F(i)||(i=[i,i,i,i]);var n=Math.abs(t.r||0-t.r0||0);return{cornerRadius:V(i,function(a){return yr(a,n)})}}var kc=Math.PI*2,Ga=Math.PI/180;function Dy(r,t){return Oi(r.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()})}function My(r,t){var e=Dy(r,t),i=r.get("center"),n=r.get("radius");F(n)||(n=[0,n]);var a=ct(e.width,t.getWidth()),o=ct(e.height,t.getHeight()),s=Math.min(a,o),l=ct(n[0],s/2),u=ct(n[1],s/2),f,h,c=r.coordinateSystem;if(c){var v=c.dataToPoint(i);f=v[0]||0,h=v[1]||0}else F(i)||(i=[i,i]),f=ct(i[0],a)+e.x,h=ct(i[1],o)+e.y;return{cx:f,cy:h,r0:l,r:u}}function HC(r,t,e){t.eachSeriesByType(r,function(i){var n=i.getData(),a=n.mapDimension("value"),o=Dy(i,e),s=My(i,e),l=s.cx,u=s.cy,f=s.r,h=s.r0,c=-i.get("startAngle")*Ga,v=i.get("endAngle"),d=i.get("padAngle")*Ga;v=v==="auto"?c-kc:-v*Ga;var g=i.get("minAngle")*Ga,p=g+d,y=0;n.each(a,function(z){!isNaN(z)&&y++});var m=n.getSum(a),_=Math.PI/(m||y)*2,S=i.get("clockwise"),b=i.get("roseType"),w=i.get("stillShowZeroSum"),x=n.getDataExtent(a);x[0]=0;var C=S?1:-1,T=[c,v],D=C*d/2;ip(T,!S),c=T[0],v=T[1];var A=Ay(i);A.startAngle=c,A.endAngle=v,A.clockwise=S;var L=Math.abs(v-c),P=L,I=0,R=c;if(n.setLayout({viewRect:o,r:f}),n.each(a,function(z,B){var k;if(isNaN(z)){n.setItemLayout(B,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:S,cx:l,cy:u,r0:h,r:b?NaN:f});return}b!=="area"?k=m===0&&w?_:z*_:k=L/y,k<p?(k=p,P-=p):I+=z;var H=R+C*k,Y=0,U=0;d>k?(Y=R+C*k/2,U=Y):(Y=R+D,U=H-D),n.setItemLayout(B,{angle:k,startAngle:Y,endAngle:U,clockwise:S,cx:l,cy:u,r0:h,r:b?Kl(z,x,[h,f]):f}),R=H}),P<kc&&y)if(P<=.001){var E=L/y;n.each(a,function(z,B){if(!isNaN(z)){var k=n.getItemLayout(B);k.angle=E;var H=0,Y=0;E<d?(H=c+C*(B+1/2)*E,Y=H):(H=c+C*B*E+D,Y=c+C*(B+1)*E-D),k.startAngle=H,k.endAngle=Y}})}else _=P/I,R=c,n.each(a,function(z,B){if(!isNaN(z)){var k=n.getItemLayout(B),H=k.angle===p?p:z*_,Y=0,U=0;H<d?(Y=R+C*H/2,U=Y):(Y=R+D,U=R+C*H-D),k.startAngle=Y,k.endAngle=U,R+=C*H}})})}var Ay=_t();function GC(r){return{seriesType:r,reset:function(t,e){var i=e.findComponents({mainType:"legend"});if(!(!i||!i.length)){var n=t.getData();n.filterSelf(function(a){for(var o=n.getName(a),s=0;s<i.length;s++)if(!i[s].isSelected(o))return!1;return!0})}}}}var VC=Math.PI/180;function Bc(r,t,e,i,n,a,o,s,l,u){if(r.length<2)return;function f(g){for(var p=g.rB,y=p*p,m=0;m<g.list.length;m++){var _=g.list[m],S=Math.abs(_.label.y-e),b=i+_.len,w=b*b,x=Math.sqrt(Math.abs((1-S*S/y)*w)),C=t+(x+_.len2)*n,T=C-_.label.x,D=_.targetTextWidth-T*n;Ly(_,D,!0),_.label.x=C}}function h(g){for(var p={list:[],maxY:0},y={list:[],maxY:0},m=0;m<g.length;m++)if(g[m].labelAlignTo==="none"){var _=g[m],S=_.label.y>e?y:p,b=Math.abs(_.label.y-e);if(b>=S.maxY){var w=_.label.x-t-_.len2*n,x=i+_.len,C=Math.abs(w)<x?Math.sqrt(b*b/(1-w*w/x/x)):x;S.rB=C,S.maxY=b}S.list.push(_)}f(p),f(y)}for(var c=r.length,v=0;v<c;v++)if(r[v].position==="outer"&&r[v].labelAlignTo==="labelLine"){var d=r[v].label.x-u;r[v].linePoints[1][0]+=d,r[v].label.x=u}sC(r,l,l+o)&&h(r)}function WC(r,t,e,i,n,a,o,s){for(var l=[],u=[],f=Number.MAX_VALUE,h=-Number.MAX_VALUE,c=0;c<r.length;c++){var v=r[c].label;xl(r[c])||(v.x<t?(f=Math.min(f,v.x),l.push(r[c])):(h=Math.max(h,v.x),u.push(r[c])))}for(var c=0;c<r.length;c++){var d=r[c];if(!xl(d)&&d.linePoints){if(d.labelStyleWidth!=null)continue;var v=d.label,g=d.linePoints,p=void 0;d.labelAlignTo==="edge"?v.x<t?p=g[2][0]-d.labelDistance-o-d.edgeDistance:p=o+n-d.edgeDistance-g[2][0]-d.labelDistance:d.labelAlignTo==="labelLine"?v.x<t?p=f-o-d.bleedMargin:p=o+n-h-d.bleedMargin:v.x<t?p=v.x-o-d.bleedMargin:p=o+n-v.x-d.bleedMargin,d.targetTextWidth=p,Ly(d,p)}}Bc(u,t,e,i,1,n,a,o,s,h),Bc(l,t,e,i,-1,n,a,o,s,f);for(var c=0;c<r.length;c++){var d=r[c];if(!xl(d)&&d.linePoints){var v=d.label,g=d.linePoints,y=d.labelAlignTo==="edge",m=v.style.padding,_=m?m[1]+m[3]:0,S=v.style.backgroundColor?0:_,b=d.rect.width+S,w=g[1][0]-g[2][0];y?v.x<t?g[2][0]=o+d.edgeDistance+b+d.labelDistance:g[2][0]=o+n-d.edgeDistance-b-d.labelDistance:(v.x<t?g[2][0]=v.x+d.labelDistance:g[2][0]=v.x-d.labelDistance,g[1][0]=g[2][0]+w),g[1][1]=g[2][1]=v.y}}}function Ly(r,t,e){if(e===void 0&&(e=!1),r.labelStyleWidth==null){var i=r.label,n=i.style,a=r.rect,o=n.backgroundColor,s=n.padding,l=s?s[1]+s[3]:0,u=n.overflow,f=a.width+(o?0:l);if(t<f||e){var h=a.height;if(u&&u.match("break")){i.setStyle("backgroundColor",null),i.setStyle("width",t-l);var c=i.getBoundingRect();i.setStyle("width",Math.ceil(c.width)),i.setStyle("backgroundColor",o)}else{var v=t-l,d=t<f?v:e?v>r.unconstrainedWidth?null:v:null;i.setStyle("width",d)}var g=i.getBoundingRect();a.width=g.width;var p=(i.style.margin||0)+2.1;a.height=g.height+p,a.y-=(a.height-h)/2}}}function xl(r){return r.position==="center"}function UC(r){var t=r.getData(),e=[],i,n,a=!1,o=(r.get("minShowLabelAngle")||0)*VC,s=t.getLayout("viewRect"),l=t.getLayout("r"),u=s.width,f=s.x,h=s.y,c=s.height;function v(w){w.ignore=!0}function d(w){if(!w.ignore)return!0;for(var x in w.states)if(w.states[x].ignore===!1)return!0;return!1}t.each(function(w){var x=t.getItemGraphicEl(w),C=x.shape,T=x.getTextContent(),D=x.getTextGuideLine(),A=t.getItemModel(w),L=A.getModel("label"),P=L.get("position")||A.get(["emphasis","label","position"]),I=L.get("distanceToLabelLine"),R=L.get("alignTo"),E=ct(L.get("edgeDistance"),u),z=L.get("bleedMargin"),B=A.getModel("labelLine"),k=B.get("length");k=ct(k,u);var H=B.get("length2");if(H=ct(H,u),Math.abs(C.endAngle-C.startAngle)<o){M(T.states,v),T.ignore=!0,D&&(M(D.states,v),D.ignore=!0);return}if(d(T)){var Y=(C.startAngle+C.endAngle)/2,U=Math.cos(Y),tt=Math.sin(Y),et,ut,Ft,Jt;i=C.cx,n=C.cy;var qt=P==="inside"||P==="inner";if(P==="center")et=C.cx,ut=C.cy,Jt="center";else{var Tt=(qt?(C.r+C.r0)/2*U:C.r*U)+i,yt=(qt?(C.r+C.r0)/2*tt:C.r*tt)+n;if(et=Tt+U*3,ut=yt+tt*3,!qt){var X=Tt+U*(k+l-C.r),J=yt+tt*(k+l-C.r),be=X+(U<0?-1:1)*H,bt=J;R==="edge"?et=U<0?f+E:f+u-E:et=be+(U<0?-I:I),ut=bt,Ft=[[Tt,yt],[X,J],[be,bt]]}Jt=qt?"center":R==="edge"?U>0?"right":"left":U>0?"left":"right"}var qe=Math.PI,Ne=0,Yi=L.get("rotate");if(vt(Yi))Ne=Yi*(qe/180);else if(P==="center")Ne=0;else if(Yi==="radial"||Yi===!0){var Ky=U<0?-Y+qe:-Y;Ne=Ky}else if(Yi==="tangential"&&P!=="outside"&&P!=="outer"){var ii=Math.atan2(U,tt);ii<0&&(ii=qe*2+ii);var Qy=tt>0;Qy&&(ii=qe+ii),Ne=ii-qe}if(a=!!Ne,T.x=et,T.y=ut,T.rotation=Ne,T.setStyle({verticalAlign:"middle"}),qt){T.setStyle({align:Jt});var fs=T.states.select;fs&&(fs.x+=T.x,fs.y+=T.y)}else{var Xi=T.getBoundingRect().clone();Xi.applyTransform(T.getComputedTransform());var Gf=(T.style.margin||0)+2.1;Xi.y-=Gf/2,Xi.height+=Gf,e.push({label:T,labelLine:D,position:P,len:k,len2:H,minTurnAngle:B.get("minTurnAngle"),maxSurfaceAngle:B.get("maxSurfaceAngle"),surfaceNormal:new Z(U,tt),linePoints:Ft,textAlign:Jt,labelDistance:I,labelAlignTo:R,edgeDistance:E,bleedMargin:z,rect:Xi,unconstrainedWidth:Xi.width,labelStyleWidth:T.style.width})}x.setTextConfig({inside:qt})}}),!a&&r.get("avoidLabelOverlap")&&WC(e,i,n,l,u,c,f,h);for(var g=0;g<e.length;g++){var p=e[g],y=p.label,m=p.labelLine,_=isNaN(y.x)||isNaN(y.y);if(y){y.setStyle({align:p.textAlign}),_&&(M(y.states,v),y.ignore=!0);var S=y.states.select;S&&(S.x+=y.x,S.y+=y.y)}if(m){var b=p.linePoints;_||!b?(M(m.states,v),m.ignore=!0):(tC(b,p.minTurnAngle),eC(b,p.surfaceNormal,p.maxSurfaceAngle),m.setShape({points:b}),y.__hostTarget.textGuideLineConfig={anchor:new Z(b[0][0],b[0][1])})}}}var YC=function(r){N(t,r);function t(e,i,n){var a=r.call(this)||this;a.z2=2;var o=new Lt;return a.setTextContent(o),a.updateData(e,i,n,!0),a}return t.prototype.updateData=function(e,i,n,a){var o=this,s=e.hostModel,l=e.getItemModel(i),u=l.getModel("emphasis"),f=e.getItemLayout(i),h=O(Ha(l.getModel("itemStyle"),f,!0),f);if(isNaN(h.startAngle)){o.setShape(h);return}if(a){o.setShape(h);var c=s.getShallow("animationType");s.ecModel.ssr?(cr(o,{scaleX:0,scaleY:0},s,{dataIndex:i,isFrom:!0}),o.originX=h.cx,o.originY=h.cy):c==="scale"?(o.shape.r=f.r0,cr(o,{shape:{r:f.r}},s,i)):n!=null?(o.setShape({startAngle:n,endAngle:n}),cr(o,{shape:{startAngle:f.startAngle,endAngle:f.endAngle}},s,i)):(o.shape.endAngle=f.startAngle,Re(o,{shape:{endAngle:f.endAngle}},s,i))}else Dp(o),Re(o,{shape:h},s,i);o.useStyle(e.getItemVisual(i,"style")),nu(o,l);var v=(f.startAngle+f.endAngle)/2,d=s.get("selectedOffset"),g=Math.cos(v)*d,p=Math.sin(v)*d,y=l.getShallow("cursor");y&&o.attr("cursor",y),this._updateLabel(s,e,i),o.ensureState("emphasis").shape=O({r:f.r+(u.get("scale")&&u.get("scaleSize")||0)},Ha(u.getModel("itemStyle"),f)),O(o.ensureState("select"),{x:g,y:p,shape:Ha(l.getModel(["select","itemStyle"]),f)}),O(o.ensureState("blur"),{shape:Ha(l.getModel(["blur","itemStyle"]),f)});var m=o.getTextGuideLine(),_=o.getTextContent();m&&O(m.ensureState("select"),{x:g,y:p}),O(_.ensureState("select"),{x:g,y:p}),So(this,u.get("focus"),u.get("blurScope"),u.get("disabled"))},t.prototype._updateLabel=function(e,i,n){var a=this,o=i.getItemModel(n),s=o.getModel("labelLine"),l=i.getItemVisual(n,"style"),u=l&&l.fill,f=l&&l.opacity;Zo(a,qo(o),{labelFetcher:i.hostModel,labelDataIndex:n,inheritColor:u,defaultOpacity:f,defaultText:e.getFormattedLabel(n,"normal")||i.getName(n)});var h=a.getTextContent();a.setTextConfig({position:null,rotation:null}),h.attr({z2:10});var c=e.get(["label","position"]);if(c!=="outside"&&c!=="outer")a.removeTextGuideLine();else{var v=this.getTextGuideLine();v||(v=new ia,this.setTextGuideLine(v)),iC(this,nC(o),{stroke:u,opacity:bn(s.get(["lineStyle","opacity"]),f,1)})}},t}(Hi),XC=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.ignoreLabelLineUpdate=!0,e}return t.prototype.render=function(e,i,n,a){var o=e.getData(),s=this._data,l=this.group,u;if(!s&&o.count()>0){for(var f=o.getItemLayout(0),h=1;isNaN(f&&f.startAngle)&&h<o.count();++h)f=o.getItemLayout(h);f&&(u=f.startAngle)}if(this._emptyCircleSector&&l.remove(this._emptyCircleSector),o.count()===0&&e.get("showEmptyCircle")){var c=Ay(e),v=new Hi({shape:O(My(e,n),c)});v.useStyle(e.getModel("emptyCircleStyle").getItemStyle()),this._emptyCircleSector=v,l.add(v)}o.diff(s).add(function(d){var g=new YC(o,d,u);o.setItemGraphicEl(d,g),l.add(g)}).update(function(d,g){var p=s.getItemGraphicEl(g);p.updateData(o,d,u),p.off("click"),l.add(p),o.setItemGraphicEl(d,p)}).remove(function(d){var g=s.getItemGraphicEl(d);Cp(g,e,d)}).execute(),UC(e),e.get("animationTypeUpdate")!=="expansion"&&(this._data=o)},t.prototype.dispose=function(){},t.prototype.containPoint=function(e,i){var n=i.getData(),a=n.getItemLayout(0);if(a){var o=e[0]-a.cx,s=e[1]-a.cy,l=Math.sqrt(o*o+s*s);return l<=a.r&&l>=a.r0}},t.type="pie",t}(me);function $C(r,t,e){t=F(t)&&{coordDimensions:t}||O({encodeDefine:r.getEncode()},t);var i=r.getSource(),n=ay(i,t).dimensions,a=new ny(n,r);return a.initData(i,e),a}var ZC=function(){function r(t,e){this._getDataWithEncodedVisual=t,this._getRawData=e}return r.prototype.getAllNames=function(){var t=this._getRawData();return t.mapArray(t.getName)},r.prototype.containName=function(t){var e=this._getRawData();return e.indexOfName(t)>=0},r.prototype.indexOfName=function(t){var e=this._getDataWithEncodedVisual();return e.indexOfName(t)},r.prototype.getItemVisual=function(t,e){var i=this._getDataWithEncodedVisual();return i.getItemVisual(t,e)},r}(),qC=_t(),KC=function(r){N(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.init=function(e){r.prototype.init.apply(this,arguments),this.legendVisualProvider=new ZC(ht(this.getData,this),ht(this.getRawData,this)),this._defaultLabelLine(e)},t.prototype.mergeOption=function(){r.prototype.mergeOption.apply(this,arguments)},t.prototype.getInitialData=function(){return $C(this,{coordDimensions:["value"],encodeDefaulter:St(KS,this)})},t.prototype.getDataParams=function(e){var i=this.getData(),n=qC(i),a=n.seats;if(!a){var o=[];i.each(i.mapDimension("value"),function(l){o.push(l)}),a=n.seats=U0(o,i.hostModel.get("percentPrecision"))}var s=r.prototype.getDataParams.call(this,e);return s.percent=a[e]||0,s.$vars.push("percent"),s},t.prototype._defaultLabelLine=function(e){Ql(e,"labelLine",["show"]);var i=e.labelLine,n=e.emphasis.labelLine;i.show=i.show&&e.label.show,n.show=n.show&&e.emphasis.label.show},t.type="series.pie",t.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,endAngle:"auto",padAngle:0,minAngle:0,minShowLabelAngle:0,selectedOffset:10,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:0,show:!0,overflow:"truncate",position:"outer",alignTo:"none",edgeDistance:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,minTurnAngle:90,maxSurfaceAngle:90,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1,borderJoin:"round"},showEmptyCircle:!0,emptyCircleStyle:{color:"lightgray",opacity:1},labelLayout:{hideOverlap:!0},emphasis:{scale:!0,scaleSize:5},avoidLabelOverlap:!0,animationType:"expansion",animationDuration:1e3,animationTypeUpdate:"transition",animationEasingUpdate:"cubicInOut",animationDurationUpdate:500,animationEasing:"cubicInOut"},t}($e);function QC(r){return{seriesType:r,reset:function(t,e){var i=t.getData();i.filterSelf(function(n){var a=i.mapDimension("value"),o=i.get(a,n);return!(vt(o)&&!isNaN(o)&&o<0)})}}}function $M(r){r.registerChartView(XC),r.registerSeriesModel(KC),Eb("pie",r.registerAction),r.registerLayout(St(HC,"pie")),r.registerProcessor(GC("pie")),r.registerProcessor(QC("pie"))}var JC=function(r){N(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.type="grid",t.dependencies=["xAxis","yAxis"],t.layoutMode="box",t.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"},t}(ot),Mu=function(r){N(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",ye).models[0]},t.type="cartesian2dAxis",t}(ot);Ee(Mu,VT);var Iy={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,showMinLine:!0,showMaxLine:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},jC=it({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},Iy),Ff=it({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},Iy),tD=it({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},Ff),eD=at({logBase:10},Ff);const rD={category:jC,value:Ff,time:tD,log:eD};var iD={value:1,category:1,time:1,log:1};function Nc(r,t,e,i){M(iD,function(n,a){var o=it(it({},rD[a],!0),i,!0),s=function(l){N(u,l);function u(){var f=l!==null&&l.apply(this,arguments)||this;return f.type=t+"Axis."+a,f}return u.prototype.mergeDefaultAndTheme=function(f,h){var c=Un(this),v=c?is(f):{},d=h.getTheme();it(f,d.get(a+"Axis")),it(f,this.getDefaultOption()),f.type=Fc(f),c&&ki(f,v,c)},u.prototype.optionUpdated=function(){var f=this.option;f.type==="category"&&(this.__ordinalMeta=Tu.createByAxisModel(this))},u.prototype.getCategories=function(f){var h=this.option;if(h.type==="category")return f?h.data:this.__ordinalMeta.categories},u.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},u.type=t+"Axis."+a,u.defaultOption=o,u}(e);r.registerComponentModel(s)}),r.registerSubTypeDefaulter(t+"Axis",Fc)}function Fc(r){return r.type||(r.data?"category":"value")}var nD=function(){function r(t){this.type="cartesian",this._dimList=[],this._axes={},this.name=t||""}return r.prototype.getAxis=function(t){return this._axes[t]},r.prototype.getAxes=function(){return V(this._dimList,function(t){return this._axes[t]},this)},r.prototype.getAxesByScale=function(t){return t=t.toLowerCase(),Mt(this.getAxes(),function(e){return e.scale.type===t})},r.prototype.addAxis=function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},r}(),Au=["x","y"];function zc(r){return r.type==="interval"||r.type==="time"}var aD=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="cartesian2d",e.dimensions=Au,e}return t.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var e=this.getAxis("x").scale,i=this.getAxis("y").scale;if(!(!zc(e)||!zc(i))){var n=e.getExtent(),a=i.getExtent(),o=this.dataToPoint([n[0],a[0]]),s=this.dataToPoint([n[1],a[1]]),l=n[1]-n[0],u=a[1]-a[0];if(!(!l||!u)){var f=(s[0]-o[0])/l,h=(s[1]-o[1])/u,c=o[0]-n[0]*f,v=o[1]-a[0]*h,d=this._transform=[f,0,0,h,c,v];this._invTransform=Yu([],d)}}},t.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},t.prototype.containPoint=function(e){var i=this.getAxis("x"),n=this.getAxis("y");return i.contain(i.toLocalCoord(e[0]))&&n.contain(n.toLocalCoord(e[1]))},t.prototype.containData=function(e){return this.getAxis("x").containData(e[0])&&this.getAxis("y").containData(e[1])},t.prototype.containZone=function(e,i){var n=this.dataToPoint(e),a=this.dataToPoint(i),o=this.getArea(),s=new nt(n[0],n[1],a[0]-n[0],a[1]-n[1]);return o.intersect(s)},t.prototype.dataToPoint=function(e,i,n){n=n||[];var a=e[0],o=e[1];if(this._transform&&a!=null&&isFinite(a)&&o!=null&&isFinite(o))return ue(n,e,this._transform);var s=this.getAxis("x"),l=this.getAxis("y");return n[0]=s.toGlobalCoord(s.dataToCoord(a,i)),n[1]=l.toGlobalCoord(l.dataToCoord(o,i)),n},t.prototype.clampData=function(e,i){var n=this.getAxis("x").scale,a=this.getAxis("y").scale,o=n.getExtent(),s=a.getExtent(),l=n.parse(e[0]),u=a.parse(e[1]);return i=i||[],i[0]=Math.min(Math.max(Math.min(o[0],o[1]),l),Math.max(o[0],o[1])),i[1]=Math.min(Math.max(Math.min(s[0],s[1]),u),Math.max(s[0],s[1])),i},t.prototype.pointToData=function(e,i){var n=[];if(this._invTransform)return ue(n,e,this._invTransform);var a=this.getAxis("x"),o=this.getAxis("y");return n[0]=a.coordToData(a.toLocalCoord(e[0]),i),n[1]=o.coordToData(o.toLocalCoord(e[1]),i),n},t.prototype.getOtherAxis=function(e){return this.getAxis(e.dim==="x"?"y":"x")},t.prototype.getArea=function(e){e=e||0;var i=this.getAxis("x").getGlobalExtent(),n=this.getAxis("y").getGlobalExtent(),a=Math.min(i[0],i[1])-e,o=Math.min(n[0],n[1])-e,s=Math.max(i[0],i[1])-a+e,l=Math.max(n[0],n[1])-o+e;return new nt(a,o,s,l)},t}(nD),oD=function(r){N(t,r);function t(e,i,n,a,o){var s=r.call(this,e,i,n)||this;return s.index=0,s.type=a||"value",s.position=o||"bottom",s}return t.prototype.isHorizontal=function(){var e=this.position;return e==="top"||e==="bottom"},t.prototype.getGlobalExtent=function(e){var i=this.getExtent();return i[0]=this.toGlobalCoord(i[0]),i[1]=this.toGlobalCoord(i[1]),e&&i[0]>i[1]&&i.reverse(),i},t.prototype.pointToData=function(e,i){return this.coordToData(this.toLocalCoord(e[this.dim==="x"?0:1]),i)},t.prototype.setCategorySortInfo=function(e){if(this.type!=="category")return!1;this.model.option.categorySortInfo=e,this.scale.setSortInfo(e)},t}(JT);function Lu(r,t,e){e=e||{};var i=r.coordinateSystem,n=t.axis,a={},o=n.getAxesOnZeroOf()[0],s=n.position,l=o?"onZero":s,u=n.dim,f=i.getRect(),h=[f.x,f.x+f.width,f.y,f.y+f.height],c={left:0,right:1,top:0,bottom:1,onZero:2},v=t.get("offset")||0,d=u==="x"?[h[2]-v,h[3]+v]:[h[0]-v,h[1]+v];if(o){var g=o.toGlobalCoord(o.dataToCoord(0));d[c.onZero]=Math.max(Math.min(g,d[1]),d[0])}a.position=[u==="y"?d[c[l]]:h[0],u==="x"?d[c[l]]:h[3]],a.rotation=Math.PI/2*(u==="x"?0:1);var p={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=p[s],a.labelOffset=o?d[c[s]]-d[c.onZero]:0,t.get(["axisTick","inside"])&&(a.tickDirection=-a.tickDirection),Fn(e.labelInside,t.get(["axisLabel","inside"]))&&(a.labelDirection=-a.labelDirection);var y=t.get(["axisLabel","rotate"]);return a.labelRotate=l==="top"?-y:y,a.z2=1,a}function Hc(r){return r.get("coordinateSystem")==="cartesian2d"}function Gc(r){var t={xAxisModel:null,yAxisModel:null};return M(t,function(e,i){var n=i.replace(/Model$/,""),a=r.getReferringComponents(n,ye).models[0];t[i]=a}),t}var Tl=Math.log;function sD(r,t,e){var i=Wi.prototype,n=i.getTicks.call(e),a=i.getTicks.call(e,!0),o=n.length-1,s=i.getInterval.call(e),l=hy(r,t),u=l.extent,f=l.fixMin,h=l.fixMax;if(r.type==="log"){var c=Tl(r.base);u=[Tl(u[0])/c,Tl(u[1])/c]}r.setExtent(u[0],u[1]),r.calcNiceExtent({splitNumber:o,fixMin:f,fixMax:h});var v=i.getExtent.call(r);f&&(u[0]=v[0]),h&&(u[1]=v[1]);var d=i.getInterval.call(r),g=u[0],p=u[1];if(f&&h)d=(p-g)/o;else if(f)for(p=u[0]+d*o;p<u[1]&&isFinite(p)&&isFinite(u[1]);)d=ml(d),p=u[0]+d*o;else if(h)for(g=u[1]-d*o;g>u[0]&&isFinite(g)&&isFinite(u[0]);)d=ml(d),g=u[1]-d*o;else{var y=r.getTicks().length-1;y>o&&(d=ml(d));var m=d*o;p=Math.ceil(u[1]/d)*d,g=wt(p-m),g<0&&u[0]>=0?(g=0,p=wt(m)):p>0&&u[1]<=0&&(p=0,g=-wt(m))}var _=(n[0].value-a[0].value)/s,S=(n[o].value-a[o].value)/s;i.setExtent.call(r,g+d*_,p+d*S),i.setInterval.call(r,d),(_||S)&&i.setNiceExtent.call(r,g+d,p-d)}var lD=function(){function r(t,e,i){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=Au,this._initCartesian(t,e,i),this.model=t}return r.prototype.getRect=function(){return this._rect},r.prototype.update=function(t,e){var i=this._axesMap;this._updateScale(t,this.model);function n(o){var s,l=dt(o),u=l.length;if(u){for(var f=[],h=u-1;h>=0;h--){var c=+l[h],v=o[c],d=v.model,g=v.scale;Cu(g)&&d.get("alignTicks")&&d.get("interval")==null?f.push(v):(bc(g,d),Cu(g)&&(s=v))}f.length&&(s||(s=f.pop(),bc(s.scale,s.model)),M(f,function(p){sD(p.scale,p.model,s.scale)}))}}n(i.x),n(i.y);var a={};M(i.x,function(o){Vc(i,"y",o,a)}),M(i.y,function(o){Vc(i,"x",o,a)}),this.resize(this.model,e)},r.prototype.resize=function(t,e,i){var n=t.getBoxLayoutParams(),a=!i&&t.get("containLabel"),o=Oi(n,{width:e.getWidth(),height:e.getHeight()});this._rect=o;var s=this._axesList;l(),a&&(M(s,function(u){if(!u.model.get(["axisLabel","inside"])){var f=zT(u);if(f){var h=u.isHorizontal()?"height":"width",c=u.model.get(["axisLabel","margin"]);o[h]-=f[h]+c,u.position==="top"?o.y+=f.height+c:u.position==="left"&&(o.x+=f.width+c)}}}),l()),M(this._coordsList,function(u){u.calcAffineTransform()});function l(){M(s,function(u){var f=u.isHorizontal(),h=f?[0,o.width]:[0,o.height],c=u.inverse?1:0;u.setExtent(h[c],h[1-c]),uD(u,f?o.x:o.y)})}},r.prototype.getAxis=function(t,e){var i=this._axesMap[t];if(i!=null)return i[e||0]},r.prototype.getAxes=function(){return this._axesList.slice()},r.prototype.getCartesian=function(t,e){if(t!=null&&e!=null){var i="x"+t+"y"+e;return this._coordsMap[i]}W(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var n=0,a=this._coordsList;n<a.length;n++)if(a[n].getAxis("x").index===t||a[n].getAxis("y").index===e)return a[n]},r.prototype.getCartesians=function(){return this._coordsList.slice()},r.prototype.convertToPixel=function(t,e,i){var n=this._findConvertTarget(e);return n.cartesian?n.cartesian.dataToPoint(i):n.axis?n.axis.toGlobalCoord(n.axis.dataToCoord(i)):null},r.prototype.convertFromPixel=function(t,e,i){var n=this._findConvertTarget(e);return n.cartesian?n.cartesian.pointToData(i):n.axis?n.axis.coordToData(n.axis.toLocalCoord(i)):null},r.prototype._findConvertTarget=function(t){var e=t.seriesModel,i=t.xAxisModel||e&&e.getReferringComponents("xAxis",ye).models[0],n=t.yAxisModel||e&&e.getReferringComponents("yAxis",ye).models[0],a=t.gridModel,o=this._coordsList,s,l;if(e)s=e.coordinateSystem,lt(o,s)<0&&(s=null);else if(i&&n)s=this.getCartesian(i.componentIndex,n.componentIndex);else if(i)l=this.getAxis("x",i.componentIndex);else if(n)l=this.getAxis("y",n.componentIndex);else if(a){var u=a.coordinateSystem;u===this&&(s=this._coordsList[0])}return{cartesian:s,axis:l}},r.prototype.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},r.prototype._initCartesian=function(t,e,i){var n=this,a=this,o={left:!1,right:!1,top:!1,bottom:!1},s={x:{},y:{}},l={x:0,y:0};if(e.eachComponent("xAxis",u("x"),this),e.eachComponent("yAxis",u("y"),this),!l.x||!l.y){this._axesMap={},this._axesList=[];return}this._axesMap=s,M(s.x,function(f,h){M(s.y,function(c,v){var d="x"+h+"y"+v,g=new aD(d);g.master=n,g.model=t,n._coordsMap[d]=g,n._coordsList.push(g),g.addAxis(f),g.addAxis(c)})});function u(f){return function(h,c){if(Cl(h,t)){var v=h.get("position");f==="x"?v!=="top"&&v!=="bottom"&&(v=o.bottom?"top":"bottom"):v!=="left"&&v!=="right"&&(v=o.left?"right":"left"),o[v]=!0;var d=new oD(f,NT(h),[0,0],h.get("type"),v),g=d.type==="category";d.onBand=g&&h.get("boundaryGap"),d.inverse=h.get("inverse"),h.axis=d,d.model=h,d.grid=a,d.index=c,a._axesList.push(d),s[f][c]=d,l[f]++}}}},r.prototype._updateScale=function(t,e){M(this._axesList,function(n){if(n.scale.setExtent(1/0,-1/0),n.type==="category"){var a=n.model.get("categorySortInfo");n.scale.setSortInfo(a)}}),t.eachSeries(function(n){if(Hc(n)){var a=Gc(n),o=a.xAxisModel,s=a.yAxisModel;if(!Cl(o,e)||!Cl(s,e))return;var l=this.getCartesian(o.componentIndex,s.componentIndex),u=n.getData(),f=l.getAxis("x"),h=l.getAxis("y");i(u,f),i(u,h)}},this);function i(n,a){M(GT(n,a.dim),function(o){a.scale.unionExtentFromData(n,o)})}},r.prototype.getTooltipAxes=function(t){var e=[],i=[];return M(this.getCartesians(),function(n){var a=t!=null&&t!=="auto"?n.getAxis(t):n.getBaseAxis(),o=n.getOtherAxis(a);lt(e,a)<0&&e.push(a),lt(i,o)<0&&i.push(o)}),{baseAxes:e,otherAxes:i}},r.create=function(t,e){var i=[];return t.eachComponent("grid",function(n,a){var o=new r(n,t,e);o.name="grid_"+a,o.resize(n,e,!0),n.coordinateSystem=o,i.push(o)}),t.eachSeries(function(n){if(Hc(n)){var a=Gc(n),o=a.xAxisModel,s=a.yAxisModel,l=o.getCoordSysModel(),u=l.coordinateSystem;n.coordinateSystem=u.getCartesian(o.componentIndex,s.componentIndex)}}),i},r.dimensions=Au,r}();function Cl(r,t){return r.getCoordSysModel()===t}function Vc(r,t,e,i){e.getAxesOnZeroOf=function(){return a?[a]:[]};var n=r[t],a,o=e.model,s=o.get(["axisLine","onZero"]),l=o.get(["axisLine","onZeroAxisIndex"]);if(!s)return;if(l!=null)Wc(n[l])&&(a=n[l]);else for(var u in n)if(n.hasOwnProperty(u)&&Wc(n[u])&&!i[f(n[u])]){a=n[u];break}a&&(i[f(a)]=!0);function f(h){return h.dim+"_"+h.index}}function Wc(r){return r&&r.type!=="category"&&r.type!=="time"&&FT(r)}function uD(r,t){var e=r.getExtent(),i=e[0]+e[1];r.toGlobalCoord=r.dim==="x"?function(n){return n+t}:function(n){return i-n+t},r.toLocalCoord=r.dim==="x"?function(n){return n-t}:function(n){return i-n+t}}var fr=Math.PI,pr=function(){function r(t,e){this.group=new Ot,this.opt=e,this.axisModel=t,at(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});var i=new Ot({x:e.position[0],y:e.position[1],rotation:e.rotation});i.updateTransform(),this._transformGroup=i}return r.prototype.hasBuilder=function(t){return!!Uc[t]},r.prototype.add=function(t){Uc[t](this.opt,this.axisModel,this.group,this._transformGroup)},r.prototype.getGroup=function(){return this.group},r.innerTextLayout=function(t,e,i){var n=Ud(e-t),a,o;return co(n)?(o=i>0?"top":"bottom",a="center"):co(n-fr)?(o=i>0?"bottom":"top",a="center"):(o="middle",n>0&&n<fr?a=i>0?"right":"left":a=i>0?"left":"right"),{rotation:n,textAlign:a,textVerticalAlign:o}},r.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},r.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},r}(),Uc={axisLine:function(r,t,e,i){var n=t.get(["axisLine","show"]);if(n==="auto"&&r.handleAutoShown&&(n=r.handleAutoShown("axisLine")),!!n){var a=t.axis.getExtent(),o=i.transform,s=[a[0],0],l=[a[1],0],u=s[0]>l[0];o&&(ue(s,s,o),ue(l,l,o));var f=O({lineCap:"round"},t.getModel(["axisLine","lineStyle"]).getLineStyle()),h=new mr({shape:{x1:s[0],y1:s[1],x2:l[0],y2:l[1]},style:f,strokeContainThreshold:r.strokeContainThreshold||5,silent:!0,z2:1});Vn(h.shape,h.style.lineWidth),h.anid="line",e.add(h);var c=t.get(["axisLine","symbol"]);if(c!=null){var v=t.get(["axisLine","symbolSize"]);G(c)&&(c=[c,c]),(G(v)||vt(v))&&(v=[v,v]);var d=Lg(t.get(["axisLine","symbolOffset"])||0,v),g=v[0],p=v[1];M([{rotate:r.rotation+Math.PI/2,offset:d[0],r:0},{rotate:r.rotation-Math.PI/2,offset:d[1],r:Math.sqrt((s[0]-l[0])*(s[0]-l[0])+(s[1]-l[1])*(s[1]-l[1]))}],function(y,m){if(c[m]!=="none"&&c[m]!=null){var _=Ni(c[m],-g/2,-p/2,g,p,f.stroke,!0),S=y.r+y.offset,b=u?l:s;_.attr({rotation:y.rotate,x:b[0]+S*Math.cos(r.rotation),y:b[1]-S*Math.sin(r.rotation),silent:!0,z2:11}),e.add(_)}})}}},axisTickLabel:function(r,t,e,i){var n=vD(e,i,t,r),a=dD(e,i,t,r);if(hD(t,a,n),cD(e,i,t,r.tickDirection),t.get(["axisLabel","hideOverlap"])){var o=aC(V(a,function(s){return{label:s,priority:s.z2,defaultAttr:{ignore:s.ignore}}}));lC(o)}},axisName:function(r,t,e,i){var n=Fn(r.axisName,t.get("name"));if(n){var a=t.get("nameLocation"),o=r.nameDirection,s=t.getModel("nameTextStyle"),l=t.get("nameGap")||0,u=t.axis.getExtent(),f=u[0]>u[1]?-1:1,h=[a==="start"?u[0]-f*l:a==="end"?u[1]+f*l:(u[0]+u[1])/2,Xc(a)?r.labelOffset+o*l:0],c,v=t.get("nameRotate");v!=null&&(v=v*fr/180);var d;Xc(a)?c=pr.innerTextLayout(r.rotation,v??r.rotation,o):(c=fD(r.rotation,a,v||0,u),d=r.axisNameAvailableWidth,d!=null&&(d=Math.abs(d/Math.sin(c.rotation)),!isFinite(d)&&(d=null)));var g=s.getFont(),p=t.get("nameTruncate",!0)||{},y=p.ellipsis,m=Fn(r.nameTruncateMaxWidth,p.maxWidth,d),_=new Lt({x:h[0],y:h[1],rotation:c.rotation,silent:pr.isLabelSilent(t),style:_r(s,{text:n,font:g,overflow:"truncate",width:m,ellipsis:y,fill:s.getTextColor()||t.get(["axisLine","lineStyle","color"]),align:s.get("align")||c.textAlign,verticalAlign:s.get("verticalAlign")||c.textVerticalAlign}),z2:1});if(Xo({el:_,componentModel:t,itemName:n}),_.__fullText=n,_.anid="name",t.get("triggerEvent")){var S=pr.makeAxisEventDataBase(t);S.targetType="axisName",S.name=n,st(_).eventData=S}i.add(_),_.updateTransform(),e.add(_),_.decomposeTransform()}}};function fD(r,t,e,i){var n=Ud(e-r),a,o,s=i[0]>i[1],l=t==="start"&&!s||t!=="start"&&s;return co(n-fr/2)?(o=l?"bottom":"top",a="center"):co(n-fr*1.5)?(o=l?"top":"bottom",a="center"):(o="middle",n<fr*1.5&&n>fr/2?a=l?"left":"right":a=l?"right":"left"),{rotation:n,textAlign:a,textVerticalAlign:o}}function hD(r,t,e){if(!vy(r.axis)){var i=r.get(["axisLabel","showMinLabel"]),n=r.get(["axisLabel","showMaxLabel"]);t=t||[],e=e||[];var a=t[0],o=t[1],s=t[t.length-1],l=t[t.length-2],u=e[0],f=e[1],h=e[e.length-1],c=e[e.length-2];i===!1?(te(a),te(u)):Yc(a,o)&&(i?(te(o),te(f)):(te(a),te(u))),n===!1?(te(s),te(h)):Yc(l,s)&&(n?(te(l),te(c)):(te(s),te(h)))}}function te(r){r&&(r.ignore=!0)}function Yc(r,t){var e=r&&r.getBoundingRect().clone(),i=t&&t.getBoundingRect().clone();if(!(!e||!i)){var n=Wu([]);return Uu(n,n,-r.rotation),e.applyTransform(Li([],n,r.getLocalTransform())),i.applyTransform(Li([],n,t.getLocalTransform())),e.intersect(i)}}function Xc(r){return r==="middle"||r==="center"}function Py(r,t,e,i,n){for(var a=[],o=[],s=[],l=0;l<r.length;l++){var u=r[l].coord;o[0]=u,o[1]=0,s[0]=u,s[1]=e,t&&(ue(o,o,t),ue(s,s,t));var f=new mr({shape:{x1:o[0],y1:o[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0});Vn(f.shape,f.style.lineWidth),f.anid=n+"_"+r[l].tickValue,a.push(f)}return a}function vD(r,t,e,i){var n=e.axis,a=e.getModel("axisTick"),o=a.get("show");if(o==="auto"&&i.handleAutoShown&&(o=i.handleAutoShown("axisTick")),!(!o||n.scale.isBlank())){for(var s=a.getModel("lineStyle"),l=i.tickDirection*a.get("length"),u=n.getTicksCoords(),f=Py(u,t.transform,l,at(s.getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])}),"ticks"),h=0;h<f.length;h++)r.add(f[h]);return f}}function cD(r,t,e,i){var n=e.axis,a=e.getModel("minorTick");if(!(!a.get("show")||n.scale.isBlank())){var o=n.getMinorTicksCoords();if(o.length)for(var s=a.getModel("lineStyle"),l=i*a.get("length"),u=at(s.getLineStyle(),at(e.getModel("axisTick").getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])})),f=0;f<o.length;f++)for(var h=Py(o[f],t.transform,l,u,"minorticks_"+f),c=0;c<h.length;c++)r.add(h[c])}}function dD(r,t,e,i){var n=e.axis,a=Fn(i.axisLabelShow,e.get(["axisLabel","show"]));if(!(!a||n.scale.isBlank())){var o=e.getModel("axisLabel"),s=o.get("margin"),l=n.getViewLabels(),u=(Fn(i.labelRotate,o.get("rotate"))||0)*fr/180,f=pr.innerTextLayout(i.rotation,u,i.labelDirection),h=e.getCategories&&e.getCategories(!0),c=[],v=pr.isLabelSilent(e),d=e.get("triggerEvent");return M(l,function(g,p){var y=n.scale.type==="ordinal"?n.scale.getRawOrdinalNumber(g.tickValue):g.tickValue,m=g.formattedLabel,_=g.rawLabel,S=o;if(h&&h[y]){var b=h[y];W(b)&&b.textStyle&&(S=new gt(b.textStyle,o,e.ecModel))}var w=S.getTextColor()||e.get(["axisLine","lineStyle","color"]),x=n.dataToCoord(y),C=S.getShallow("align",!0)||f.textAlign,T=K(S.getShallow("alignMinLabel",!0),C),D=K(S.getShallow("alignMaxLabel",!0),C),A=S.getShallow("verticalAlign",!0)||S.getShallow("baseline",!0)||f.textVerticalAlign,L=K(S.getShallow("verticalAlignMinLabel",!0),A),P=K(S.getShallow("verticalAlignMaxLabel",!0),A),I=new Lt({x,y:i.labelOffset+i.labelDirection*s,rotation:f.rotation,silent:v,z2:10+(g.level||0),style:_r(S,{text:m,align:p===0?T:p===l.length-1?D:C,verticalAlign:p===0?L:p===l.length-1?P:A,fill:q(w)?w(n.type==="category"?_:n.type==="value"?y+"":y,p):w})});if(I.anid="label_"+y,Xo({el:I,componentModel:e,itemName:m,formatterParamsExtra:{isTruncated:function(){return I.isTruncated},value:_,tickIndex:p}}),d){var R=pr.makeAxisEventDataBase(e);R.targetType="axisLabel",R.value=_,R.tickIndex=p,n.type==="category"&&(R.dataIndex=y),st(I).eventData=R}t.add(I),I.updateTransform(),c.push(I),r.add(I),I.decomposeTransform()}),c}}function pD(r,t){var e={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return gD(e,r,t),e.seriesInvolved&&mD(e,r),e}function gD(r,t,e){var i=t.getComponent("tooltip"),n=t.getComponent("axisPointer"),a=n.get("link",!0)||[],o=[];M(e.getCoordinateSystems(),function(s){if(!s.axisPointerEnabled)return;var l=Qn(s.model),u=r.coordSysAxesInfo[l]={};r.coordSysMap[l]=s;var f=s.model,h=f.getModel("tooltip",i);if(M(s.getAxes(),St(g,!1,null)),s.getTooltipAxes&&i&&h.get("show")){var c=h.get("trigger")==="axis",v=h.get(["axisPointer","type"])==="cross",d=s.getTooltipAxes(h.get(["axisPointer","axis"]));(c||v)&&M(d.baseAxes,St(g,v?"cross":!0,c)),v&&M(d.otherAxes,St(g,"cross",!1))}function g(p,y,m){var _=m.model.getModel("axisPointer",n),S=_.get("show");if(!(!S||S==="auto"&&!p&&!Iu(_))){y==null&&(y=_.get("triggerTooltip")),_=p?yD(m,h,n,t,p,y):_;var b=_.get("snap"),w=_.get("triggerEmphasis"),x=Qn(m.model),C=y||b||m.type==="category",T=r.axesInfo[x]={key:x,axis:m,coordSys:s,axisPointerModel:_,triggerTooltip:y,triggerEmphasis:w,involveSeries:C,snap:b,useHandle:Iu(_),seriesModels:[],linkGroup:null};u[x]=T,r.seriesInvolved=r.seriesInvolved||C;var D=_D(a,m);if(D!=null){var A=o[D]||(o[D]={axesInfo:{}});A.axesInfo[x]=T,A.mapper=a[D].mapper,T.linkGroup=A}}}})}function yD(r,t,e,i,n,a){var o=t.getModel("axisPointer"),s=["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],l={};M(s,function(c){l[c]=j(o.get(c))}),l.snap=r.type!=="category"&&!!a,o.get("type")==="cross"&&(l.type="line");var u=l.label||(l.label={});if(u.show==null&&(u.show=!1),n==="cross"){var f=o.get(["label","show"]);if(u.show=f??!0,!a){var h=l.lineStyle=o.get("crossStyle");h&&at(u,h.textStyle)}}return r.model.getModel("axisPointer",new gt(l,e,i))}function mD(r,t){t.eachSeries(function(e){var i=e.coordinateSystem,n=e.get(["tooltip","trigger"],!0),a=e.get(["tooltip","show"],!0);!i||n==="none"||n===!1||n==="item"||a===!1||e.get(["axisPointer","show"],!0)===!1||M(r.coordSysAxesInfo[Qn(i.model)],function(o){var s=o.axis;i.getAxis(s.dim)===s&&(o.seriesModels.push(e),o.seriesDataCount==null&&(o.seriesDataCount=0),o.seriesDataCount+=e.getData().count())})})}function _D(r,t){for(var e=t.model,i=t.dim,n=0;n<r.length;n++){var a=r[n]||{};if(Dl(a[i+"AxisId"],e.id)||Dl(a[i+"AxisIndex"],e.componentIndex)||Dl(a[i+"AxisName"],e.name))return n}}function Dl(r,t){return r==="all"||F(r)&&lt(r,t)>=0||r===t}function SD(r){var t=zf(r);if(t){var e=t.axisPointerModel,i=t.axis.scale,n=e.option,a=e.get("status"),o=e.get("value");o!=null&&(o=i.parse(o));var s=Iu(e);a==null&&(n.status=s?"show":"hide");var l=i.getExtent().slice();l[0]>l[1]&&l.reverse(),(o==null||o>l[1])&&(o=l[1]),o<l[0]&&(o=l[0]),n.value=o,s&&(n.status=t.axis.scale.isBlank()?"hide":"show")}}function zf(r){var t=(r.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return t&&t.axesInfo[Qn(r)]}function wD(r){var t=zf(r);return t&&t.axisPointerModel}function Iu(r){return!!r.get(["handle","show"])}function Qn(r){return r.type+"||"+r.id}var $c={},Ry=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n,a){this.axisPointerClass&&SD(e),r.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(e,n,!0)},t.prototype.updateAxisPointer=function(e,i,n,a){this._doUpdateAxisPointerClass(e,n,!1)},t.prototype.remove=function(e,i){var n=this._axisPointer;n&&n.remove(i)},t.prototype.dispose=function(e,i){this._disposeAxisPointer(i),r.prototype.dispose.apply(this,arguments)},t.prototype._doUpdateAxisPointerClass=function(e,i,n){var a=t.getAxisPointerClass(this.axisPointerClass);if(a){var o=wD(e);o?(this._axisPointer||(this._axisPointer=new a)).render(e,o,i,n):this._disposeAxisPointer(i)}},t.prototype._disposeAxisPointer=function(e){this._axisPointer&&this._axisPointer.dispose(e),this._axisPointer=null},t.registerAxisPointerClass=function(e,i){$c[e]=i},t.getAxisPointerClass=function(e){return e&&$c[e]},t.type="axis",t}(_e),Pu=_t();function bD(r,t,e,i){var n=e.axis;if(!n.scale.isBlank()){var a=e.getModel("splitArea"),o=a.getModel("areaStyle"),s=o.get("color"),l=i.coordinateSystem.getRect(),u=n.getTicksCoords({tickModel:a,clamp:!0});if(u.length){var f=s.length,h=Pu(r).splitAreaColors,c=Q(),v=0;if(h)for(var d=0;d<u.length;d++){var g=h.get(u[d].tickValue);if(g!=null){v=(g+(f-1)*d)%f;break}}var p=n.toGlobalCoord(u[0].coord),y=o.getAreaStyle();s=F(s)?s:[s];for(var d=1;d<u.length;d++){var m=n.toGlobalCoord(u[d].coord),_=void 0,S=void 0,b=void 0,w=void 0;n.isHorizontal()?(_=p,S=l.y,b=m-_,w=l.height,p=_+b):(_=l.x,S=p,b=l.width,w=m-S,p=S+w);var x=u[d-1].tickValue;x!=null&&c.set(x,v),t.add(new xt({anid:x!=null?"area_"+x:null,shape:{x:_,y:S,width:b,height:w},style:at({fill:s[v]},y),autoBatch:!0,silent:!0})),v=(v+1)%f}Pu(r).splitAreaColors=c}}}function xD(r){Pu(r).splitAreaColors=null}var TD=["axisLine","axisTickLabel","axisName"],CD=["splitArea","splitLine","minorSplitLine"],Ey=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.axisPointerClass="CartesianAxisPointer",e}return t.prototype.render=function(e,i,n,a){this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new Ot,this.group.add(this._axisGroup),!!e.get("show")){var s=e.getCoordSysModel(),l=Lu(s,e),u=new pr(e,O({handleAutoShown:function(h){for(var c=s.coordinateSystem.getCartesians(),v=0;v<c.length;v++)if(Cu(c[v].getOtherAxis(e.axis).scale))return!0;return!1}},l));M(TD,u.add,u),this._axisGroup.add(u.getGroup()),M(CD,function(h){e.get([h,"show"])&&DD[h](this,this._axisGroup,e,s)},this);var f=a&&a.type==="changeAxisOrder"&&a.isInitSort;f||Ip(o,this._axisGroup,e),r.prototype.render.call(this,e,i,n,a)}},t.prototype.remove=function(){xD(this)},t.type="cartesianAxis",t}(Ry),DD={splitLine:function(r,t,e,i){var n=e.axis;if(!n.scale.isBlank()){var a=e.getModel("splitLine"),o=a.getModel("lineStyle"),s=o.get("color"),l=a.get("showMinLine")!==!1,u=a.get("showMaxLine")!==!1;s=F(s)?s:[s];for(var f=i.coordinateSystem.getRect(),h=n.isHorizontal(),c=0,v=n.getTicksCoords({tickModel:a}),d=[],g=[],p=o.getLineStyle(),y=0;y<v.length;y++){var m=n.toGlobalCoord(v[y].coord);if(!(y===0&&!l||y===v.length-1&&!u)){var _=v[y].tickValue;h?(d[0]=m,d[1]=f.y,g[0]=m,g[1]=f.y+f.height):(d[0]=f.x,d[1]=m,g[0]=f.x+f.width,g[1]=m);var S=c++%s.length,b=new mr({anid:_!=null?"line_"+_:null,autoBatch:!0,shape:{x1:d[0],y1:d[1],x2:g[0],y2:g[1]},style:at({stroke:s[S]},p),silent:!0});Vn(b.shape,p.lineWidth),t.add(b)}}}},minorSplitLine:function(r,t,e,i){var n=e.axis,a=e.getModel("minorSplitLine"),o=a.getModel("lineStyle"),s=i.coordinateSystem.getRect(),l=n.isHorizontal(),u=n.getMinorTicksCoords();if(u.length)for(var f=[],h=[],c=o.getLineStyle(),v=0;v<u.length;v++)for(var d=0;d<u[v].length;d++){var g=n.toGlobalCoord(u[v][d].coord);l?(f[0]=g,f[1]=s.y,h[0]=g,h[1]=s.y+s.height):(f[0]=s.x,f[1]=g,h[0]=s.x+s.width,h[1]=g);var p=new mr({anid:"minor_line_"+u[v][d].tickValue,autoBatch:!0,shape:{x1:f[0],y1:f[1],x2:h[0],y2:h[1]},style:c,silent:!0});Vn(p.shape,c.lineWidth),t.add(p)}},splitArea:function(r,t,e,i){bD(r,t,e,i)}},Oy=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="xAxis",t}(Ey),MD=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=Oy.type,e}return t.type="yAxis",t}(Ey),AD=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="grid",e}return t.prototype.render=function(e,i){this.group.removeAll(),e.get("show")&&this.group.add(new xt({shape:e.coordinateSystem.getRect(),style:at({fill:e.get("backgroundColor")},e.getItemStyle()),silent:!0,z2:-1}))},t.type="grid",t}(_e),Zc={offset:0};function LD(r){r.registerComponentView(AD),r.registerComponentModel(JC),r.registerCoordinateSystem("cartesian2d",lD),Nc(r,"x",Mu,Zc),Nc(r,"y",Mu,Zc),r.registerComponentView(Oy),r.registerComponentView(MD),r.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})}var Wr=_t(),qc=j,Ml=ht,ID=function(){function r(){this._dragging=!1,this.animationThreshold=15}return r.prototype.render=function(t,e,i,n){var a=e.get("value"),o=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=i,!(!n&&this._lastValue===a&&this._lastStatus===o)){this._lastValue=a,this._lastStatus=o;var s=this._group,l=this._handle;if(!o||o==="hide"){s&&s.hide(),l&&l.hide();return}s&&s.show(),l&&l.show();var u={};this.makeElOption(u,a,t,e,i);var f=u.graphicKey;f!==this._lastGraphicKey&&this.clear(i),this._lastGraphicKey=f;var h=this._moveAnimation=this.determineAnimation(t,e);if(!s)s=this._group=new Ot,this.createPointerEl(s,u,t,e),this.createLabelEl(s,u,t,e),i.getZr().add(s);else{var c=St(Kc,e,h);this.updatePointerEl(s,u,c),this.updateLabelEl(s,u,c,e)}Jc(s,e,!0),this._renderHandle(a)}},r.prototype.remove=function(t){this.clear(t)},r.prototype.dispose=function(t){this.clear(t)},r.prototype.determineAnimation=function(t,e){var i=e.get("animation"),n=t.axis,a=n.type==="category",o=e.get("snap");if(!o&&!a)return!1;if(i==="auto"||i==null){var s=this.animationThreshold;if(a&&n.getBandWidth()>s)return!0;if(o){var l=zf(t).seriesDataCount,u=n.getExtent();return Math.abs(u[0]-u[1])/l>s}return!1}return i===!0},r.prototype.makeElOption=function(t,e,i,n,a){},r.prototype.createPointerEl=function(t,e,i,n){var a=e.pointer;if(a){var o=Wr(t).pointerEl=new mS[a.type](qc(e.pointer));t.add(o)}},r.prototype.createLabelEl=function(t,e,i,n){if(e.label){var a=Wr(t).labelEl=new Lt(qc(e.label));t.add(a),Qc(a,n)}},r.prototype.updatePointerEl=function(t,e,i){var n=Wr(t).pointerEl;n&&e.pointer&&(n.setStyle(e.pointer.style),i(n,{shape:e.pointer.shape}))},r.prototype.updateLabelEl=function(t,e,i,n){var a=Wr(t).labelEl;a&&(a.setStyle(e.label.style),i(a,{x:e.label.x,y:e.label.y}),Qc(a,n))},r.prototype._renderHandle=function(t){if(!(this._dragging||!this.updateHandleTransform)){var e=this._axisPointerModel,i=this._api.getZr(),n=this._handle,a=e.getModel("handle"),o=e.get("status");if(!a.get("show")||!o||o==="hide"){n&&i.remove(n),this._handle=null;return}var s;this._handle||(s=!0,n=this._handle=vf(a.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(u){Cd(u.event)},onmousedown:Ml(this._onHandleDragMove,this,0,0),drift:Ml(this._onHandleDragMove,this),ondragend:Ml(this._onHandleDragEnd,this)}),i.add(n)),Jc(n,e,!1),n.setStyle(a.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"]));var l=a.get("size");F(l)||(l=[l,l]),n.scaleX=l[0]/2,n.scaleY=l[1]/2,wg(this,"_doDispatchAxisPointer",a.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,s)}},r.prototype._moveHandleToValue=function(t,e){Kc(this._axisPointerModel,!e&&this._moveAnimation,this._handle,Al(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},r.prototype._onHandleDragMove=function(t,e){var i=this._handle;if(i){this._dragging=!0;var n=this.updateHandleTransform(Al(i),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=n,i.stopAnimation(),i.attr(Al(n)),Wr(i).lastProp=null,this._doDispatchAxisPointer()}},r.prototype._doDispatchAxisPointer=function(){var t=this._handle;if(t){var e=this._payloadInfo,i=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:i.axis.dim,axisIndex:i.componentIndex}]})}},r.prototype._onHandleDragEnd=function(){this._dragging=!1;var t=this._handle;if(t){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},r.prototype.clear=function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),i=this._group,n=this._handle;e&&i&&(this._lastGraphicKey=null,i&&e.remove(i),n&&e.remove(n),this._group=null,this._handle=null,this._payloadInfo=null),pu(this,"_doDispatchAxisPointer")},r.prototype.doClear=function(){},r.prototype.buildLabel=function(t,e,i){return i=i||0,{x:t[i],y:t[1-i],width:e[i],height:e[1-i]}},r}();function Kc(r,t,e,i){ky(Wr(e).lastProp,i)||(Wr(e).lastProp=i,t?Re(e,i,r):(e.stopAnimation(),e.attr(i)))}function ky(r,t){if(W(r)&&W(t)){var e=!0;return M(t,function(i,n){e=e&&ky(r[n],i)}),!!e}else return r===t}function Qc(r,t){r[t.get(["label","show"])?"show":"hide"]()}function Al(r){return{x:r.x||0,y:r.y||0,rotation:r.rotation||0}}function Jc(r,t,e){var i=t.get("z"),n=t.get("zlevel");r&&r.traverse(function(a){a.type!=="group"&&(i!=null&&(a.z=i),n!=null&&(a.zlevel=n),a.silent=e)})}function PD(r){var t=r.get("type"),e=r.getModel(t+"Style"),i;return t==="line"?(i=e.getLineStyle(),i.fill=null):t==="shadow"&&(i=e.getAreaStyle(),i.stroke=null),i}function RD(r,t,e,i,n){var a=e.get("value"),o=By(a,t.axis,t.ecModel,e.get("seriesDataIndices"),{precision:e.get(["label","precision"]),formatter:e.get(["label","formatter"])}),s=e.getModel("label"),l=rs(s.get("padding")||0),u=s.getFont(),f=Zu(o,u),h=n.position,c=f.width+l[1]+l[3],v=f.height+l[0]+l[2],d=n.align;d==="right"&&(h[0]-=c),d==="center"&&(h[0]-=c/2);var g=n.verticalAlign;g==="bottom"&&(h[1]-=v),g==="middle"&&(h[1]-=v/2),ED(h,c,v,i);var p=s.get("backgroundColor");(!p||p==="auto")&&(p=t.get(["axisLine","lineStyle","color"])),r.label={x:h[0],y:h[1],style:_r(s,{text:o,font:u,fill:s.getTextColor(),padding:l,backgroundColor:p}),z2:10}}function ED(r,t,e,i){var n=i.getWidth(),a=i.getHeight();r[0]=Math.min(r[0]+t,n)-t,r[1]=Math.min(r[1]+e,a)-e,r[0]=Math.max(r[0],0),r[1]=Math.max(r[1],0)}function By(r,t,e,i,n){r=t.scale.parse(r);var a=t.scale.getLabel({value:r},{precision:n.precision}),o=n.formatter;if(o){var s={value:kf(t,{value:r}),axisDimension:t.dim,axisIndex:t.index,seriesData:[]};M(i,function(l){var u=e.getSeriesByIndex(l.seriesIndex),f=l.dataIndexInside,h=u&&u.getDataParams(f);h&&s.seriesData.push(h)}),G(o)?a=o.replace("{value}",a):q(o)&&(a=o(s))}return a}function Ny(r,t,e){var i=Ai();return Uu(i,i,e.rotation),zl(i,i,e.position),hf([r.dataToCoord(t),(e.labelOffset||0)+(e.labelDirection||1)*(e.labelMargin||0)],i)}function OD(r,t,e,i,n,a){var o=pr.innerTextLayout(e.rotation,0,e.labelDirection);e.labelMargin=n.get(["label","margin"]),RD(t,i,n,a,{position:Ny(i.axis,r,e),align:o.textAlign,verticalAlign:o.textVerticalAlign})}function kD(r,t,e){return e=e||0,{x1:r[e],y1:r[1-e],x2:t[e],y2:t[1-e]}}function BD(r,t,e){return e=e||0,{x:r[e],y:r[1-e],width:t[e],height:t[1-e]}}var ND=function(r){N(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.makeElOption=function(e,i,n,a,o){var s=n.axis,l=s.grid,u=a.get("type"),f=jc(l,s).getOtherAxis(s).getGlobalExtent(),h=s.toGlobalCoord(s.dataToCoord(i,!0));if(u&&u!=="none"){var c=PD(a),v=FD[u](s,h,f);v.style=c,e.graphicKey=v.type,e.pointer=v}var d=Lu(l.model,n);OD(i,e,d,n,a,o)},t.prototype.getHandleTransform=function(e,i,n){var a=Lu(i.axis.grid.model,i,{labelInside:!1});a.labelMargin=n.get(["handle","margin"]);var o=Ny(i.axis,e,a);return{x:o[0],y:o[1],rotation:a.rotation+(a.labelDirection<0?Math.PI:0)}},t.prototype.updateHandleTransform=function(e,i,n,a){var o=n.axis,s=o.grid,l=o.getGlobalExtent(!0),u=jc(s,o).getOtherAxis(o).getGlobalExtent(),f=o.dim==="x"?0:1,h=[e.x,e.y];h[f]+=i[f],h[f]=Math.min(l[1],h[f]),h[f]=Math.max(l[0],h[f]);var c=(u[1]+u[0])/2,v=[c,c];v[f]=h[f];var d=[{verticalAlign:"middle"},{align:"center"}];return{x:h[0],y:h[1],rotation:e.rotation,cursorPoint:v,tooltipOption:d[f]}},t}(ID);function jc(r,t){var e={};return e[t.dim+"AxisIndex"]=t.index,r.getCartesian(e)}var FD={line:function(r,t,e){var i=kD([t,e[0]],[t,e[1]],td(r));return{type:"Line",subPixelOptimize:!0,shape:i}},shadow:function(r,t,e){var i=Math.max(1,r.getBandWidth()),n=e[1]-e[0];return{type:"Rect",shape:BD([t-i/2,e[0]],[i,n],td(r))}}};function td(r){return r.dim==="x"?0:1}var zD=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="axisPointer",t.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,triggerEmphasis:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}},t}(ot),Ve=_t(),HD=M;function Fy(r,t,e){if(!$.node){var i=t.getZr();Ve(i).records||(Ve(i).records={}),GD(i,t);var n=Ve(i).records[r]||(Ve(i).records[r]={});n.handler=e}}function GD(r,t){if(Ve(r).initialized)return;Ve(r).initialized=!0,e("click",St(ed,"click")),e("mousemove",St(ed,"mousemove")),e("globalout",WD);function e(i,n){r.on(i,function(a){var o=UD(t);HD(Ve(r).records,function(s){s&&n(s,a,o.dispatchAction)}),VD(o.pendings,t)})}}function VD(r,t){var e=r.showTip.length,i=r.hideTip.length,n;e?n=r.showTip[e-1]:i&&(n=r.hideTip[i-1]),n&&(n.dispatchAction=null,t.dispatchAction(n))}function WD(r,t,e){r.handler("leave",null,e)}function ed(r,t,e,i){t.handler(r,e,i)}function UD(r){var t={showTip:[],hideTip:[]},e=function(i){var n=t[i.type];n?n.push(i):(i.dispatchAction=e,r.dispatchAction(i))};return{dispatchAction:e,pendings:t}}function Ru(r,t){if(!$.node){var e=t.getZr(),i=(Ve(e).records||{})[r];i&&(Ve(e).records[r]=null)}}var YD=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n){var a=i.getComponent("tooltip"),o=e.get("triggerOn")||a&&a.get("triggerOn")||"mousemove|click";Fy("axisPointer",n,function(s,l,u){o!=="none"&&(s==="leave"||o.indexOf(s)>=0)&&u({type:"updateAxisPointer",currTrigger:s,x:l&&l.offsetX,y:l&&l.offsetY})})},t.prototype.remove=function(e,i){Ru("axisPointer",i)},t.prototype.dispose=function(e,i){Ru("axisPointer",i)},t.type="axisPointer",t}(_e);function zy(r,t){var e=[],i=r.seriesIndex,n;if(i==null||!(n=t.getSeriesByIndex(i)))return{point:[]};var a=n.getData(),o=Jr(a,r);if(o==null||o<0||F(o))return{point:[]};var s=a.getItemGraphicEl(o),l=n.coordinateSystem;if(n.getTooltipPosition)e=n.getTooltipPosition(o)||[];else if(l&&l.dataToPoint)if(r.isStacked){var u=l.getBaseAxis(),f=l.getOtherAxis(u),h=f.dim,c=u.dim,v=h==="x"||h==="radius"?1:0,d=a.mapDimension(c),g=[];g[v]=a.get(d,o),g[1-v]=a.get(a.getCalculationInfo("stackResultDimension"),o),e=l.dataToPoint(g)||[]}else e=l.dataToPoint(a.getValues(V(l.dimensions,function(y){return a.mapDimension(y)}),o))||[];else if(s){var p=s.getBoundingRect().clone();p.applyTransform(s.transform),e=[p.x+p.width/2,p.y+p.height/2]}return{point:e,el:s}}var rd=_t();function XD(r,t,e){var i=r.currTrigger,n=[r.x,r.y],a=r,o=r.dispatchAction||ht(e.dispatchAction,e),s=t.getComponent("axisPointer").coordSysAxesInfo;if(s){io(n)&&(n=zy({seriesIndex:a.seriesIndex,dataIndex:a.dataIndex},t).point);var l=io(n),u=a.axesInfo,f=s.axesInfo,h=i==="leave"||io(n),c={},v={},d={list:[],map:{}},g={showPointer:St(ZD,v),showTooltip:St(qD,d)};M(s.coordSysMap,function(y,m){var _=l||y.containPoint(n);M(s.coordSysAxesInfo[m],function(S,b){var w=S.axis,x=jD(u,S);if(!h&&_&&(!u||x)){var C=x&&x.value;C==null&&!l&&(C=w.pointToData(n)),C!=null&&id(S,C,g,!1,c)}})});var p={};return M(f,function(y,m){var _=y.linkGroup;_&&!v[m]&&M(_.axesInfo,function(S,b){var w=v[b];if(S!==y&&w){var x=w.value;_.mapper&&(x=y.axis.scale.parse(_.mapper(x,nd(S),nd(y)))),p[y.key]=x}})}),M(p,function(y,m){id(f[m],y,g,!0,c)}),KD(v,f,c),QD(d,n,r,o),JD(f,o,e),c}}function id(r,t,e,i,n){var a=r.axis;if(!(a.scale.isBlank()||!a.containData(t))){if(!r.involveSeries){e.showPointer(r,t);return}var o=$D(t,r),s=o.payloadBatch,l=o.snapToValue;s[0]&&n.seriesIndex==null&&O(n,s[0]),!i&&r.snap&&a.containData(l)&&l!=null&&(t=l),e.showPointer(r,t,s),e.showTooltip(r,o,l)}}function $D(r,t){var e=t.axis,i=e.dim,n=r,a=[],o=Number.MAX_VALUE,s=-1;return M(t.seriesModels,function(l,u){var f=l.getData().mapDimensionsAll(i),h,c;if(l.getAxisTooltipData){var v=l.getAxisTooltipData(f,r,e);c=v.dataIndices,h=v.nestestValue}else{if(c=l.getData().indicesOfNearest(f[0],r,e.type==="category"?.5:null),!c.length)return;h=l.getData().get(f[0],c[0])}if(!(h==null||!isFinite(h))){var d=r-h,g=Math.abs(d);g<=o&&((g<o||d>=0&&s<0)&&(o=g,s=d,n=h,a.length=0),M(c,function(p){a.push({seriesIndex:l.seriesIndex,dataIndexInside:p,dataIndex:l.getData().getRawIndex(p)})}))}}),{payloadBatch:a,snapToValue:n}}function ZD(r,t,e,i){r[t.key]={value:e,payloadBatch:i}}function qD(r,t,e,i){var n=e.payloadBatch,a=t.axis,o=a.model,s=t.axisPointerModel;if(!(!t.triggerTooltip||!n.length)){var l=t.coordSys.model,u=Qn(l),f=r.map[u];f||(f=r.map[u]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},r.list.push(f)),f.dataByAxis.push({axisDim:a.dim,axisIndex:o.componentIndex,axisType:o.type,axisId:o.id,value:i,valueLabelOpt:{precision:s.get(["label","precision"]),formatter:s.get(["label","formatter"])},seriesDataIndices:n.slice()})}}function KD(r,t,e){var i=e.axesInfo=[];M(t,function(n,a){var o=n.axisPointerModel.option,s=r[a];s?(!n.useHandle&&(o.status="show"),o.value=s.value,o.seriesDataIndices=(s.payloadBatch||[]).slice()):!n.useHandle&&(o.status="hide"),o.status==="show"&&i.push({axisDim:n.axis.dim,axisIndex:n.axis.model.componentIndex,value:o.value})})}function QD(r,t,e,i){if(io(t)||!r.list.length){i({type:"hideTip"});return}var n=((r.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};i({type:"showTip",escapeConnect:!0,x:t[0],y:t[1],tooltipOption:e.tooltipOption,position:e.position,dataIndexInside:n.dataIndexInside,dataIndex:n.dataIndex,seriesIndex:n.seriesIndex,dataByCoordSys:r.list})}function JD(r,t,e){var i=e.getZr(),n="axisPointerLastHighlights",a=rd(i)[n]||{},o=rd(i)[n]={};M(r,function(u,f){var h=u.axisPointerModel.option;h.status==="show"&&u.triggerEmphasis&&M(h.seriesDataIndices,function(c){var v=c.seriesIndex+" | "+c.dataIndex;o[v]=c})});var s=[],l=[];M(a,function(u,f){!o[f]&&l.push(u)}),M(o,function(u,f){!a[f]&&s.push(u)}),l.length&&e.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:l}),s.length&&e.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:s})}function jD(r,t){for(var e=0;e<(r||[]).length;e++){var i=r[e];if(t.axis.dim===i.axisDim&&t.axis.model.componentIndex===i.axisIndex)return i}}function nd(r){var t=r.axis.model,e={},i=e.axisDim=r.axis.dim;return e.axisIndex=e[i+"AxisIndex"]=t.componentIndex,e.axisName=e[i+"AxisName"]=t.name,e.axisId=e[i+"AxisId"]=t.id,e}function io(r){return!r||r[0]==null||isNaN(r[0])||r[1]==null||isNaN(r[1])}function Hy(r){Ry.registerAxisPointerClass("CartesianAxisPointer",ND),r.registerComponentModel(zD),r.registerComponentView(YD),r.registerPreprocessor(function(t){if(t){(!t.axisPointer||t.axisPointer.length===0)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!F(e)&&(t.axisPointer.link=[e])}}),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=pD(t,e)}),r.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},XD)}function ZM(r){ei(LD),ei(Hy)}function tM(r,t){var e=rs(t.get("padding")),i=t.getItemStyle(["color","opacity"]);return i.fill=t.get("backgroundColor"),r=new xt({shape:{x:r.x-e[3],y:r.y-e[0],width:r.width+e[1]+e[3],height:r.height+e[0]+e[2],r:t.get("borderRadius")},style:i,silent:!0,z2:-1}),r}var eM=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="tooltip",t.dependencies=["axisPointer"],t.defaultOption={z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:null,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, .2)",shadowOffsetX:1,shadowOffsetY:2,borderRadius:4,borderWidth:1,padding:null,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#666",fontSize:14}},t}(ot);function Gy(r){var t=r.get("confine");return t!=null?!!t:r.get("renderMode")==="richText"}function Vy(r){if($.domSupported){for(var t=document.documentElement.style,e=0,i=r.length;e<i;e++)if(r[e]in t)return r[e]}}var Wy=Vy(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),rM=Vy(["webkitTransition","transition","OTransition","MozTransition","msTransition"]);function Uy(r,t){if(!r)return t;t=Xp(t,!0);var e=r.indexOf(t);return r=e===-1?t:"-"+r.slice(0,e)+"-"+t,r.toLowerCase()}function iM(r,t){var e=r.currentStyle||document.defaultView&&document.defaultView.getComputedStyle(r);return e?e[t]:null}var nM=Uy(rM,"transition"),Hf=Uy(Wy,"transform"),aM="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+($.transform3dSupported?"will-change:transform;":"");function oM(r){return r=r==="left"?"right":r==="right"?"left":r==="top"?"bottom":"top",r}function sM(r,t,e){if(!G(e)||e==="inside")return"";var i=r.get("backgroundColor"),n=r.get("borderWidth");t=ti(t);var a=oM(e),o=Math.max(Math.round(n)*1.5,6),s="",l=Hf+":",u;lt(["left","right"],a)>-1?(s+="top:50%",l+="translateY(-50%) rotate("+(u=a==="left"?-225:-45)+"deg)"):(s+="left:50%",l+="translateX(-50%) rotate("+(u=a==="top"?225:45)+"deg)");var f=u*Math.PI/180,h=o+n,c=h*Math.abs(Math.cos(f))+h*Math.abs(Math.sin(f)),v=Math.round(((c-Math.SQRT2*n)/2+Math.SQRT2*n-(c-h)/2)*100)/100;s+=";"+a+":-"+v+"px";var d=t+" solid "+n+"px;",g=["position:absolute;width:"+o+"px;height:"+o+"px;z-index:-1;",s+";"+l+";","border-bottom:"+d,"border-right:"+d,"background-color:"+i+";"];return'<div style="'+g.join("")+'"></div>'}function lM(r,t){var e="cubic-bezier(0.23,1,0.32,1)",i=" "+r/2+"s "+e,n="opacity"+i+",visibility"+i;return t||(i=" "+r+"s "+e,n+=$.transformSupported?","+Hf+i:",left"+i+",top"+i),nM+":"+n}function ad(r,t,e){var i=r.toFixed(0)+"px",n=t.toFixed(0)+"px";if(!$.transformSupported)return e?"top:"+n+";left:"+i+";":[["top",n],["left",i]];var a=$.transform3dSupported,o="translate"+(a?"3d":"")+"("+i+","+n+(a?",0":"")+")";return e?"top:0;left:0;"+Hf+":"+o+";":[["top",0],["left",0],[Wy,o]]}function uM(r){var t=[],e=r.get("fontSize"),i=r.getTextColor();i&&t.push("color:"+i),t.push("font:"+r.getFont());var n=K(r.get("lineHeight"),Math.round(e*3/2));e&&t.push("line-height:"+n+"px");var a=r.get("textShadowColor"),o=r.get("textShadowBlur")||0,s=r.get("textShadowOffsetX")||0,l=r.get("textShadowOffsetY")||0;return a&&o&&t.push("text-shadow:"+s+"px "+l+"px "+o+"px "+a),M(["decoration","align"],function(u){var f=r.get(u);f&&t.push("text-"+u+":"+f)}),t.join(";")}function fM(r,t,e){var i=[],n=r.get("transitionDuration"),a=r.get("backgroundColor"),o=r.get("shadowBlur"),s=r.get("shadowColor"),l=r.get("shadowOffsetX"),u=r.get("shadowOffsetY"),f=r.getModel("textStyle"),h=mg(r,"html"),c=l+"px "+u+"px "+o+"px "+s;return i.push("box-shadow:"+c),t&&n&&i.push(lM(n,e)),a&&i.push("background-color:"+a),M(["width","color","radius"],function(v){var d="border-"+v,g=Xp(d),p=r.get(g);p!=null&&i.push(d+":"+p+(v==="color"?"":"px"))}),i.push(uM(f)),h!=null&&i.push("padding:"+rs(h).join("px ")+"px"),i.join(";")+";"}function od(r,t,e,i,n){var a=t&&t.painter;if(e){var o=a&&a.getViewportRoot();o&&Bm(r,o,e,i,n)}else{r[0]=i,r[1]=n;var s=a&&a.getViewportRootOffset();s&&(r[0]+=s.offsetLeft,r[1]+=s.offsetTop)}r[2]=r[0]/t.getWidth(),r[3]=r[1]/t.getHeight()}var hM=function(){function r(t,e){if(this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._alwaysShowContent=!1,this._firstShow=!0,this._longHide=!0,$.wxa)return null;var i=document.createElement("div");i.domBelongToZr=!0,this.el=i;var n=this._zr=t.getZr(),a=e.appendTo,o=a&&(G(a)?document.querySelector(a):Nn(a)?a:q(a)&&a(t.getDom()));od(this._styleCoord,n,o,t.getWidth()/2,t.getHeight()/2),(o||t.getDom()).appendChild(i),this._api=t,this._container=o;var s=this;i.onmouseenter=function(){s._enterable&&(clearTimeout(s._hideTimeout),s._show=!0),s._inContent=!0},i.onmousemove=function(l){if(l=l||window.event,!s._enterable){var u=n.handler,f=n.painter.getViewportRoot();re(f,l,!0),u.dispatch("mousemove",l)}},i.onmouseleave=function(){s._inContent=!1,s._enterable&&s._show&&s.hideLater(s._hideDelay)}}return r.prototype.update=function(t){if(!this._container){var e=this._api.getDom(),i=iM(e,"position"),n=e.style;n.position!=="absolute"&&i!=="absolute"&&(n.position="relative")}var a=t.get("alwaysShowContent");a&&this._moveIfResized(),this._alwaysShowContent=a,this.el.className=t.get("className")||""},r.prototype.show=function(t,e){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var i=this.el,n=i.style,a=this._styleCoord;i.innerHTML?n.cssText=aM+fM(t,!this._firstShow,this._longHide)+ad(a[0],a[1],!0)+("border-color:"+ti(e)+";")+(t.get("extraCssText")||"")+(";pointer-events:"+(this._enterable?"auto":"none")):n.display="none",this._show=!0,this._firstShow=!1,this._longHide=!1},r.prototype.setContent=function(t,e,i,n,a){var o=this.el;if(t==null){o.innerHTML="";return}var s="";if(G(a)&&i.get("trigger")==="item"&&!Gy(i)&&(s=sM(i,n,a)),G(t))o.innerHTML=t+s;else if(t){o.innerHTML="",F(t)||(t=[t]);for(var l=0;l<t.length;l++)Nn(t[l])&&t[l].parentNode!==o&&o.appendChild(t[l]);if(s&&o.childNodes.length){var u=document.createElement("div");u.innerHTML=s,o.appendChild(u)}}},r.prototype.setEnterable=function(t){this._enterable=t},r.prototype.getSize=function(){var t=this.el;return t?[t.offsetWidth,t.offsetHeight]:[0,0]},r.prototype.moveTo=function(t,e){if(this.el){var i=this._styleCoord;if(od(i,this._zr,this._container,t,e),i[0]!=null&&i[1]!=null){var n=this.el.style,a=ad(i[0],i[1]);M(a,function(o){n[o[0]]=o[1]})}}},r.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},r.prototype.hide=function(){var t=this,e=this.el.style;e.visibility="hidden",e.opacity="0",$.transform3dSupported&&(e.willChange=""),this._show=!1,this._longHideTimeout=setTimeout(function(){return t._longHide=!0},500)},r.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(ht(this.hide,this),t)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var t=this.el.parentNode;t&&t.removeChild(this.el),this.el=this._container=null},r}(),vM=function(){function r(t){this._show=!1,this._styleCoord=[0,0,0,0],this._alwaysShowContent=!1,this._enterable=!0,this._zr=t.getZr(),ld(this._styleCoord,this._zr,t.getWidth()/2,t.getHeight()/2)}return r.prototype.update=function(t){var e=t.get("alwaysShowContent");e&&this._moveIfResized(),this._alwaysShowContent=e},r.prototype.show=function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.show(),this._show=!0},r.prototype.setContent=function(t,e,i,n,a){var o=this;W(t)&&Wt(""),this.el&&this._zr.remove(this.el);var s=i.getModel("textStyle");this.el=new Lt({style:{rich:e.richTextStyles,text:t,lineHeight:22,borderWidth:1,borderColor:n,textShadowColor:s.get("textShadowColor"),fill:i.get(["textStyle","color"]),padding:mg(i,"richText"),verticalAlign:"top",align:"left"},z:i.get("z")}),M(["backgroundColor","borderRadius","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"],function(u){o.el.style[u]=i.get(u)}),M(["textShadowBlur","textShadowOffsetX","textShadowOffsetY"],function(u){o.el.style[u]=s.get(u)||0}),this._zr.add(this.el);var l=this;this.el.on("mouseover",function(){l._enterable&&(clearTimeout(l._hideTimeout),l._show=!0),l._inContent=!0}),this.el.on("mouseout",function(){l._enterable&&l._show&&l.hideLater(l._hideDelay),l._inContent=!1})},r.prototype.setEnterable=function(t){this._enterable=t},r.prototype.getSize=function(){var t=this.el,e=this.el.getBoundingRect(),i=sd(t.style);return[e.width+i.left+i.right,e.height+i.top+i.bottom]},r.prototype.moveTo=function(t,e){var i=this.el;if(i){var n=this._styleCoord;ld(n,this._zr,t,e),t=n[0],e=n[1];var a=i.style,o=or(a.borderWidth||0),s=sd(a);i.x=t+o+s.left,i.y=e+o+s.top,i.markRedraw()}},r.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},r.prototype.hide=function(){this.el&&this.el.hide(),this._show=!1},r.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(ht(this.hide,this),t)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){this._zr.remove(this.el)},r}();function or(r){return Math.max(0,r)}function sd(r){var t=or(r.shadowBlur||0),e=or(r.shadowOffsetX||0),i=or(r.shadowOffsetY||0);return{left:or(t-e),right:or(t+e),top:or(t-i),bottom:or(t+i)}}function ld(r,t,e,i){r[0]=e,r[1]=i,r[2]=r[0]/t.getWidth(),r[3]=r[1]/t.getHeight()}var cM=new xt({shape:{x:-1,y:-1,width:2,height:2}}),dM=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.init=function(e,i){if(!($.node||!i.getDom())){var n=e.getComponent("tooltip"),a=this._renderMode=l_(n.get("renderMode"));this._tooltipContent=a==="richText"?new vM(i):new hM(i,{appendTo:n.get("appendToBody",!0)?"body":n.get("appendTo",!0)})}},t.prototype.render=function(e,i,n){if(!($.node||!n.getDom())){this.group.removeAll(),this._tooltipModel=e,this._ecModel=i,this._api=n;var a=this._tooltipContent;a.update(e),a.setEnterable(e.get("enterable")),this._initGlobalListener(),this._keepShow(),this._renderMode!=="richText"&&e.get("transitionDuration")?wg(this,"_updatePosition",50,"fixRate"):pu(this,"_updatePosition")}},t.prototype._initGlobalListener=function(){var e=this._tooltipModel,i=e.get("triggerOn");Fy("itemTooltip",this._api,ht(function(n,a,o){i!=="none"&&(i.indexOf(n)>=0?this._tryShow(a,o):n==="leave"&&this._hide(o))},this))},t.prototype._keepShow=function(){var e=this._tooltipModel,i=this._ecModel,n=this._api,a=e.get("triggerOn");if(this._lastX!=null&&this._lastY!=null&&a!=="none"&&a!=="click"){var o=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){!n.isDisposed()&&o.manuallyShowTip(e,i,n,{x:o._lastX,y:o._lastY,dataByCoordSys:o._lastDataByCoordSys})})}},t.prototype.manuallyShowTip=function(e,i,n,a){if(!(a.from===this.uid||$.node||!n.getDom())){var o=ud(a,n);this._ticket="";var s=a.dataByCoordSys,l=mM(a,i,n);if(l){var u=l.el.getBoundingRect().clone();u.applyTransform(l.el.transform),this._tryShow({offsetX:u.x+u.width/2,offsetY:u.y+u.height/2,target:l.el,position:a.position,positionDefault:"bottom"},o)}else if(a.tooltip&&a.x!=null&&a.y!=null){var f=cM;f.x=a.x,f.y=a.y,f.update(),st(f).tooltipConfig={name:null,option:a.tooltip},this._tryShow({offsetX:a.x,offsetY:a.y,target:f},o)}else if(s)this._tryShow({offsetX:a.x,offsetY:a.y,position:a.position,dataByCoordSys:s,tooltipOption:a.tooltipOption},o);else if(a.seriesIndex!=null){if(this._manuallyAxisShowTip(e,i,n,a))return;var h=zy(a,i),c=h.point[0],v=h.point[1];c!=null&&v!=null&&this._tryShow({offsetX:c,offsetY:v,target:h.el,position:a.position,positionDefault:"bottom"},o)}else a.x!=null&&a.y!=null&&(n.dispatchAction({type:"updateAxisPointer",x:a.x,y:a.y}),this._tryShow({offsetX:a.x,offsetY:a.y,position:a.position,target:n.getZr().findHover(a.x,a.y).target},o))}},t.prototype.manuallyHideTip=function(e,i,n,a){var o=this._tooltipContent;this._tooltipModel&&o.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=this._lastDataByCoordSys=null,a.from!==this.uid&&this._hide(ud(a,n))},t.prototype._manuallyAxisShowTip=function(e,i,n,a){var o=a.seriesIndex,s=a.dataIndex,l=i.getComponent("axisPointer").coordSysAxesInfo;if(!(o==null||s==null||l==null)){var u=i.getSeriesByIndex(o);if(u){var f=u.getData(),h=vn([f.getItemModel(s),u,(u.coordinateSystem||{}).model],this._tooltipModel);if(h.get("trigger")==="axis")return n.dispatchAction({type:"updateAxisPointer",seriesIndex:o,dataIndex:s,position:a.position}),!0}}},t.prototype._tryShow=function(e,i){var n=e.target,a=this._tooltipModel;if(a){this._lastX=e.offsetX,this._lastY=e.offsetY;var o=e.dataByCoordSys;if(o&&o.length)this._showAxisTooltip(o,e);else if(n){var s=st(n);if(s.ssrType==="legend")return;this._lastDataByCoordSys=null;var l,u;wn(n,function(f){if(st(f).dataIndex!=null)return l=f,!0;if(st(f).tooltipConfig!=null)return u=f,!0},!0),l?this._showSeriesItemTooltip(e,l,i):u?this._showComponentItemTooltip(e,u,i):this._hide(i)}else this._lastDataByCoordSys=null,this._hide(i)}},t.prototype._showOrMove=function(e,i){var n=e.get("showDelay");i=ht(i,this),clearTimeout(this._showTimout),n>0?this._showTimout=setTimeout(i,n):i()},t.prototype._showAxisTooltip=function(e,i){var n=this._ecModel,a=this._tooltipModel,o=[i.offsetX,i.offsetY],s=vn([i.tooltipOption],a),l=this._renderMode,u=[],f=Xn("section",{blocks:[],noHeader:!0}),h=[],c=new ol;M(e,function(m){M(m.dataByAxis,function(_){var S=n.getComponent(_.axisDim+"Axis",_.axisIndex),b=_.value;if(!(!S||b==null)){var w=By(b,S.axis,n,_.seriesDataIndices,_.valueLabelOpt),x=Xn("section",{header:w,noHeader:!Le(w),sortBlocks:!0,blocks:[]});f.blocks.push(x),M(_.seriesDataIndices,function(C){var T=n.getSeriesByIndex(C.seriesIndex),D=C.dataIndexInside,A=T.getDataParams(D);if(!(A.dataIndex<0)){A.axisDim=_.axisDim,A.axisIndex=_.axisIndex,A.axisType=_.axisType,A.axisId=_.axisId,A.axisValue=kf(S.axis,{value:b}),A.axisValueLabel=w,A.marker=c.makeTooltipMarker("item",ti(A.color),l);var L=Pv(T.formatTooltip(D,!0,null)),P=L.frag;if(P){var I=vn([T],a).get("valueFormatter");x.blocks.push(I?O({valueFormatter:I},P):P)}L.text&&h.push(L.text),u.push(A)}})}})}),f.blocks.reverse(),h.reverse();var v=i.position,d=s.get("order"),g=Bv(f,c,l,d,n.get("useUTC"),s.get("textStyle"));g&&h.unshift(g);var p=l==="richText"?`

`:"<br/>",y=h.join(p);this._showOrMove(s,function(){this._updateContentNotChangedOnAxis(e,u)?this._updatePosition(s,v,o[0],o[1],this._tooltipContent,u):this._showTooltipContent(s,y,u,Math.random()+"",o[0],o[1],v,null,c)})},t.prototype._showSeriesItemTooltip=function(e,i,n){var a=this._ecModel,o=st(i),s=o.seriesIndex,l=a.getSeriesByIndex(s),u=o.dataModel||l,f=o.dataIndex,h=o.dataType,c=u.getData(h),v=this._renderMode,d=e.positionDefault,g=vn([c.getItemModel(f),u,l&&(l.coordinateSystem||{}).model],this._tooltipModel,d?{position:d}:null),p=g.get("trigger");if(!(p!=null&&p!=="item")){var y=u.getDataParams(f,h),m=new ol;y.marker=m.makeTooltipMarker("item",ti(y.color),v);var _=Pv(u.formatTooltip(f,!1,h)),S=g.get("order"),b=g.get("valueFormatter"),w=_.frag,x=w?Bv(b?O({valueFormatter:b},w):w,m,v,S,a.get("useUTC"),g.get("textStyle")):_.text,C="item_"+u.name+"_"+f;this._showOrMove(g,function(){this._showTooltipContent(g,x,y,C,e.offsetX,e.offsetY,e.position,e.target,m)}),n({type:"showTip",dataIndexInside:f,dataIndex:c.getRawIndex(f),seriesIndex:s,from:this.uid})}},t.prototype._showComponentItemTooltip=function(e,i,n){var a=this._renderMode==="html",o=st(i),s=o.tooltipConfig,l=s.option||{},u=l.encodeHTMLContent;if(G(l)){var f=l;l={content:f,formatter:f},u=!0}u&&a&&l.content&&(l=j(l),l.content=Gt(l.content));var h=[l],c=this._ecModel.getComponent(o.componentMainType,o.componentIndex);c&&h.push(c),h.push({formatter:l.content});var v=e.positionDefault,d=vn(h,this._tooltipModel,v?{position:v}:null),g=d.get("content"),p=Math.random()+"",y=new ol;this._showOrMove(d,function(){var m=j(d.get("formatterParams")||{});this._showTooltipContent(d,g,m,p,e.offsetX,e.offsetY,e.position,i,y)}),n({type:"showTip",from:this.uid})},t.prototype._showTooltipContent=function(e,i,n,a,o,s,l,u,f){if(this._ticket="",!(!e.get("showContent")||!e.get("show"))){var h=this._tooltipContent;h.setEnterable(e.get("enterable"));var c=e.get("formatter");l=l||e.get("position");var v=i,d=this._getNearestPoint([o,s],n,e.get("trigger"),e.get("borderColor")),g=d.color;if(c)if(G(c)){var p=e.ecModel.get("useUTC"),y=F(n)?n[0]:n,m=y&&y.axisType&&y.axisType.indexOf("time")>=0;v=c,m&&(v=Qo(y.axisValue,v,p)),v=$p(v,n,!0)}else if(q(c)){var _=ht(function(S,b){S===this._ticket&&(h.setContent(b,f,e,g,l),this._updatePosition(e,l,o,s,h,n,u))},this);this._ticket=a,v=c(n,a,_)}else v=c;h.setContent(v,f,e,g,l),h.show(e,g),this._updatePosition(e,l,o,s,h,n,u)}},t.prototype._getNearestPoint=function(e,i,n,a){if(n==="axis"||F(i))return{color:a||(this._renderMode==="html"?"#fff":"none")};if(!F(i))return{color:a||i.color||i.borderColor}},t.prototype._updatePosition=function(e,i,n,a,o,s,l){var u=this._api.getWidth(),f=this._api.getHeight();i=i||e.get("position");var h=o.getSize(),c=e.get("align"),v=e.get("verticalAlign"),d=l&&l.getBoundingRect().clone();if(l&&d.applyTransform(l.transform),q(i)&&(i=i([n,a],s,o.el,d,{viewSize:[u,f],contentSize:h.slice()})),F(i))n=ct(i[0],u),a=ct(i[1],f);else if(W(i)){var g=i;g.width=h[0],g.height=h[1];var p=Oi(g,{width:u,height:f});n=p.x,a=p.y,c=null,v=null}else if(G(i)&&l){var y=yM(i,d,h,e.get("borderWidth"));n=y[0],a=y[1]}else{var y=pM(n,a,o,u,f,c?null:20,v?null:20);n=y[0],a=y[1]}if(c&&(n-=fd(c)?h[0]/2:c==="right"?h[0]:0),v&&(a-=fd(v)?h[1]/2:v==="bottom"?h[1]:0),Gy(e)){var y=gM(n,a,o,u,f);n=y[0],a=y[1]}o.moveTo(n,a)},t.prototype._updateContentNotChangedOnAxis=function(e,i){var n=this._lastDataByCoordSys,a=this._cbParamsList,o=!!n&&n.length===e.length;return o&&M(n,function(s,l){var u=s.dataByAxis||[],f=e[l]||{},h=f.dataByAxis||[];o=o&&u.length===h.length,o&&M(u,function(c,v){var d=h[v]||{},g=c.seriesDataIndices||[],p=d.seriesDataIndices||[];o=o&&c.value===d.value&&c.axisType===d.axisType&&c.axisId===d.axisId&&g.length===p.length,o&&M(g,function(y,m){var _=p[m];o=o&&y.seriesIndex===_.seriesIndex&&y.dataIndex===_.dataIndex}),a&&M(c.seriesDataIndices,function(y){var m=y.seriesIndex,_=i[m],S=a[m];_&&S&&S.data!==_.data&&(o=!1)})})}),this._lastDataByCoordSys=e,this._cbParamsList=i,!!o},t.prototype._hide=function(e){this._lastDataByCoordSys=null,e({type:"hideTip",from:this.uid})},t.prototype.dispose=function(e,i){$.node||!i.getDom()||(pu(this,"_updatePosition"),this._tooltipContent.dispose(),Ru("itemTooltip",i))},t.type="tooltip",t}(_e);function vn(r,t,e){var i=t.ecModel,n;e?(n=new gt(e,i,i),n=new gt(t.option,n,i)):n=t;for(var a=r.length-1;a>=0;a--){var o=r[a];o&&(o instanceof gt&&(o=o.get("tooltip",!0)),G(o)&&(o={formatter:o}),o&&(n=new gt(o,n,i)))}return n}function ud(r,t){return r.dispatchAction||ht(t.dispatchAction,t)}function pM(r,t,e,i,n,a,o){var s=e.getSize(),l=s[0],u=s[1];return a!=null&&(r+l+a+2>i?r-=l+a:r+=a),o!=null&&(t+u+o>n?t-=u+o:t+=o),[r,t]}function gM(r,t,e,i,n){var a=e.getSize(),o=a[0],s=a[1];return r=Math.min(r+o,i)-o,t=Math.min(t+s,n)-s,r=Math.max(r,0),t=Math.max(t,0),[r,t]}function yM(r,t,e,i){var n=e[0],a=e[1],o=Math.ceil(Math.SQRT2*i)+8,s=0,l=0,u=t.width,f=t.height;switch(r){case"inside":s=t.x+u/2-n/2,l=t.y+f/2-a/2;break;case"top":s=t.x+u/2-n/2,l=t.y-a-o;break;case"bottom":s=t.x+u/2-n/2,l=t.y+f+o;break;case"left":s=t.x-n-o,l=t.y+f/2-a/2;break;case"right":s=t.x+u+o,l=t.y+f/2-a/2}return[s,l]}function fd(r){return r==="center"||r==="middle"}function mM(r,t,e){var i=Ju(r).queryOptionMap,n=i.keys()[0];if(!(!n||n==="series")){var a=ea(t,n,i.get(n),{useDefault:!1,enableAll:!1,enableNone:!1}),o=a.models[0];if(o){var s=e.getViewOfComponentModel(o),l;if(s.group.traverse(function(u){var f=st(u).tooltipConfig;if(f&&f.name===r.name)return l=u,!0}),l)return{componentMainType:n,componentIndex:o.componentIndex,el:l}}}}function qM(r){ei(Hy),r.registerComponentModel(eM),r.registerComponentView(dM),r.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},Yt),r.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},Yt)}var _M=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.layoutMode={type:"box",ignoreSize:!0},e}return t.type="title",t.defaultOption={z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bold",color:"#464646"},subtextStyle:{fontSize:12,color:"#6E7079"}},t}(ot),SM=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n){if(this.group.removeAll(),!!e.get("show")){var a=this.group,o=e.getModel("textStyle"),s=e.getModel("subtextStyle"),l=e.get("textAlign"),u=K(e.get("textBaseline"),e.get("textVerticalAlign")),f=new Lt({style:_r(o,{text:e.get("text"),fill:o.getTextColor()},{disableBox:!0}),z2:10}),h=f.getBoundingRect(),c=e.get("subtext"),v=new Lt({style:_r(s,{text:c,fill:s.getTextColor(),y:h.height+e.get("itemGap"),verticalAlign:"top"},{disableBox:!0}),z2:10}),d=e.get("link"),g=e.get("sublink"),p=e.get("triggerEvent",!0);f.silent=!d&&!p,v.silent=!g&&!p,d&&f.on("click",function(){cv(d,"_"+e.get("target"))}),g&&v.on("click",function(){cv(g,"_"+e.get("subtarget"))}),st(f).eventData=st(v).eventData=p?{componentType:"title",componentIndex:e.componentIndex}:null,a.add(f),c&&a.add(v);var y=a.getBoundingRect(),m=e.getBoxLayoutParams();m.width=y.width,m.height=y.height;var _=Oi(m,{width:n.getWidth(),height:n.getHeight()},e.get("padding"));l||(l=e.get("left")||e.get("right"),l==="middle"&&(l="center"),l==="right"?_.x+=_.width:l==="center"&&(_.x+=_.width/2)),u||(u=e.get("top")||e.get("bottom"),u==="center"&&(u="middle"),u==="bottom"?_.y+=_.height:u==="middle"&&(_.y+=_.height/2),u=u||"top"),a.x=_.x,a.y=_.y,a.markRedraw();var S={align:l,verticalAlign:u};f.setStyle(S),v.setStyle(S),y=a.getBoundingRect();var b=_.margin,w=e.getItemStyle(["color","opacity"]);w.fill=e.get("backgroundColor");var x=new xt({shape:{x:y.x-b[3],y:y.y-b[0],width:y.width+b[1]+b[3],height:y.height+b[0]+b[2],r:e.get("borderRadius")},style:w,subPixelOptimize:!0,silent:!0});a.add(x)}},t.type="title",t}(_e);function KM(r){r.registerComponentModel(_M),r.registerComponentView(SM)}var wM=function(r,t){if(t==="all")return{type:"all",title:r.getLocaleModel().get(["legend","selector","all"])};if(t==="inverse")return{type:"inverse",title:r.getLocaleModel().get(["legend","selector","inverse"])}},Eu=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.layoutMode={type:"box",ignoreSize:!0},e}return t.prototype.init=function(e,i,n){this.mergeDefaultAndTheme(e,n),e.selected=e.selected||{},this._updateSelector(e)},t.prototype.mergeOption=function(e,i){r.prototype.mergeOption.call(this,e,i),this._updateSelector(e)},t.prototype._updateSelector=function(e){var i=e.selector,n=this.ecModel;i===!0&&(i=e.selector=["all","inverse"]),F(i)&&M(i,function(a,o){G(a)&&(a={type:a}),i[o]=it(a,wM(n,a.type))})},t.prototype.optionUpdated=function(){this._updateData(this.ecModel);var e=this._data;if(e[0]&&this.get("selectedMode")==="single"){for(var i=!1,n=0;n<e.length;n++){var a=e[n].get("name");if(this.isSelected(a)){this.select(a),i=!0;break}}!i&&this.select(e[0].get("name"))}},t.prototype._updateData=function(e){var i=[],n=[];e.eachRawSeries(function(l){var u=l.name;n.push(u);var f;if(l.legendVisualProvider){var h=l.legendVisualProvider,c=h.getAllNames();e.isSeriesFiltered(l)||(n=n.concat(c)),c.length?i=i.concat(c):f=!0}else f=!0;f&&Qu(l)&&i.push(l.name)}),this._availableNames=n;var a=this.get("data")||i,o=Q(),s=V(a,function(l){return(G(l)||vt(l))&&(l={name:l}),o.get(l.name)?null:(o.set(l.name,!0),new gt(l,this,this.ecModel))},this);this._data=Mt(s,function(l){return!!l})},t.prototype.getData=function(){return this._data},t.prototype.select=function(e){var i=this.option.selected,n=this.get("selectedMode");if(n==="single"){var a=this._data;M(a,function(o){i[o.get("name")]=!1})}i[e]=!0},t.prototype.unSelect=function(e){this.get("selectedMode")!=="single"&&(this.option.selected[e]=!1)},t.prototype.toggleSelected=function(e){var i=this.option.selected;i.hasOwnProperty(e)||(i[e]=!0),this[i[e]?"unSelect":"select"](e)},t.prototype.allSelect=function(){var e=this._data,i=this.option.selected;M(e,function(n){i[n.get("name",!0)]=!0})},t.prototype.inverseSelect=function(){var e=this._data,i=this.option.selected;M(e,function(n){var a=n.get("name",!0);i.hasOwnProperty(a)||(i[a]=!0),i[a]=!i[a]})},t.prototype.isSelected=function(e){var i=this.option.selected;return!(i.hasOwnProperty(e)&&!i[e])&&lt(this._availableNames,e)>=0},t.prototype.getOrient=function(){return this.get("orient")==="vertical"?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},t.type="legend.plain",t.dependencies=["series"],t.defaultOption={z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,symbolRotate:"inherit",symbolKeepAspect:!0,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",inactiveBorderWidth:"auto",itemStyle:{color:"inherit",opacity:"inherit",borderColor:"inherit",borderWidth:"auto",borderCap:"inherit",borderJoin:"inherit",borderDashOffset:"inherit",borderMiterLimit:"inherit"},lineStyle:{width:"auto",color:"inherit",inactiveColor:"#ccc",inactiveWidth:2,opacity:"inherit",type:"inherit",cap:"inherit",join:"inherit",dashOffset:"inherit",miterLimit:"inherit"},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:"sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}},t}(ot),mi=St,Ou=M,Va=Ot,Yy=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.newlineDisabled=!1,e}return t.prototype.init=function(){this.group.add(this._contentGroup=new Va),this.group.add(this._selectorGroup=new Va),this._isFirstRender=!0},t.prototype.getContentGroup=function(){return this._contentGroup},t.prototype.getSelectorGroup=function(){return this._selectorGroup},t.prototype.render=function(e,i,n){var a=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),!!e.get("show",!0)){var o=e.get("align"),s=e.get("orient");(!o||o==="auto")&&(o=e.get("left")==="right"&&s==="vertical"?"right":"left");var l=e.get("selector",!0),u=e.get("selectorPosition",!0);l&&(!u||u==="auto")&&(u=s==="horizontal"?"end":"start"),this.renderInner(o,e,i,n,l,s,u);var f=e.getBoxLayoutParams(),h={width:n.getWidth(),height:n.getHeight()},c=e.get("padding"),v=Oi(f,h,c),d=this.layoutInner(e,o,v,a,l,u),g=Oi(at({width:d.width,height:d.height},f),h,c);this.group.x=g.x-d.x,this.group.y=g.y-d.y,this.group.markRedraw(),this.group.add(this._backgroundEl=tM(d,e))}},t.prototype.resetInner=function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},t.prototype.renderInner=function(e,i,n,a,o,s,l){var u=this.getContentGroup(),f=Q(),h=i.get("selectedMode"),c=[];n.eachRawSeries(function(v){!v.get("legendHoverLink")&&c.push(v.id)}),Ou(i.getData(),function(v,d){var g=v.get("name");if(!this.newlineDisabled&&(g===""||g===`
`)){var p=new Va;p.newline=!0,u.add(p);return}var y=n.getSeriesByName(g)[0];if(!f.get(g))if(y){var m=y.getData(),_=m.getVisual("legendLineStyle")||{},S=m.getVisual("legendIcon"),b=m.getVisual("style"),w=this._createItem(y,g,d,v,i,e,_,b,S,h,a);w.on("click",mi(hd,g,null,a,c)).on("mouseover",mi(ku,y.name,null,a,c)).on("mouseout",mi(Bu,y.name,null,a,c)),n.ssr&&w.eachChild(function(x){var C=st(x);C.seriesIndex=y.seriesIndex,C.dataIndex=d,C.ssrType="legend"}),f.set(g,!0)}else n.eachRawSeries(function(x){if(!f.get(g)&&x.legendVisualProvider){var C=x.legendVisualProvider;if(!C.containName(g))return;var T=C.indexOfName(g),D=C.getItemVisual(T,"style"),A=C.getItemVisual(T,"legendIcon"),L=We(D.fill);L&&L[3]===0&&(L[3]=.2,D=O(O({},D),{fill:Bo(L,"rgba")}));var P=this._createItem(x,g,d,v,i,e,{},D,A,h,a);P.on("click",mi(hd,null,g,a,c)).on("mouseover",mi(ku,null,g,a,c)).on("mouseout",mi(Bu,null,g,a,c)),n.ssr&&P.eachChild(function(I){var R=st(I);R.seriesIndex=x.seriesIndex,R.dataIndex=d,R.ssrType="legend"}),f.set(g,!0)}},this)},this),o&&this._createSelector(o,i,a,s,l)},t.prototype._createSelector=function(e,i,n,a,o){var s=this.getSelectorGroup();Ou(e,function(u){var f=u.type,h=new Lt({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){n.dispatchAction({type:f==="all"?"legendAllSelect":"legendInverseSelect",legendId:i.id})}});s.add(h);var c=i.getModel("selectorLabel"),v=i.getModel(["emphasis","selectorLabel"]);Zo(h,{normal:c,emphasis:v},{defaultText:u.title}),iu(h)})},t.prototype._createItem=function(e,i,n,a,o,s,l,u,f,h,c){var v=e.visualDrawType,d=o.get("itemWidth"),g=o.get("itemHeight"),p=o.isSelected(i),y=a.get("symbolRotate"),m=a.get("symbolKeepAspect"),_=a.get("icon");f=_||f||"roundRect";var S=bM(f,a,l,u,v,p,c),b=new Va,w=a.getModel("textStyle");if(q(e.getLegendIcon)&&(!_||_==="inherit"))b.add(e.getLegendIcon({itemWidth:d,itemHeight:g,icon:f,iconRotate:y,itemStyle:S.itemStyle,lineStyle:S.lineStyle,symbolKeepAspect:m}));else{var x=_==="inherit"&&e.getData().getVisual("symbol")?y==="inherit"?e.getData().getVisual("symbolRotate"):y:0;b.add(xM({itemWidth:d,itemHeight:g,icon:f,iconRotate:x,itemStyle:S.itemStyle,symbolKeepAspect:m}))}var C=s==="left"?d+5:-5,T=s,D=o.get("formatter"),A=i;G(D)&&D?A=D.replace("{name}",i??""):q(D)&&(A=D(i));var L=p?w.getTextColor():a.get("inactiveColor");b.add(new Lt({style:_r(w,{text:A,x:C,y:g/2,fill:L,align:T,verticalAlign:"middle"},{inheritColor:L})}));var P=new xt({shape:b.getBoundingRect(),style:{fill:"transparent"}}),I=a.getModel("tooltip");return I.get("show")&&Xo({el:P,componentModel:o,itemName:i,itemTooltipOption:I.option}),b.add(P),b.eachChild(function(R){R.silent=!0}),P.silent=!h,this.getContentGroup().add(b),iu(b),b.__legendDataIndex=n,b},t.prototype.layoutInner=function(e,i,n,a,o,s){var l=this.getContentGroup(),u=this.getSelectorGroup();Rn(e.get("orient"),l,e.get("itemGap"),n.width,n.height);var f=l.getBoundingRect(),h=[-f.x,-f.y];if(u.markRedraw(),l.markRedraw(),o){Rn("horizontal",u,e.get("selectorItemGap",!0));var c=u.getBoundingRect(),v=[-c.x,-c.y],d=e.get("selectorButtonGap",!0),g=e.getOrient().index,p=g===0?"width":"height",y=g===0?"height":"width",m=g===0?"y":"x";s==="end"?v[g]+=f[p]+d:h[g]+=c[p]+d,v[1-g]+=f[y]/2-c[y]/2,u.x=v[0],u.y=v[1],l.x=h[0],l.y=h[1];var _={x:0,y:0};return _[p]=f[p]+d+c[p],_[y]=Math.max(f[y],c[y]),_[m]=Math.min(0,c[m]+v[1-g]),_}else return l.x=h[0],l.y=h[1],this.group.getBoundingRect()},t.prototype.remove=function(){this.getContentGroup().removeAll(),this._isFirstRender=!0},t.type="legend.plain",t}(_e);function bM(r,t,e,i,n,a,o){function s(p,y){p.lineWidth==="auto"&&(p.lineWidth=y.lineWidth>0?2:0),Ou(p,function(m,_){p[_]==="inherit"&&(p[_]=y[_])})}var l=t.getModel("itemStyle"),u=l.getItemStyle(),f=r.lastIndexOf("empty",0)===0?"fill":"stroke",h=l.getShallow("decal");u.decal=!h||h==="inherit"?i.decal:Su(h,o),u.fill==="inherit"&&(u.fill=i[n]),u.stroke==="inherit"&&(u.stroke=i[f]),u.opacity==="inherit"&&(u.opacity=(n==="fill"?i:e).opacity),s(u,i);var c=t.getModel("lineStyle"),v=c.getLineStyle();if(s(v,e),u.fill==="auto"&&(u.fill=i.fill),u.stroke==="auto"&&(u.stroke=i.fill),v.stroke==="auto"&&(v.stroke=i.fill),!a){var d=t.get("inactiveBorderWidth"),g=u[f];u.lineWidth=d==="auto"?i.lineWidth>0&&g?2:0:u.lineWidth,u.fill=t.get("inactiveColor"),u.stroke=t.get("inactiveBorderColor"),v.stroke=c.get("inactiveColor"),v.lineWidth=c.get("inactiveWidth")}return{itemStyle:u,lineStyle:v}}function xM(r){var t=r.icon||"roundRect",e=Ni(t,0,0,r.itemWidth,r.itemHeight,r.itemStyle.fill,r.symbolKeepAspect);return e.setStyle(r.itemStyle),e.rotation=(r.iconRotate||0)*Math.PI/180,e.setOrigin([r.itemWidth/2,r.itemHeight/2]),t.indexOf("empty")>-1&&(e.style.stroke=e.style.fill,e.style.fill="#fff",e.style.lineWidth=2),e}function hd(r,t,e,i){Bu(r,t,e,i),e.dispatchAction({type:"legendToggleSelect",name:r??t}),ku(r,t,e,i)}function Xy(r){for(var t=r.getZr().storage.getDisplayList(),e,i=0,n=t.length;i<n&&!(e=t[i].states.emphasis);)i++;return e&&e.hoverLayer}function ku(r,t,e,i){Xy(e)||e.dispatchAction({type:"highlight",seriesName:r,name:t,excludeSeriesId:i})}function Bu(r,t,e,i){Xy(e)||e.dispatchAction({type:"downplay",seriesName:r,name:t,excludeSeriesId:i})}function TM(r){var t=r.findComponents({mainType:"legend"});t&&t.length&&r.filterSeries(function(e){for(var i=0;i<t.length;i++)if(!t[i].isSelected(e.name))return!1;return!0})}function cn(r,t,e){var i=r==="allSelect"||r==="inverseSelect",n={},a=[];e.eachComponent({mainType:"legend",query:t},function(s){i?s[r]():s[r](t.name),vd(s,n),a.push(s.componentIndex)});var o={};return e.eachComponent("legend",function(s){M(n,function(l,u){s[l?"select":"unSelect"](u)}),vd(s,o)}),i?{selected:o,legendIndex:a}:{name:t.name,selected:o}}function vd(r,t){var e=t||{};return M(r.getData(),function(i){var n=i.get("name");if(!(n===`
`||n==="")){var a=r.isSelected(n);Qr(e,n)?e[n]=e[n]&&a:e[n]=a}}),e}function CM(r){r.registerAction("legendToggleSelect","legendselectchanged",St(cn,"toggleSelected")),r.registerAction("legendAllSelect","legendselectall",St(cn,"allSelect")),r.registerAction("legendInverseSelect","legendinverseselect",St(cn,"inverseSelect")),r.registerAction("legendSelect","legendselected",St(cn,"select")),r.registerAction("legendUnSelect","legendunselected",St(cn,"unSelect"))}function $y(r){r.registerComponentModel(Eu),r.registerComponentView(Yy),r.registerProcessor(r.PRIORITY.PROCESSOR.SERIES_FILTER,TM),r.registerSubTypeDefaulter("legend",function(){return"plain"}),CM(r)}var DM=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.setScrollDataIndex=function(e){this.option.scrollDataIndex=e},t.prototype.init=function(e,i,n){var a=is(e);r.prototype.init.call(this,e,i,n),cd(this,e,a)},t.prototype.mergeOption=function(e,i){r.prototype.mergeOption.call(this,e,i),cd(this,this.option,e)},t.type="legend.scroll",t.defaultOption=RS(Eu.defaultOption,{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800}),t}(Eu);function cd(r,t,e){var i=r.getOrient(),n=[1,1];n[i.index]=0,ki(t,e,{type:"box",ignoreSize:!!n})}var dd=Ot,Ll=["width","height"],Il=["x","y"],MM=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.newlineDisabled=!0,e._currentIndex=0,e}return t.prototype.init=function(){r.prototype.init.call(this),this.group.add(this._containerGroup=new dd),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new dd)},t.prototype.resetInner=function(){r.prototype.resetInner.call(this),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},t.prototype.renderInner=function(e,i,n,a,o,s,l){var u=this;r.prototype.renderInner.call(this,e,i,n,a,o,s,l);var f=this._controllerGroup,h=i.get("pageIconSize",!0),c=F(h)?h:[h,h];d("pagePrev",0);var v=i.getModel("pageTextStyle");f.add(new Lt({name:"pageText",style:{text:"xx/xx",fill:v.getTextColor(),font:v.getFont(),verticalAlign:"middle",align:"center"},silent:!0})),d("pageNext",1);function d(g,p){var y=g+"DataIndex",m=vf(i.get("pageIcons",!0)[i.getOrient().name][p],{onclick:ht(u._pageGo,u,y,i,a)},{x:-c[0]/2,y:-c[1]/2,width:c[0],height:c[1]});m.name=g,f.add(m)}},t.prototype.layoutInner=function(e,i,n,a,o,s){var l=this.getSelectorGroup(),u=e.getOrient().index,f=Ll[u],h=Il[u],c=Ll[1-u],v=Il[1-u];o&&Rn("horizontal",l,e.get("selectorItemGap",!0));var d=e.get("selectorButtonGap",!0),g=l.getBoundingRect(),p=[-g.x,-g.y],y=j(n);o&&(y[f]=n[f]-g[f]-d);var m=this._layoutContentAndController(e,a,y,u,f,c,v,h);if(o){if(s==="end")p[u]+=m[f]+d;else{var _=g[f]+d;p[u]-=_,m[h]-=_}m[f]+=g[f]+d,p[1-u]+=m[v]+m[c]/2-g[c]/2,m[c]=Math.max(m[c],g[c]),m[v]=Math.min(m[v],g[v]+p[1-u]),l.x=p[0],l.y=p[1],l.markRedraw()}return m},t.prototype._layoutContentAndController=function(e,i,n,a,o,s,l,u){var f=this.getContentGroup(),h=this._containerGroup,c=this._controllerGroup;Rn(e.get("orient"),f,e.get("itemGap"),a?n.width:null,a?null:n.height),Rn("horizontal",c,e.get("pageButtonItemGap",!0));var v=f.getBoundingRect(),d=c.getBoundingRect(),g=this._showController=v[o]>n[o],p=[-v.x,-v.y];i||(p[a]=f[u]);var y=[0,0],m=[-d.x,-d.y],_=K(e.get("pageButtonGap",!0),e.get("itemGap",!0));if(g){var S=e.get("pageButtonPosition",!0);S==="end"?m[a]+=n[o]-d[o]:y[a]+=d[o]+_}m[1-a]+=v[s]/2-d[s]/2,f.setPosition(p),h.setPosition(y),c.setPosition(m);var b={x:0,y:0};if(b[o]=g?n[o]:v[o],b[s]=Math.max(v[s],d[s]),b[l]=Math.min(0,d[l]+m[1-a]),h.__rectSize=n[o],g){var w={x:0,y:0};w[o]=Math.max(n[o]-d[o]-_,0),w[s]=b[s],h.setClipPath(new xt({shape:w})),h.__rectSize=w[o]}else c.eachChild(function(C){C.attr({invisible:!0,silent:!0})});var x=this._getPageInfo(e);return x.pageIndex!=null&&Re(f,{x:x.contentPosition[0],y:x.contentPosition[1]},g?e:null),this._updatePageInfoView(e,x),b},t.prototype._pageGo=function(e,i,n){var a=this._getPageInfo(i)[e];a!=null&&n.dispatchAction({type:"legendScroll",scrollDataIndex:a,legendId:i.id})},t.prototype._updatePageInfoView=function(e,i){var n=this._controllerGroup;M(["pagePrev","pageNext"],function(f){var h=f+"DataIndex",c=i[h]!=null,v=n.childOfName(f);v&&(v.setStyle("fill",c?e.get("pageIconColor",!0):e.get("pageIconInactiveColor",!0)),v.cursor=c?"pointer":"default")});var a=n.childOfName("pageText"),o=e.get("pageFormatter"),s=i.pageIndex,l=s!=null?s+1:0,u=i.pageCount;a&&o&&a.setStyle("text",G(o)?o.replace("{current}",l==null?"":l+"").replace("{total}",u==null?"":u+""):o({current:l,total:u}))},t.prototype._getPageInfo=function(e){var i=e.get("scrollDataIndex",!0),n=this.getContentGroup(),a=this._containerGroup.__rectSize,o=e.getOrient().index,s=Ll[o],l=Il[o],u=this._findTargetItemIndex(i),f=n.children(),h=f[u],c=f.length,v=c?1:0,d={contentPosition:[n.x,n.y],pageCount:v,pageIndex:v-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!h)return d;var g=S(h);d.contentPosition[o]=-g.s;for(var p=u+1,y=g,m=g,_=null;p<=c;++p)_=S(f[p]),(!_&&m.e>y.s+a||_&&!b(_,y.s))&&(m.i>y.i?y=m:y=_,y&&(d.pageNextDataIndex==null&&(d.pageNextDataIndex=y.i),++d.pageCount)),m=_;for(var p=u-1,y=g,m=g,_=null;p>=-1;--p)_=S(f[p]),(!_||!b(m,_.s))&&y.i<m.i&&(m=y,d.pagePrevDataIndex==null&&(d.pagePrevDataIndex=y.i),++d.pageCount,++d.pageIndex),y=_;return d;function S(w){if(w){var x=w.getBoundingRect(),C=x[l]+w[l];return{s:C,e:C+x[s],i:w.__legendDataIndex}}}function b(w,x){return w.e>=x&&w.s<=x+a}},t.prototype._findTargetItemIndex=function(e){if(!this._showController)return 0;var i,n=this.getContentGroup(),a;return n.eachChild(function(o,s){var l=o.__legendDataIndex;a==null&&l!=null&&(a=s),l===e&&(i=s)}),i??a},t.type="legend.scroll",t}(Yy);function AM(r){r.registerAction("legendScroll","legendscroll",function(t,e){var i=t.scrollDataIndex;i!=null&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(n){n.setScrollDataIndex(i)})})}function LM(r){ei($y),r.registerComponentModel(DM),r.registerComponentView(MM),AM(r)}function QM(r){ei($y),ei(LM)}const IM=["getWidth","getHeight","getDom","getOption","resize","dispatchAction","convertToPixel","convertFromPixel","containPixel","getDataURL","getConnectedDataURL","appendData","clear","isDisposed","dispose"];function PM(r){function t(i){return(...n)=>{if(!r.value)throw new Error("ECharts is not initialized yet.");return r.value[i].apply(r.value,n)}}function e(){const i=Object.create(null);return IM.forEach(n=>{i[n]=t(n)}),i}return e()}function RM(r,t,e){Ua([e,r,t],([i,n,a],o,s)=>{let l=null;if(i&&n&&a){const{offsetWidth:u,offsetHeight:f}=i,h=a===!0?{}:a,{throttle:c=100,onResize:v}=h;let d=!1;const g=()=>{n.resize(),v==null||v()},p=c?Mf(g,c):g;l=new ResizeObserver(()=>{!d&&(d=!0,i.offsetWidth===u&&i.offsetHeight===f)||p()}),l.observe(i)}s(()=>{l&&(l.disconnect(),l=null)})})}const EM={autoresize:[Boolean,Object]},OM=/^on[^a-z]/,Zy=r=>OM.test(r);function kM(r){const t={};for(const e in r)Zy(e)||(t[e]=r[e]);return t}function no(r,t){const e=am(r)?om(r):r;return e&&typeof e=="object"&&"value"in e?e.value||t:e||t}const BM="ecLoadingOptions";function NM(r,t,e){const i=Wa(BM,{}),n=_i(()=>({...no(i,{}),...e==null?void 0:e.value}));gd(()=>{const a=r.value;a&&(t.value?a.showLoading(n.value):a.hideLoading())})}const FM={loading:Boolean,loadingOptions:Object};let dn=null;const qy="x-vue-echarts";function zM(){if(dn!=null)return dn;if(typeof HTMLElement>"u"||typeof customElements>"u")return dn=!1;try{new Function("tag","class EChartsElement extends HTMLElement{__dispose=null;disconnectedCallback(){this.__dispose&&(this.__dispose(),this.__dispose=null)}}customElements.get(tag)==null&&customElements.define(tag,EChartsElement);")(qy)}catch{return dn=!1}return dn=!0}document.head.appendChild(document.createElement("style")).textContent=`x-vue-echarts{display:block;width:100%;height:100%;min-width:0}
`;const HM=zM(),GM="ecTheme",VM="ecInitOptions",WM="ecUpdateOptions",pd=/(^&?~?!?)native:/;var JM=Jy({name:"echarts",props:{option:Object,theme:{type:[Object,String]},initOptions:Object,updateOptions:Object,group:String,manualUpdate:Boolean,...EM,...FM},emits:{},inheritAttrs:!1,setup(r,{attrs:t}){const e=hs(),i=hs(),n=hs(),a=Wa(GM,null),o=Wa(VM,null),s=Wa(WM,null),{autoresize:l,manualUpdate:u,loading:f,loadingOptions:h}=tm(r),c=_i(()=>n.value||r.option||null),v=_i(()=>r.theme||no(a,{})),d=_i(()=>r.initOptions||no(o,{})),g=_i(()=>r.updateOptions||no(s,{})),p=_i(()=>kM(t)),y={},m=em().proxy.$listeners,_={};m?Object.keys(m).forEach(T=>{pd.test(T)?y[T.replace(pd,"$1")]=m[T]:_[T]=m[T]}):Object.keys(t).filter(T=>Zy(T)).forEach(T=>{let D=T.charAt(2).toLowerCase()+T.slice(3);if(D.indexOf("native:")===0){const A=`on${D.charAt(7).toUpperCase()}${D.slice(8)}`;y[A]=t[T];return}D.substring(D.length-4)==="Once"&&(D=`~${D.substring(0,D.length-4)}`),_[D]=t[T]});function S(T){if(!e.value)return;const D=i.value=Ax(e.value,v.value,d.value);r.group&&(D.group=r.group),Object.keys(_).forEach(P=>{let I=_[P];if(!I)return;let R=P.toLowerCase();R.charAt(0)==="~"&&(R=R.substring(1),I.__once__=!0);let E=D;if(R.indexOf("zr:")===0&&(E=D.getZr(),R=R.substring(3)),I.__once__){delete I.__once__;const z=I;I=(...B)=>{z(...B),E.off(R,I)}}E.on(R,I)});function A(){D&&!D.isDisposed()&&D.resize()}function L(){const P=T||c.value;P&&D.setOption(P,g.value)}l.value?nm(()=>{A(),L()}):L()}function b(T,D){r.manualUpdate&&(n.value=T),i.value?i.value.setOption(T,D||{}):S(T)}function w(){i.value&&(i.value.dispose(),i.value=void 0)}let x=null;Ua(u,T=>{typeof x=="function"&&(x(),x=null),T||(x=Ua(()=>r.option,(D,A)=>{D&&(i.value?i.value.setOption(D,{notMerge:D!==A,...g.value}):S())},{deep:!0}))},{immediate:!0}),Ua([v,d],()=>{w(),S()},{deep:!0}),gd(()=>{r.group&&i.value&&(i.value.group=r.group)});const C=PM(i);return NM(i,f,h),RM(i,l,e),rm(()=>{S()}),im(()=>{HM&&e.value?e.value.__dispose=w:w()}),{chart:i,root:e,setOption:b,nonEventAttrs:p,nativeListeners:y,...C}},render(){const r={...this.nonEventAttrs,...this.nativeListeners};return r.ref="root",r.class=r.class?["echarts"].concat(r.class):"echarts",jy(qy,r)}});export{JM as E,XM as a,$M as b,KM as c,qM as d,QM as e,ZM as f,YM as i,ei as u};
