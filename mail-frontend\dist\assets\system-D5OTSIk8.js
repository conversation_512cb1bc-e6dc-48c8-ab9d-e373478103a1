import{b as t}from"./index-BVF_NGWt.js";const r={getSystemStats:()=>t.get("/monitor/statistics"),getSystemStatsWithMode:(e="full")=>t.get("/monitor/statistics",{params:{mode:e}}),getHealthStatus:()=>t.get("/health"),getTasks:()=>t.get("/monitor/tasks"),enableTask:e=>t.post(`/monitor/tasks/${e}/enable`),disableTask:e=>t.post(`/monitor/tasks/${e}/disable`),runTask:e=>t.post(`/monitor/tasks/${e}/run`),getSystemConfig:()=>t.get("/config"),updateSystemConfig:e=>t.put("/config",e),resetSystemConfig:()=>t.post("/config/reset"),getSystemLogs:e=>t.get("/monitor/logs",{params:e}),cleanupSystem:e=>t.post("/system/cleanup",e),backupSystem:()=>t.post("/system/backup"),getVersionInfo:()=>t.get("/system/version")};export{r as s};
