import{u as V,a as H,g as O,c as R,_ as j}from"./index-BVF_NGWt.js";import{P as q}from"./Person-Cj8AC7xs.js";import{u as E,v as c,H as J,E as K,h as v,i as Q,j as _,k as P,B as g,O as N,w as W,J as X}from"./ui-B428bLoF.js";import{a as G,M as Y,L as Z}from"./Moon-V5Og8Ghg.js";import{k as S,W as C,Q as x,X as a,r as y,e as ss,S as o,R as l,K as s,$ as d,j as m}from"./vendor-RHijBMdK.js";const es={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},as=a("path",{d:"M478.33 433.6l-90-218a22 22 0 0 0-40.67 0l-90 218a22 22 0 1 0 40.67 16.79L316.66 406h102.67l18.33 44.39A22 22 0 0 0 458 464a22 22 0 0 0 20.32-30.4zM334.83 362L368 281.65L401.17 362z",fill:"currentColor"},null,-1),os=a("path",{d:"M267.84 342.92a22 22 0 0 0-4.89-30.7c-.2-.15-15-11.13-36.49-34.73c39.65-53.68 62.11-114.75 71.27-143.49H330a22 22 0 0 0 0-44H214V70a22 22 0 0 0-44 0v20H54a22 22 0 0 0 0 44h197.25c-9.52 26.95-27.05 69.5-53.79 108.36c-31.41-41.68-43.08-68.65-43.17-68.87a22 22 0 0 0-40.58 17c.58 1.38 14.55 34.23 52.86 83.93c.92 1.19 1.83 2.35 2.74 3.51c-39.24 44.35-77.74 71.86-93.85 80.74a22 22 0 1 0 21.07 38.63c2.16-1.18 48.6-26.89 101.63-85.59c22.52 24.08 38 35.44 38.93 36.1a22 22 0 0 0 30.75-4.9z",fill:"currentColor"},null,-1),ls=[as,os],ts=S({name:"Language",render:function(f,i){return x(),C("svg",es,ls)}}),ns={class:"profile"},rs={class:"profile-grid"},ds={class:"user-info"},is={class:"avatar-section"},us={class:"user-details"},cs={class:"last-login"},ps={class:"preference-item"},vs={class:"preference-label"},fs={class:"preference-item"},ws={class:"preference-label"},_s={class:"preference-item"},gs={class:"preference-label"},ms={class:"device-info"},hs={class:"device-item"},bs={class:"device-value"},Ps={class:"device-item"},ys={class:"device-value"},ks={class:"device-item"},Ls={class:"device-value"},Ns={class:"device-item"},Ss={class:"device-value"},Cs={class:"device-item"},xs={class:"device-value"},Is={class:"security-actions"},Ms={class:"system-info"},Us={class:"info-item"},Fs={class:"info-value"},$s={class:"info-item"},zs={class:"info-value"},Bs=S({__name:"Profile",setup(I){const f=E(),i=V(),u=H(),h=y(!1),p=y(!1),b=y(null),n=ss({oldPassword:"",newPassword:"",confirmPassword:""}),M={oldPassword:[{required:!0,message:"请输入当前密码",trigger:["input","blur"]}],newPassword:[{required:!0,message:"请输入新密码",trigger:["input","blur"]},{min:6,message:"密码长度不能少于6个字符",trigger:["input","blur"]}],confirmPassword:[{required:!0,message:"请确认新密码",trigger:["input","blur"]},{validator:(r,e)=>e===n.newPassword,message:"两次输入的密码不一致",trigger:["input","blur"]}]},U=[{label:"简体中文",value:"zh-CN"},{label:"English",value:"en-US"}],w=O(),F="1.0.0",$=new Date().toLocaleString(),z=r=>r?new Date(r).toLocaleString():"未知",B=async()=>{if(b.value)try{await b.value.validate(),h.value=!0,await R.changePassword({oldPassword:n.oldPassword,newPassword:n.newPassword}),f.success("密码修改成功"),Object.assign(n,{oldPassword:"",newPassword:"",confirmPassword:""})}catch(r){console.error("Failed to change password:",r),f.error("密码修改失败")}finally{h.value=!1}},T=r=>{u.setTheme(r?"dark":"light")},A=r=>{u.setLanguage(r),f.success("语言设置已更新")},D=()=>{i.logout(),p.value=!1};return(r,e)=>(x(),C("div",ns,[e[21]||(e[21]=a("div",{class:"page-header"},[a("h1",null,"个人设置"),a("p",null,"管理您的账户信息和偏好设置")],-1)),a("div",rs,[o(s(c),{title:"基本信息",class:"profile-card"},{default:l(()=>{var t,k,L;return[a("div",ds,[a("div",is,[o(s(K),{size:"large",style:{backgroundColor:"#18a058"}},{default:l(()=>[o(s(v),{size:"32"},{default:l(()=>[o(s(q))]),_:1})]),_:1}),a("div",us,[a("h3",null,d((t=s(i).user)==null?void 0:t.username),1),a("p",null,d(((k=s(i).user)==null?void 0:k.role)==="admin"?"系统管理员":"普通用户"),1),a("p",cs,"上次登录: "+d(z((L=s(i).user)==null?void 0:L.lastLoginTime)),1)])])])]}),_:1}),o(s(c),{title:"修改密码",class:"profile-card"},{default:l(()=>[o(s(Q),{ref_key:"passwordFormRef",ref:b,model:n,rules:M,"label-placement":"top"},{default:l(()=>[o(s(_),{label:"当前密码",path:"oldPassword"},{default:l(()=>[o(s(P),{value:n.oldPassword,"onUpdate:value":e[0]||(e[0]=t=>n.oldPassword=t),type:"password",placeholder:"请输入当前密码","show-password-on":"mousedown"},null,8,["value"])]),_:1}),o(s(_),{label:"新密码",path:"newPassword"},{default:l(()=>[o(s(P),{value:n.newPassword,"onUpdate:value":e[1]||(e[1]=t=>n.newPassword=t),type:"password",placeholder:"请输入新密码","show-password-on":"mousedown"},null,8,["value"])]),_:1}),o(s(_),{label:"确认新密码",path:"confirmPassword"},{default:l(()=>[o(s(P),{value:n.confirmPassword,"onUpdate:value":e[2]||(e[2]=t=>n.confirmPassword=t),type:"password",placeholder:"请再次输入新密码","show-password-on":"mousedown"},null,8,["value"])]),_:1}),o(s(_),null,{default:l(()=>[o(s(g),{type:"primary",loading:h.value,onClick:B,block:""},{default:l(()=>e[6]||(e[6]=[m(" 修改密码 ")])),_:1,__:[6]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1}),o(s(c),{title:"偏好设置",class:"profile-card"},{default:l(()=>[a("div",ps,[a("div",vs,[o(s(v),null,{default:l(()=>[o(s(G))]),_:1}),e[7]||(e[7]=a("span",null,"深色主题",-1))]),o(s(N),{value:s(u).isDark,"onUpdate:value":T},null,8,["value"])]),a("div",fs,[a("div",ws,[o(s(v),null,{default:l(()=>[o(s(ts))]),_:1}),e[8]||(e[8]=a("span",null,"语言设置",-1))]),o(s(W),{value:s(u).language,options:U,style:{width:"120px"},"onUpdate:value":A},null,8,["value"])]),a("div",_s,[a("div",gs,[o(s(v),null,{default:l(()=>[o(s(Y))]),_:1}),e[9]||(e[9]=a("span",null,"侧边栏折叠",-1))]),o(s(N),{value:s(u).sidebarCollapsed,"onUpdate:value":s(u).setSidebarCollapsed},null,8,["value","onUpdate:value"])])]),_:1}),o(s(c),{title:"设备信息",class:"profile-card"},{default:l(()=>[a("div",ms,[a("div",hs,[e[10]||(e[10]=a("span",{class:"device-label"},"设备指纹:",-1)),a("code",bs,d(s(i).deviceFingerprint),1)]),a("div",Ps,[e[11]||(e[11]=a("span",{class:"device-label"},"浏览器:",-1)),a("span",ys,d(s(w).browser),1)]),a("div",ks,[e[12]||(e[12]=a("span",{class:"device-label"},"平台:",-1)),a("span",Ls,d(s(w).platform),1)]),a("div",Ns,[e[13]||(e[13]=a("span",{class:"device-label"},"屏幕分辨率:",-1)),a("span",Ss,d(s(w).screen),1)]),a("div",Cs,[e[14]||(e[14]=a("span",{class:"device-label"},"语言:",-1)),a("span",xs,d(s(w).language),1)])])]),_:1}),o(s(c),{title:"安全设置",class:"profile-card"},{default:l(()=>[a("div",Is,[o(s(g),{type:"warning",onClick:e[3]||(e[3]=t=>p.value=!0),block:""},{icon:l(()=>[o(s(v),null,{default:l(()=>[o(s(Z))]),_:1})]),default:l(()=>[e[15]||(e[15]=m(" 退出登录 "))]),_:1,__:[15]})])]),_:1}),o(s(c),{title:"系统信息",class:"profile-card"},{default:l(()=>[a("div",Ms,[a("div",Us,[e[16]||(e[16]=a("span",{class:"info-label"},"前端版本:",-1)),a("span",Fs,d(s(F)),1)]),a("div",$s,[e[17]||(e[17]=a("span",{class:"info-label"},"构建时间:",-1)),a("span",zs,d(s($)),1)])])]),_:1})]),o(s(J),{show:p.value,"onUpdate:show":e[5]||(e[5]=t=>p.value=t),preset:"dialog",title:"退出登录"},{default:l(()=>e[18]||(e[18]=[a("p",null,"确定要退出登录吗？退出后需要重新输入用户名和密码。",-1)])),action:l(()=>[o(s(X),null,{default:l(()=>[o(s(g),{onClick:e[4]||(e[4]=t=>p.value=!1)},{default:l(()=>e[19]||(e[19]=[m("取消")])),_:1,__:[19]}),o(s(g),{type:"primary",onClick:D},{default:l(()=>e[20]||(e[20]=[m("确认退出")])),_:1,__:[20]})]),_:1})]),_:1},8,["show"])]))}}),Os=j(Bs,[["__scopeId","data-v-91c07813"]]);export{Os as default};
