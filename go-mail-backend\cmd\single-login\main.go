package main

import (
	"context"
	"fmt"
	"go-mail/internal/mail/client"
	"go-mail/internal/manager"
	"go-mail/internal/services/task_log"
	"go-mail/internal/types"
	"log"
	"net/url"
	"strconv"
	"time"
)

func main() {
	// 硬编码配置
	const (
		//*
		//  baltgipomelac 十个别名邮箱已经满了，一个账号加上自己只能创建九个，加自己就是十个。
		//  */
		/*

<EMAIL>----KxaT0FQ69dfi
<EMAIL>----xLLX6Jq4WHKl
<EMAIL>----ziliang0107
<EMAIL>----jSUmecVr57I5
<EMAIL>----Oy48YeU7W4Px

		*/
		username = "<EMAIL>"
		password = "xLLX6Jq4WHKl"
		proxyStr = "" // 设置为空字符串 "" 可禁用代理，http://127.0.0.1:7890
		timeout  = 30 * time.Second
		verbose  = true // 启用详细输出
	)

	fmt.Printf("=== 单账号登录测试 ===\n")
	fmt.Printf("用户名: %s\n", username)
	fmt.Printf("超时时间: %v\n", timeout)
	if proxyStr != "" {
		fmt.Printf("代理: %s\n", proxyStr)
	}
	fmt.Println()

	// 创建配置
	config := &types.ManagerConfig{
		MaxConcurrent:       1,
		RequestTimeout:      timeout,
		SessionTimeout:      24 * time.Hour,
		RetryAttempts:       3,
		RetryDelay:          2 * time.Second,
		ProxyRotation:       false,
		HealthCheckInterval: 5 * time.Minute,
		LogLevel:            "info",
		UserAgent:           "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
	}

	// 创建管理器
	mailManager := manager.NewMailManager(config)

	// 创建日志服务（用于详细登录日志）
	detailLogService := task_log.NewDetailLogService("")
	taskLogService := task_log.NewTaskLogService(nil, detailLogService) // 单独测试时不需要数据库
	mailManager.SetLogServices(taskLogService, detailLogService)

	// 启动管理器
	ctx := context.Background()
	if err := mailManager.Start(ctx); err != nil {
		log.Fatalf("启动管理器失败: %v", err)
	}
	defer func() {
		if err := mailManager.Stop(ctx); err != nil {
			log.Printf("停止管理器失败: %v", err)
		}
	}()

	// 配置代理（如果提供）
	if proxyStr != "" {
		proxyConfig, err := parseProxy(proxyStr)
		if err != nil {
			log.Fatalf("解析代理配置失败: %v", err)
		}

		if err := mailManager.SetProxyPool([]types.ProxyConfig{*proxyConfig}); err != nil {
			log.Fatalf("设置代理失败: %v", err)
		}

		// 启用代理轮换
		config.ProxyRotation = true
		mailManager.UpdateConfig(config)

		fmt.Printf("✓ 代理配置成功: %s://%s:%d\n", proxyConfig.Type, proxyConfig.Host, proxyConfig.Port)
	}

	// 创建账户
	account := types.Account{
		Username: username,
		Password: password,
		Status:   types.AccountStatusInactive,
		Metadata: map[string]string{
			"test_type": "single_login",
			"timestamp": time.Now().Format(time.RFC3339),
		},
	}

	// 添加账户
	if err := mailManager.AddAccount(account); err != nil {
		log.Fatalf("添加账户失败: %v", err)
	}
	fmt.Printf("✓ 账户添加成功\n")

	// 执行登录
	fmt.Printf("\n--- 开始登录 ---\n")
	startTime := time.Now()

	loginCtx, cancel := context.WithTimeout(ctx, timeout+10*time.Second)
	defer cancel()

	result, err := mailManager.BatchLogin(loginCtx, []types.Account{account})
	if err != nil {
		log.Fatalf("登录失败: %v", err)
	}

	duration := time.Since(startTime)
	fmt.Printf("--- 登录完成 (耗时: %v) ---\n\n", duration)

	// 显示结果
	fmt.Printf("=== 登录结果 ===\n")
	fmt.Printf("总数: %d\n", result.Total)
	fmt.Printf("成功: %d\n", result.Success)
	fmt.Printf("失败: %d\n", result.Failed)
	fmt.Printf("耗时: %v\n", result.Duration)
	fmt.Println()

	// 详细结果
	if loginResult, exists := result.Results[username]; exists {
		fmt.Printf("✓ 登录成功!\n")
		fmt.Printf("  会话ID: %s\n", loginResult.SessionID)
		fmt.Printf("  JSESSIONID: %s\n", loginResult.JSessionID)
		fmt.Printf("  Navigator SID: %s\n", loginResult.NavigatorSID)
		fmt.Printf("  重定向URL: %s\n", loginResult.RedirectURL)
		fmt.Printf("  登录时间: %v\n", loginResult.LoginTime)
		fmt.Printf("  消息: %s\n", loginResult.Message)

		if verbose {
			fmt.Printf("\n=== 详细信息 ===\n")

			// 获取统计信息
			stats := mailManager.GetStatistics()
			fmt.Printf("系统统计:\n")
			fmt.Printf("  总账户数: %d\n", stats.TotalAccounts)
			fmt.Printf("  活跃会话: %d\n", stats.ActiveSessions)
			fmt.Printf("  总登录次数: %d\n", stats.TotalLogins)
			fmt.Printf("  成功登录: %d\n", stats.SuccessfulLogins)
			fmt.Printf("  失败登录: %d\n", stats.FailedLogins)
			fmt.Printf("  平均登录时间: %v\n", stats.AverageLoginTime)

			// 获取账户状态
			if accountStatus, err := mailManager.GetAccountStatus(username); err == nil {
				fmt.Printf("账户状态: %s\n", *accountStatus)
			}

			// 健康检查
			if healthReport, err := mailManager.BatchHealthCheck(ctx); err == nil {
				fmt.Printf("健康检查: %s\n", healthReport.OverallStatus)
			}
		}

		// 测试会话保持
		fmt.Printf("\n--- 测试会话保持 ---\n")
		time.Sleep(2 * time.Second)

		// 再次获取统计信息验证会话仍然活跃
		stats := mailManager.GetStatistics()
		if stats.ActiveSessions > 0 {
			fmt.Printf("✓ 会话保持正常 (活跃会话: %d)\n", stats.ActiveSessions)
		} else {
			fmt.Printf("⚠ 会话可能已过期\n")
		}

	} else if err, exists := result.Errors[username]; exists {
		fmt.Printf("✗ 登录失败!\n")
		fmt.Printf("  错误: %v\n", err)

		// 尝试分析错误类型
		if verbose {
			fmt.Printf("  错误分析:\n")
			// 这里可以添加更详细的错误分析
			fmt.Printf("    - 请检查用户名和密码是否正确\n")
			fmt.Printf("    - 请检查网络连接是否正常\n")
			if proxyStr != "" {
				fmt.Printf("    - 请检查代理配置是否正确\n")
			}
		}
	}

	// 测试邮件功能
	if loginResult, exists := result.Results[username]; exists && loginResult.Success {
		fmt.Printf("\n--- 测试邮件功能 ---\n")

		// 获取会话信息
		session, err := mailManager.GetSessionByAccount(username)
		if err != nil {
			fmt.Printf("✗ 获取会话失败: %v\n", err)
		} else {
			// 创建邮件客户端
			mailClient := client.NewMailClient(mailManager.GetConfig())

			// 测试获取邮件列表
			fmt.Printf("正在获取邮件列表...\n")
			mailList, err := mailClient.GetMailList(ctx, session)
			if err != nil {
				fmt.Printf("✗ 获取邮件列表失败: %v\n", err)
			} else {
				fmt.Printf("✓ 成功获取邮件列表\n")
				fmt.Printf("  邮件数量: %d\n", len(mailList.Items))
				fmt.Printf("  当前页码: %d\n", mailList.Page)

				// 显示前几封邮件的信息
				for i, mail := range mailList.Items {
					if i >= 3 { // 只显示前3封邮件
						break
					}
					fmt.Printf("  邮件 %d:\n", i+1)
					fmt.Printf("    ID: %s\n", mail.MailID)
					fmt.Printf("    发件人: %s\n", mail.From)
					fmt.Printf("    主题: %s\n", mail.Subject)
					fmt.Printf("    日期: %s\n", mail.Date)
					fmt.Printf("    是否新邮件: %t\n", mail.IsNew)
				}

				// 如果有邮件，测试读取第一封邮件的内容
				if len(mailList.Items) > 0 {
					firstMail := mailList.Items[0]
					fmt.Printf("\n正在读取第一封邮件内容...\n")
					mailContent, err := mailClient.GetMailContentWithItem(ctx, session, firstMail.MailID, &firstMail)
					if err != nil {
						fmt.Printf("✗ 读取邮件内容失败: %v\n", err)
					} else {
						fmt.Printf("✓ 成功读取邮件内容\n")
						fmt.Printf("  邮件ID: %s\n", mailContent.MailID)
						fmt.Printf("  发件人: %s\n", mailContent.From)
						fmt.Printf("  收件人: %s\n", mailContent.To)
						fmt.Printf("  主题: %s\n", mailContent.Subject)
						fmt.Printf("  日期: %s\n", mailContent.Date)
						fmt.Printf("  正文长度: %d 字符\n", len(mailContent.Body))
						fmt.Printf("  纯文本长度: %d 字符\n", len(mailContent.TextBody))
						fmt.Printf("  附件数量: %d\n", len(mailContent.Attachments))

						// 显示正文的前100个字符
						if len(mailContent.TextBody) > 0 {
							preview := mailContent.TextBody
							if len(preview) > 100 {
								preview = preview[:100] + "..."
							}
							fmt.Printf("  正文预览: %s\n", preview)
						}
					}
				}
			}

			// 测试别名邮箱功能
			fmt.Printf("\n--- 测试别名邮箱功能 ---\n")
			testAliasEmails(ctx, mailClient, session)
		}
	}

	fmt.Printf("\n=== 测试完成 ===\n")
}

// testAliasEmails 测试别名邮箱功能
func testAliasEmails(ctx context.Context, mailClient *client.MailClient, session *types.Session) {
	// 1. 获取别名邮箱列表
	fmt.Printf("1. 正在获取别名邮箱列表...\n")
	aliasEmailList, err := mailClient.GetAliasEmails(ctx, session)
	if err != nil {
		fmt.Printf("✗ 获取别名邮箱列表失败: %v\n", err)
		return
	}

	fmt.Printf("✓ 成功获取别名邮箱列表\n")
	fmt.Printf("  别名邮箱数量: %d\n", len(aliasEmailList.Aliases))
	fmt.Printf("  可用域名数量: %d\n", len(aliasEmailList.DomainOptions))

	// 显示现有别名邮箱
	if len(aliasEmailList.Aliases) > 0 {
		fmt.Printf("  现有别名邮箱:\n")
		for i, alias := range aliasEmailList.Aliases {
			defaultStr := ""
			if alias.IsDefault {
				defaultStr = " (默认)"
			}
			fmt.Printf("    %d. %s%s (ID: %s)\n", i+1, alias.Email, defaultStr, alias.ID)
		}
	}

	// 显示部分可用域名
	if len(aliasEmailList.DomainOptions) > 0 {
		fmt.Printf("  部分可用域名:\n")
		count := 0
		for _, domain := range aliasEmailList.DomainOptions {
			if count >= 5 { // 只显示前5个域名
				break
			}
			fmt.Printf("    %s (%s) - %s\n", domain.Domain, domain.Value, domain.Category)
			count++
		}
		if len(aliasEmailList.DomainOptions) > 5 {
			fmt.Printf("    ... 还有 %d 个域名\n", len(aliasEmailList.DomainOptions)-5)
		}
	}

	// 2. 测试创建别名邮箱
	if len(aliasEmailList.DomainOptions) > 0 {
		fmt.Printf("\n2. 正在测试创建别名邮箱...\n")

		// 生成一个随机的邮箱前缀
		testPrefix := fmt.Sprintf("test%d", time.Now().Unix()%10000)

		// 选择第一个可用域名
		selectedDomain := aliasEmailList.DomainOptions[0]

		createRequest := &types.CreateAliasRequest{
			LocalPart:       testPrefix,
			DomainSelection: selectedDomain.Value,
		}

		fmt.Printf("  尝试创建: %s@%s\n", testPrefix, selectedDomain.Domain)

		createResponse, err := mailClient.CreateAliasEmail(ctx, session, createRequest)
		if err != nil {
			fmt.Printf("✗ 创建别名邮箱请求失败: %v\n", err)
		} else {
			if createResponse.Success {
				fmt.Printf("✓ 别名邮箱创建成功: %s\n", createResponse.Email)
				fmt.Printf("  消息: %s\n", createResponse.Message)

				// 等待一下，然后重新获取列表验证
				time.Sleep(2 * time.Second)

				// 3. 验证创建结果
				fmt.Printf("\n3. 正在验证创建结果...\n")
				updatedList, err := mailClient.GetAliasEmails(ctx, session)
				if err != nil {
					fmt.Printf("✗ 重新获取别名邮箱列表失败: %v\n", err)
				} else {
					// 查找新创建的别名邮箱
					var newAlias *types.AliasEmail
					for _, alias := range updatedList.Aliases {
						if alias.Email == createResponse.Email {
							newAlias = &alias
							break
						}
					}

					if newAlias != nil {
						fmt.Printf("✓ 验证成功，新别名邮箱已存在于列表中\n")
						fmt.Printf("  邮箱: %s\n", newAlias.Email)
						fmt.Printf("  ID: %s\n", newAlias.ID)

						// 4. 测试删除别名邮箱
						fmt.Printf("\n4. 正在测试删除别名邮箱...\n")
						deleteRequest := &types.DeleteAliasRequest{
							RowID: newAlias.ID,
						}

						deleteResponse, err := mailClient.DeleteAliasEmail(ctx, session, deleteRequest)
						if err != nil {
							fmt.Printf("✗ 删除别名邮箱请求失败: %v\n", err)
						} else {
							if deleteResponse.Success {
								fmt.Printf("✓ 别名邮箱删除成功\n")
								fmt.Printf("  消息: %s\n", deleteResponse.Message)

								// 等待一下，然后验证删除结果
								time.Sleep(2 * time.Second)

								// 5. 验证删除结果
								fmt.Printf("\n5. 正在验证删除结果...\n")
								finalList, err := mailClient.GetAliasEmails(ctx, session)
								if err != nil {
									fmt.Printf("✗ 最终验证获取列表失败: %v\n", err)
								} else {
									// 检查别名邮箱是否已被删除
									found := false
									for _, alias := range finalList.Aliases {
										if alias.Email == createResponse.Email {
											found = true
											break
										}
									}

									if !found {
										fmt.Printf("✓ 验证成功，别名邮箱已从列表中删除\n")
									} else {
										fmt.Printf("⚠ 警告：别名邮箱仍存在于列表中，可能删除未完全生效\n")
									}
								}
							} else {
								fmt.Printf("✗ 别名邮箱删除失败\n")
								fmt.Printf("  消息: %s\n", deleteResponse.Message)
							}
						}
					} else {
						fmt.Printf("⚠ 警告：未在更新后的列表中找到新创建的别名邮箱\n")
					}
				}
			} else {
				fmt.Printf("✗ 别名邮箱创建失败\n")
				fmt.Printf("  消息: %s\n", createResponse.Message)
			}
		}
	} else {
		fmt.Printf("\n⚠ 跳过创建测试：没有可用的域名选项\n")
	}

	fmt.Printf("\n--- 别名邮箱功能测试完成 ---\n")
}

// parseProxy 解析代理字符串
func parseProxy(proxyStr string) (*types.ProxyConfig, error) {
	// 使用标准库 net/url 解析代理URL
	// 支持格式: http://host:port, socks5://user:pass@host:port

	parsedURL, err := url.Parse(proxyStr)
	if err != nil {
		return nil, fmt.Errorf("解析代理URL失败: %v", err)
	}

	// 确定代理类型
	var proxyType types.ProxyType
	switch parsedURL.Scheme {
	case "http":
		proxyType = types.ProxyTypeHTTP
	case "socks5":
		proxyType = types.ProxyTypeSOCKS5
	default:
		return nil, fmt.Errorf("不支持的代理类型: %s (支持 http, socks5)", parsedURL.Scheme)
	}

	// 解析主机和端口
	host := parsedURL.Hostname()
	if host == "" {
		return nil, fmt.Errorf("代理主机不能为空")
	}

	portStr := parsedURL.Port()
	if portStr == "" {
		return nil, fmt.Errorf("代理端口不能为空")
	}

	port, err := strconv.Atoi(portStr)
	if err != nil {
		return nil, fmt.Errorf("解析代理端口失败: %v", err)
	}

	if port <= 0 || port > 65535 {
		return nil, fmt.Errorf("代理端口超出范围: %d (应在 1-65535)", port)
	}

	// 解析用户名和密码（如果有）
	var username, password string
	if parsedURL.User != nil {
		username = parsedURL.User.Username()
		password, _ = parsedURL.User.Password()
	}

	return &types.ProxyConfig{
		ID:       fmt.Sprintf("%s_%s_%d", proxyType, host, port),
		Type:     proxyType,
		Host:     host,
		Port:     port,
		Username: username,
		Password: password,
		Enabled:  true,
	}, nil
}
