package admin

import (
	"go-mail/internal/api/handlers"
	"go-mail/internal/api/middleware"

	"github.com/gin-gonic/gin"
)

// MailboxController 邮箱管理控制器
type MailboxController struct {
	adminAPI *AdminAPI
}

// NewMailboxController 创建邮箱管理控制器
func NewMailboxController(adminAPI *AdminAPI) *MailboxController {
	return &MailboxController{
		adminAPI: adminAPI,
	}
}

// RegisterRoutes 注册邮箱管理路由
func (c *MailboxController) RegisterRoutes(v1 *gin.RouterGroup) {
	mailboxGroup := v1.Group("/mailbox")
	mailboxGroup.Use(middleware.JWTAuth(c.adminAPI.auth))
	mailboxGroup.Use(middleware.SecurityHeaders())
	mailboxGroup.Use(middleware.OperationAudit())
	{
		mailboxHandler := handlers.NewMailboxManagementHandler(c.adminAPI.services.MailboxManagement)
		c.adminAPI.logger.Info("注册邮箱管理路由", "prefix", "/api/v1/mailbox")

		// 创建频率限制器
		rateLimiter := middleware.NewRateLimiter()

		// 批量操作（应用严格的频率和并发限制）
		mailboxGroup.POST("/batch-import",
			rateLimiter.ImportRateLimit(),
			middleware.BatchImportSizeLimit(),
			middleware.BatchOperationConcurrencyLimit(),
			mailboxHandler.BatchImportAccounts)

		mailboxGroup.POST("/verify",
			rateLimiter.VerificationRateLimit(),
			middleware.VerificationConcurrencyLimit(),
			mailboxHandler.StartVerificationTask)

		mailboxGroup.GET("/batch-operation/:operation_id", mailboxHandler.GetBatchOperationStatus)

		// 账户管理
		mailboxGroup.GET("/accounts", mailboxHandler.GetAccountsList)
		mailboxGroup.DELETE("/accounts/:id", mailboxHandler.DeleteAccount)
		mailboxGroup.PUT("/accounts/:id/status", mailboxHandler.ToggleAccountStatus)
		mailboxGroup.POST("/batch-delete", mailboxHandler.BatchDeleteAccounts)
		mailboxGroup.POST("/batch-disable", mailboxHandler.BatchDisableAccounts)

		// 任务控制（应用频率限制）
		mailboxGroup.POST("/task-control",
			rateLimiter.BatchOperationRateLimit(),
			mailboxHandler.ControlTask)

		mailboxGroup.GET("/scheduler-status", mailboxHandler.GetTaskSchedulerStatus)

		// 统计信息
		mailboxGroup.GET("/statistics", mailboxHandler.GetMailboxStatistics)

		
		// 代理配置管理
		mailboxGroup.GET("/proxy-config", mailboxHandler.GetProxyConfig)        // 获取代理配置
		mailboxGroup.POST("/proxy-config", mailboxHandler.SetProxyConfig)       // 设置代理配置
		mailboxGroup.DELETE("/proxy-config", mailboxHandler.DeleteProxyConfig)  // 删除代理配置
		mailboxGroup.POST("/proxy-config/test", mailboxHandler.TestProxyConfig) // 测试代理连通性

		// 任务日志管理 - 添加到mailbox组下以匹配前端API调用
		taskLogHandler := handlers.NewTaskLogHandler(c.adminAPI.services.TaskLog)
		mailboxGroup.GET("/task-logs", taskLogHandler.GetTaskLogs)                  // 获取任务日志列表
		mailboxGroup.GET("/task-logs/:id/details", taskLogHandler.GetTaskLogDetail) // 获取任务日志详情
		mailboxGroup.POST("/task-logs", taskLogHandler.CreateTaskLog)               // 创建任务日志
		mailboxGroup.PUT("/task-logs/:task_id", taskLogHandler.UpdateTaskLog)       // 更新任务日志

		c.adminAPI.logger.Info("邮箱管理路由注册完成", "routes", []string{
			"POST /api/v1/mailbox/batch-import",
			"POST /api/v1/mailbox/verify",
			"GET /api/v1/mailbox/batch-operation/:operation_id",
			"GET /api/v1/mailbox/accounts",
			"DELETE /api/v1/mailbox/accounts/:id",
			"PUT /api/v1/mailbox/accounts/:id/status",
			"POST /api/v1/mailbox/batch-delete",
			"POST /api/v1/mailbox/batch-disable",
			"POST /api/v1/mailbox/task-control",
			"GET /api/v1/mailbox/scheduler-status",
			"GET /api/v1/mailbox/statistics",
			"GET /api/v1/mailbox/task-logs",
			"GET /api/v1/mailbox/task-logs/:id/details",
			"POST /api/v1/mailbox/task-logs",
			"PUT /api/v1/mailbox/task-logs/:task_id",
		})
	}
}
