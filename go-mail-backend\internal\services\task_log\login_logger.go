package task_log

import (
	"fmt"
	"go-mail/internal/database"
	"go-mail/internal/types"
	"log/slog"
	"time"
)

// LoginLogger 登录过程日志记录器
type LoginLogger struct {
	taskLogService   *TaskLogService
	detailLogService *DetailLogService
	taskID           string
	email            string
	logFilePath      string
	logger           *slog.Logger
}

// NewLoginLogger 创建登录日志记录器
func NewLoginLogger(taskLogService *TaskLogService, detailLogService *DetailLogService, email string) (*LoginLogger, error) {
	// 生成任务ID
	taskID := fmt.Sprintf("login_%s_%d", email, time.Now().Unix())

	// 创建任务日志记录
	createReq := database.CreateTaskLogRequest{
		TaskID:        taskID,
		OperationType: "login",
		Email:         email,
		Status:        "running",
		StartTime:     time.Now(),
	}

	taskLog, err := taskLogService.CreateTaskLog(createReq)
	if err != nil {
		return nil, fmt.Errorf("创建任务日志失败: %w", err)
	}

	return &LoginLogger{
		taskLogService:   taskLogService,
		detailLogService: detailLogService,
		taskID:           taskID,
		email:            email,
		logFilePath:      *taskLog.DetailLogPath,
		logger:           slog.Default(),
	}, nil
}

// LogStep 记录登录步骤
func (l *LoginLogger) LogStep(stepName, details string, responseData map[string]interface{}) error {
	step := database.TaskLogStep{
		Step:         stepName,
		Timestamp:    time.Now(),
		Status:       "success",
		Details:      details,
		ResponseData: responseData,
	}

	// 同时输出到控制台
	l.logger.Info("登录步骤",
		"email", l.email,
		"task_id", l.taskID,
		"step", stepName,
		"details", details,
		"status", "success")

	return l.detailLogService.AddStep(l.logFilePath, step)
}

// LogStepWithDuration 记录带耗时的登录步骤
func (l *LoginLogger) LogStepWithDuration(stepName, details string, duration time.Duration, responseData map[string]interface{}) error {
	durationMs := int(duration.Milliseconds())
	step := database.TaskLogStep{
		Step:         stepName,
		Timestamp:    time.Now(),
		Status:       "success",
		Details:      details,
		Duration:     &durationMs,
		ResponseData: responseData,
	}

	// 同时输出到控制台
	l.logger.Info("登录步骤",
		"email", l.email,
		"task_id", l.taskID,
		"step", stepName,
		"details", details,
		"duration_ms", durationMs,
		"status", "success")

	return l.detailLogService.AddStep(l.logFilePath, step)
}

// LogError 记录错误步骤
func (l *LoginLogger) LogError(stepName, details, errorMessage string) error {
	step := database.TaskLogStep{
		Step:         stepName,
		Timestamp:    time.Now(),
		Status:       "failed",
		Details:      details,
		ErrorMessage: &errorMessage,
		ResponseData: make(map[string]interface{}),
	}

	// 同时输出到控制台
	l.logger.Error("登录步骤失败",
		"email", l.email,
		"task_id", l.taskID,
		"step", stepName,
		"details", details,
		"error", errorMessage,
		"status", "failed")

	return l.detailLogService.AddStep(l.logFilePath, step)
}

// LogSuccess 记录成功完成
func (l *LoginLogger) LogSuccess(loginResult *types.LoginResult) error {
	// 计算总耗时
	duration := time.Since(loginResult.LoginTime)
	durationMs := int(duration.Milliseconds())

	// 同时输出到控制台
	l.logger.Info("登录成功",
		"email", l.email,
		"task_id", l.taskID,
		"duration_ms", durationMs,
		"jsession_id", loginResult.JSessionID,
		"navigator_sid", loginResult.NavigatorSID,
		"message", loginResult.Message)

	// 更新详细日志状态
	if err := l.detailLogService.UpdateStatus(l.logFilePath, "success", nil); err != nil {
		return fmt.Errorf("更新详细日志状态失败: %w", err)
	}

	// 更新任务日志
	updateReq := database.UpdateTaskLogRequest{
		Status:     &[]string{"success"}[0],
		EndTime:    &[]time.Time{time.Now()}[0],
		DurationMs: &durationMs,
	}

	return l.taskLogService.UpdateTaskLog(l.taskID, updateReq)
}

// LogFailure 记录失败完成
func (l *LoginLogger) LogFailure(errorMessage string) error {
	// 同时输出到控制台
	l.logger.Error("登录失败",
		"email", l.email,
		"task_id", l.taskID,
		"error", errorMessage)

	// 更新详细日志状态
	if err := l.detailLogService.UpdateStatus(l.logFilePath, "failed", &errorMessage); err != nil {
		return fmt.Errorf("更新详细日志状态失败: %w", err)
	}

	// 更新任务日志
	updateReq := database.UpdateTaskLogRequest{
		Status:       &[]string{"failed"}[0],
		EndTime:      &[]time.Time{time.Now()}[0],
		ErrorMessage: &errorMessage,
	}

	return l.taskLogService.UpdateTaskLog(l.taskID, updateReq)
}

// GetTaskID 获取任务ID
func (l *LoginLogger) GetTaskID() string {
	return l.taskID
}

// GetLogFilePath 获取日志文件路径
func (l *LoginLogger) GetLogFilePath() string {
	return l.logFilePath
}

// LogInitialRequest 记录初始登录请求
func (l *LoginLogger) LogInitialRequest(account types.Account, proxyConfig *types.ProxyConfig) error {
	responseData := map[string]interface{}{
		"username":  account.Username,
		"has_proxy": proxyConfig != nil,
	}

	if proxyConfig != nil {
		responseData["proxy_type"] = proxyConfig.Type
		responseData["proxy_host"] = proxyConfig.Host
		responseData["proxy_port"] = proxyConfig.Port
	}

	return l.LogStep("initial_request", "开始登录流程", responseData)
}

// LogOTTRequest 记录OTT请求
func (l *LoginLogger) LogOTTRequest(duration time.Duration, ott string) error {
	responseData := map[string]interface{}{
		"ott_length": len(ott),
		"ott_prefix": ott[:min(10, len(ott))],
	}

	return l.LogStepWithDuration("get_ott", "获取OTT令牌", duration, responseData)
}

// LogRedirectRequest 记录重定向请求
func (l *LoginLogger) LogRedirectRequest(duration time.Duration, redirectURL string) error {
	responseData := map[string]interface{}{
		"redirect_url": redirectURL,
		"url_length":   len(redirectURL),
	}

	return l.LogStepWithDuration("get_redirect", "获取重定向URL", duration, responseData)
}

// LogSessionRequest 记录会话请求
func (l *LoginLogger) LogSessionRequest(duration time.Duration, sid string) error {
	responseData := map[string]interface{}{
		"sid_length": len(sid),
		"sid_prefix": sid[:min(10, len(sid))],
	}

	return l.LogStepWithDuration("get_session_id", "获取会话ID", duration, responseData)
}

// LogFinalSession 记录最终会话
func (l *LoginLogger) LogFinalSession(duration time.Duration, jsessionID, navigatorSID, finalURL string) error {
	responseData := map[string]interface{}{
		"jsession_id_length":   len(jsessionID),
		"navigator_sid_length": len(navigatorSID),
		"final_url":            finalURL,
		"has_jsession_id":      jsessionID != "",
		"has_navigator_sid":    navigatorSID != "",
	}

	return l.LogStepWithDuration("get_final_session", "获取最终会话信息", duration, responseData)
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
