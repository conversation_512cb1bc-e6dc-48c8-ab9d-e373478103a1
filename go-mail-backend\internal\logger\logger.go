package logger

import (
	"compress/gzip"
	"context"
	"fmt"
	"io"
	"log/slog"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"
)

// LogLevel 日志级别
type LogLevel int

const (
	LevelDebug LogLevel = iota
	LevelInfo
	LevelWarn
	LevelError
)

// String 返回日志级别字符串
func (l LogLevel) String() string {
	switch l {
	case LevelDebug:
		return "DEBUG"
	case LevelInfo:
		return "INFO"
	case LevelWarn:
		return "WARN"
	case LevelError:
		return "ERROR"
	default:
		return "UNKNOWN"
	}
}

// LoggerConfig 日志配置
type LoggerConfig struct {
	Level         LogLevel `json:"level"`
	Format        string   `json:"format"`         // "text", "json", or "logback"
	Output        string   `json:"output"`         // "stdout", "stderr", or file path
	MaxSize       int      `json:"max_size"`       // 最大文件大小(MB)
	MaxBackups    int      `json:"max_backups"`    // 最大备份文件数
	MaxAge        int      `json:"max_age"`        // 最大保留天数
	Compress      bool     `json:"compress"`       // 是否压缩备份文件
	AsyncWrite    bool     `json:"async_write"`    // 是否启用异步写入
	BufferSize    int      `json:"buffer_size"`    // 异步写入缓冲区大小
	FlushInterval int      `json:"flush_interval"` // 刷新间隔(秒)
	ShowCaller    bool     `json:"show_caller"`    // 是否显示调用者信息
	ShowThread    bool     `json:"show_thread"`    // 是否显示线程信息
}

// DefaultLoggerConfig 默认日志配置
func DefaultLoggerConfig() *LoggerConfig {
	return &LoggerConfig{
		Level:         LevelInfo,
		Format:        "logback",
		Output:        "both", // 同时输出到终端和文件
		MaxSize:       500,    // 500MB
		MaxBackups:    10,
		MaxAge:        30,
		Compress:      true,
		AsyncWrite:    false, // 双重输出时禁用异步写入，避免复杂性
		BufferSize:    2048,
		FlushInterval: 3,
		ShowCaller:    true,
		ShowThread:    true,
	}
}

// FileOnlyLoggerConfig 仅文件输出的日志配置（用于生产环境）
func FileOnlyLoggerConfig() *LoggerConfig {
	return &LoggerConfig{
		Level:         LevelInfo,
		Format:        "logback",
		Output:        "go-mail-data/log/go-mail.log",
		MaxSize:       500, // 500MB
		MaxBackups:    10,
		MaxAge:        30,
		Compress:      true,
		AsyncWrite:    true, // 仅文件输出时可以启用异步写入
		BufferSize:    2048,
		FlushInterval: 3,
		ShowCaller:    true,
		ShowThread:    true,
	}
}

// ConsoleOnlyLoggerConfig 仅终端输出的日志配置（用于调试）
func ConsoleOnlyLoggerConfig() *LoggerConfig {
	return &LoggerConfig{
		Level:         LevelDebug,
		Format:        "logback",
		Output:        "stdout",
		MaxSize:       100,
		MaxBackups:    5,
		MaxAge:        7,
		Compress:      false,
		AsyncWrite:    false,
		BufferSize:    1024,
		FlushInterval: 1,
		ShowCaller:    true,
		ShowThread:    true,
	}
}

// RotatingWriter 日志轮转写入器
type RotatingWriter struct {
	filename   string
	maxSize    int64 // 最大文件大小（字节）
	maxBackups int   // 最大备份文件数
	compress   bool  // 是否压缩备份文件

	file  *os.File
	size  int64
	mutex sync.Mutex
}

// NewRotatingWriter 创建日志轮转写入器
func NewRotatingWriter(filename string, maxSizeMB int, maxBackups int, compress bool) (*RotatingWriter, error) {
	rw := &RotatingWriter{
		filename:   filename,
		maxSize:    int64(maxSizeMB) * 1024 * 1024, // 转换为字节
		maxBackups: maxBackups,
		compress:   compress,
	}

	// 确保目录存在
	if err := os.MkdirAll(filepath.Dir(filename), 0755); err != nil {
		return nil, fmt.Errorf("创建日志目录失败: %w", err)
	}

	// 打开文件
	if err := rw.openFile(); err != nil {
		return nil, err
	}

	return rw, nil
}

// openFile 打开日志文件
func (rw *RotatingWriter) openFile() error {
	file, err := os.OpenFile(rw.filename, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %w", err)
	}

	// 获取文件大小
	info, err := file.Stat()
	if err != nil {
		file.Close()
		return fmt.Errorf("获取文件信息失败: %w", err)
	}

	rw.file = file
	rw.size = info.Size()
	return nil
}

// Write 实现 io.Writer 接口
func (rw *RotatingWriter) Write(p []byte) (n int, err error) {
	rw.mutex.Lock()
	defer rw.mutex.Unlock()

	// 检查是否需要轮转
	if rw.size+int64(len(p)) > rw.maxSize {
		if err := rw.rotate(); err != nil {
			return 0, err
		}
	}

	// 写入数据
	n, err = rw.file.Write(p)
	rw.size += int64(n)
	return n, err
}

// rotate 执行日志轮转
func (rw *RotatingWriter) rotate() error {
	// 关闭当前文件
	if rw.file != nil {
		rw.file.Close()
	}

	// 生成备份文件名（按日期归档）
	now := time.Now()
	dateDir := filepath.Join(filepath.Dir(rw.filename), now.Format("2006-01-02"))
	if err := os.MkdirAll(dateDir, 0755); err != nil {
		return fmt.Errorf("创建日期目录失败: %w", err)
	}

	baseName := filepath.Base(rw.filename)
	ext := filepath.Ext(baseName)
	nameWithoutExt := strings.TrimSuffix(baseName, ext)

	// 查找可用的备份文件名
	var backupPath string
	for i := 1; i <= rw.maxBackups; i++ {
		backupName := fmt.Sprintf("%s_%s_%d%s", nameWithoutExt, now.Format("15-04-05"), i, ext)
		backupPath = filepath.Join(dateDir, backupName)
		if _, err := os.Stat(backupPath); os.IsNotExist(err) {
			break
		}
	}

	// 移动当前文件到备份位置
	if err := os.Rename(rw.filename, backupPath); err != nil {
		return fmt.Errorf("移动日志文件失败: %w", err)
	}

	// 压缩备份文件（如果启用）
	if rw.compress {
		go rw.compressFile(backupPath)
	}

	// 清理旧的备份文件
	go rw.cleanupOldBackups()

	// 重新打开文件
	return rw.openFile()
}

// compressFile 压缩文件
func (rw *RotatingWriter) compressFile(filename string) {
	gzFilename := filename + ".gz"

	// 打开源文件
	src, err := os.Open(filename)
	if err != nil {
		return
	}
	defer src.Close()

	// 创建压缩文件
	dst, err := os.Create(gzFilename)
	if err != nil {
		return
	}
	defer dst.Close()

	// 创建 gzip 写入器
	gzWriter := gzip.NewWriter(dst)
	defer gzWriter.Close()

	// 复制数据
	if _, err := io.Copy(gzWriter, src); err != nil {
		os.Remove(gzFilename) // 删除损坏的压缩文件
		return
	}

	// 删除原文件
	os.Remove(filename)
}

// cleanupOldBackups 清理旧的备份文件
func (rw *RotatingWriter) cleanupOldBackups() {
	logDir := filepath.Dir(rw.filename)

	// 遍历日志目录
	entries, err := os.ReadDir(logDir)
	if err != nil {
		return
	}

	// 收集日期目录
	var dateDirs []string
	for _, entry := range entries {
		if entry.IsDir() {
			// 检查是否是日期格式的目录
			if _, err := time.Parse("2006-01-02", entry.Name()); err == nil {
				dateDirs = append(dateDirs, entry.Name())
			}
		}
	}

	// 如果日期目录超过保留天数，删除最旧的
	if len(dateDirs) > 30 { // 保留30天
		// 排序日期目录
		for i := 0; i < len(dateDirs)-1; i++ {
			for j := i + 1; j < len(dateDirs); j++ {
				if dateDirs[i] > dateDirs[j] {
					dateDirs[i], dateDirs[j] = dateDirs[j], dateDirs[i]
				}
			}
		}

		// 删除最旧的目录
		for i := 0; i < len(dateDirs)-30; i++ {
			oldDir := filepath.Join(logDir, dateDirs[i])
			os.RemoveAll(oldDir)
		}
	}
}

// Close 关闭写入器
func (rw *RotatingWriter) Close() error {
	rw.mutex.Lock()
	defer rw.mutex.Unlock()

	if rw.file != nil {
		return rw.file.Close()
	}
	return nil
}

// MultiWriter 多重写入器，支持同时写入多个目标
type MultiWriter struct {
	writers []io.Writer
}

// NewMultiWriter 创建多重写入器
func NewMultiWriter(writers ...io.Writer) *MultiWriter {
	return &MultiWriter{
		writers: writers,
	}
}

// Write 实现 io.Writer 接口，同时写入所有目标
func (mw *MultiWriter) Write(p []byte) (n int, err error) {
	for _, w := range mw.writers {
		n, err = w.Write(p)
		if err != nil {
			return n, err
		}
	}
	return len(p), nil
}

// Close 关闭所有可关闭的写入器
func (mw *MultiWriter) Close() error {
	var lastErr error
	for _, w := range mw.writers {
		if closer, ok := w.(io.Closer); ok {
			if err := closer.Close(); err != nil {
				lastErr = err
			}
		}
	}
	return lastErr
}

// AsyncWriter 异步写入器
type AsyncWriter struct {
	output     io.Writer
	buffer     chan []byte
	done       chan struct{}
	wg         sync.WaitGroup
	bufferSize int
	flushTimer *time.Timer
}

// NewAsyncWriter 创建异步写入器
func NewAsyncWriter(output io.Writer, bufferSize int, flushInterval time.Duration) *AsyncWriter {
	aw := &AsyncWriter{
		output:     output,
		buffer:     make(chan []byte, bufferSize),
		done:       make(chan struct{}),
		bufferSize: bufferSize,
	}

	aw.wg.Add(1)
	go aw.writeLoop(flushInterval)

	return aw
}

// Write 实现 io.Writer 接口
func (aw *AsyncWriter) Write(p []byte) (n int, err error) {
	// 复制数据避免竞态条件
	data := make([]byte, len(p))
	copy(data, p)

	select {
	case aw.buffer <- data:
		return len(p), nil
	case <-aw.done:
		return 0, fmt.Errorf("async writer is closed")
	default:
		// 缓冲区满时，同步写入
		return aw.output.Write(p)
	}
}

// writeLoop 异步写入循环
func (aw *AsyncWriter) writeLoop(flushInterval time.Duration) {
	defer aw.wg.Done()

	ticker := time.NewTicker(flushInterval)
	defer ticker.Stop()

	for {
		select {
		case data := <-aw.buffer:
			aw.output.Write(data)
		case <-ticker.C:
			// 定期刷新（如果支持）
			if syncer, ok := aw.output.(interface{ Sync() error }); ok {
				syncer.Sync()
			}
		case <-aw.done:
			// 处理剩余的缓冲数据
			for {
				select {
				case data := <-aw.buffer:
					aw.output.Write(data)
				default:
					if syncer, ok := aw.output.(interface{ Sync() error }); ok {
						syncer.Sync()
					}
					return
				}
			}
		}
	}
}

// Close 关闭异步写入器
func (aw *AsyncWriter) Close() error {
	close(aw.done)
	aw.wg.Wait()

	// 如果支持关闭，则关闭
	if closer, ok := aw.output.(io.Closer); ok {
		return closer.Close()
	}
	return nil
}

// Logger 日志记录器
type Logger struct {
	*slog.Logger
	config      *LoggerConfig
	asyncWriter *AsyncWriter
}

// NewLogger 创建新的日志记录器
func NewLogger(config *LoggerConfig) (*Logger, error) {
	if config == nil {
		config = DefaultLoggerConfig()
	}

	// 创建处理器选项
	opts := &slog.HandlerOptions{
		Level: convertLogLevel(config.Level),
		ReplaceAttr: func(groups []string, a slog.Attr) slog.Attr {
			// 根据格式类型自定义属性
			switch config.Format {
			case "logback":
				return formatLogbackStyle(a, config)
			default:
				// 自定义时间格式
				if a.Key == slog.TimeKey {
					a.Value = slog.StringValue(time.Now().Format("2006-01-02 15:04:05.000"))
				}
				// 添加调用者信息
				if a.Key == slog.SourceKey && config.ShowCaller {
					if source, ok := a.Value.Any().(*slog.Source); ok {
						// 简化文件路径
						source.File = filepath.Base(source.File)
						a.Value = slog.AnyValue(source)
					}
				}
			}
			return a
		},
		AddSource: config.ShowCaller,
	}

	// 创建输出目标
	var writer io.Writer

	switch config.Output {
	case "stdout":
		writer = os.Stdout
	case "stderr":
		writer = os.Stderr
	case "both":
		// 同时输出到终端和文件
		rotatingWriter, err := NewRotatingWriter("go-mail-data/log/go-mail.log", config.MaxSize, config.MaxBackups, config.Compress)
		if err != nil {
			return nil, fmt.Errorf("创建日志轮转写入器失败: %w", err)
		}
		writer = NewMultiWriter(os.Stdout, rotatingWriter)
	default:
		// 文件输出，使用日志轮转
		rotatingWriter, err := NewRotatingWriter(config.Output, config.MaxSize, config.MaxBackups, config.Compress)
		if err != nil {
			return nil, fmt.Errorf("创建日志轮转写入器失败: %w", err)
		}
		writer = rotatingWriter
	}

	// 创建最终写入器（支持异步写入）
	var finalWriter io.Writer
	var asyncWriter *AsyncWriter

	if config.AsyncWrite && config.Output != "stdout" && config.Output != "stderr" {
		// 只对文件输出启用异步写入
		flushInterval := time.Duration(config.FlushInterval) * time.Second
		asyncWriter = NewAsyncWriter(writer, config.BufferSize, flushInterval)
		finalWriter = asyncWriter
	} else {
		finalWriter = writer
	}

	// 创建处理器
	var handler slog.Handler
	switch config.Format {
	case "json":
		handler = slog.NewJSONHandler(finalWriter, opts)
	case "logback":
		handler = NewLogbackHandler(finalWriter, opts, config)
	default:
		handler = slog.NewTextHandler(finalWriter, opts)
	}

	logger := slog.New(handler)

	return &Logger{
		Logger:      logger,
		config:      config,
		asyncWriter: asyncWriter,
	}, nil
}

// convertLogLevel 转换日志级别
func convertLogLevel(level LogLevel) slog.Level {
	switch level {
	case LevelDebug:
		return slog.LevelDebug
	case LevelInfo:
		return slog.LevelInfo
	case LevelWarn:
		return slog.LevelWarn
	case LevelError:
		return slog.LevelError
	default:
		return slog.LevelInfo
	}
}

// WithContext 添加上下文信息
func (l *Logger) WithContext(ctx context.Context) *Logger {
	// 从上下文中提取请求ID等信息
	if requestID := ctx.Value("request_id"); requestID != nil {
		return &Logger{
			Logger: l.Logger.With("request_id", requestID),
			config: l.config,
		}
	}
	return l
}

// WithFields 添加字段
func (l *Logger) WithFields(fields map[string]any) *Logger {
	args := make([]any, 0, len(fields)*2)
	for k, v := range fields {
		args = append(args, k, v)
	}
	return &Logger{
		Logger: l.Logger.With(args...),
		config: l.config,
	}
}

// WithError 添加错误信息
func (l *Logger) WithError(err error) *Logger {
	if err == nil {
		return l
	}
	return &Logger{
		Logger: l.Logger.With("error", err.Error()),
		config: l.config,
	}
}

// WithCaller 添加调用者信息
func (l *Logger) WithCaller() *Logger {
	_, file, line, ok := runtime.Caller(1)
	if !ok {
		return l
	}
	return &Logger{
		Logger: l.Logger.With(
			"caller", fmt.Sprintf("%s:%d", filepath.Base(file), line),
		),
		config: l.config,
	}
}

// LogRequest 记录HTTP请求
func (l *Logger) LogRequest(method, path, userAgent, clientIP string, statusCode int, duration time.Duration) {
	l.Info("HTTP请求",
		"method", method,
		"path", path,
		"status", statusCode,
		"duration", duration.String(),
		"user_agent", userAgent,
		"client_ip", clientIP,
	)
}

// LogError 记录错误
func (l *Logger) LogError(operation string, err error, fields ...any) {
	args := []any{"operation", operation, "error", err.Error()}
	args = append(args, fields...)
	l.Error("操作失败", args...)
}

// LogSuccess 记录成功操作
func (l *Logger) LogSuccess(operation string, fields ...any) {
	args := []any{"operation", operation}
	args = append(args, fields...)
	l.Info("操作成功", args...)
}

// LogAuth 记录认证事件
func (l *Logger) LogAuth(event, username, clientIP string, success bool, fields ...any) {
	args := []any{
		"event", event,
		"username", username,
		"client_ip", clientIP,
		"success", success,
	}
	args = append(args, fields...)

	if success {
		l.Info("认证事件", args...)
	} else {
		l.Warn("认证失败", args...)
	}
}

// LogMailbox 记录邮箱操作
func (l *Logger) LogMailbox(operation string, mailboxID int, address string, fields ...any) {
	args := []any{
		"operation", operation,
		"mailbox_id", mailboxID,
		"address", address,
	}
	args = append(args, fields...)
	l.Info("邮箱操作", args...)
}

// LogActivation 记录激活码操作
func (l *Logger) LogActivation(operation, code, deviceFingerprint string, fields ...any) {
	args := []any{
		"operation", operation,
		"code", code,
		"device_fingerprint", deviceFingerprint,
	}
	args = append(args, fields...)
	l.Info("激活码操作", args...)
}

// LogTask 记录定时任务
func (l *Logger) LogTask(taskName string, duration time.Duration, success bool, err error) {
	args := []any{
		"task", taskName,
		"duration", duration.String(),
		"success", success,
	}

	if err != nil {
		args = append(args, "error", err.Error())
		l.Error("定时任务执行失败", args...)
	} else {
		l.Info("定时任务执行完成", args...)
	}
}

// LogPerformance 记录性能指标
func (l *Logger) LogPerformance(operation string, duration time.Duration, fields ...any) {
	args := []any{
		"operation", operation,
		"duration", duration.String(),
	}
	args = append(args, fields...)

	// 根据耗时判断日志级别
	if duration > 5*time.Second {
		l.Warn("操作耗时过长", args...)
	} else if duration > 1*time.Second {
		l.Info("操作耗时", args...)
	} else {
		l.Debug("操作耗时", args...)
	}
}

// Close 关闭日志记录器
func (l *Logger) Close() error {
	if l.asyncWriter != nil {
		return l.asyncWriter.Close()
	}
	return nil
}

// GetConfig 获取配置
func (l *Logger) GetConfig() *LoggerConfig {
	return l.config
}

// SetLevel 设置日志级别
func (l *Logger) SetLevel(level LogLevel) {
	l.config.Level = level
	// 注意：slog.Logger的级别在创建时设定，运行时无法修改
	// 如果需要动态修改级别，需要重新创建Logger
}

// logWithSource 带源码位置的日志辅助函数
func (l *Logger) logWithSource(level string, msg string, args ...interface{}) {
	_, file, line, ok := runtime.Caller(2) // 跳过两层调用栈
	if ok {
		// 提取文件名（去掉路径）
		for i := len(file) - 1; i >= 0; i-- {
			if file[i] == '/' || file[i] == '\\' {
				file = file[i+1:]
				break
			}
		}
		// 添加源码位置信息
		args = append([]interface{}{"source", fmt.Sprintf("%s:%d", file, line)}, args...)
	}

	switch level {
	case "DEBUG":
		l.Logger.Debug(msg, args...)
	case "INFO":
		l.Logger.Info(msg, args...)
	case "WARN":
		l.Logger.Warn(msg, args...)
	case "ERROR":
		l.Logger.Error(msg, args...)
	default:
		l.Logger.Info(msg, args...)
	}
}

// 全局日志记录器
var defaultLogger *Logger

// InitDefaultLogger 初始化默认日志记录器
func InitDefaultLogger(config *LoggerConfig) error {
	logger, err := NewLogger(config)
	if err != nil {
		return err
	}
	defaultLogger = logger
	return nil
}

// GetDefaultLogger 获取默认日志记录器
func GetDefaultLogger() *Logger {
	if defaultLogger == nil {
		// 如果没有初始化，使用默认配置
		logger, _ := NewLogger(DefaultLoggerConfig())
		defaultLogger = logger
	}
	return defaultLogger
}

// 便捷函数（自动添加源码位置）
func Debug(msg string, args ...any) {
	GetDefaultLogger().logWithSource("DEBUG", msg, args...)
}

func Info(msg string, args ...any) {
	GetDefaultLogger().logWithSource("INFO", msg, args...)
}

func Warn(msg string, args ...any) {
	GetDefaultLogger().logWithSource("WARN", msg, args...)
}

func Error(msg string, args ...any) {
	GetDefaultLogger().logWithSource("ERROR", msg, args...)
}

// formatLogbackStyle 格式化为 Logback 风格
func formatLogbackStyle(a slog.Attr, config *LoggerConfig) slog.Attr {
	switch a.Key {
	case slog.TimeKey:
		// Logback 时间格式: 2024-01-15 14:30:45.123
		a.Value = slog.StringValue(time.Now().Format("2006-01-02 15:04:05.000"))
	case slog.LevelKey:
		// 格式化日志级别
		level := a.Value.String()
		a.Value = slog.StringValue(strings.ToUpper(level))
	case slog.SourceKey:
		if config.ShowCaller {
			if source, ok := a.Value.Any().(*slog.Source); ok {
				// Logback 格式: ClassName.methodName(FileName:LineNumber)
				fileName := filepath.Base(source.File)
				a.Value = slog.StringValue(fmt.Sprintf("%s:%d", fileName, source.Line))
			}
		}
	}
	return a
}

// LogbackHandler Logback 风格的处理器
type LogbackHandler struct {
	handler slog.Handler
	config  *LoggerConfig
}

// NewLogbackHandler 创建 Logback 风格处理器
func NewLogbackHandler(w io.Writer, opts *slog.HandlerOptions, config *LoggerConfig) *LogbackHandler {
	return &LogbackHandler{
		handler: slog.NewTextHandler(w, opts),
		config:  config,
	}
}

// Enabled 实现 slog.Handler 接口
func (h *LogbackHandler) Enabled(ctx context.Context, level slog.Level) bool {
	return h.handler.Enabled(ctx, level)
}

// Handle 实现 slog.Handler 接口
func (h *LogbackHandler) Handle(ctx context.Context, r slog.Record) error {
	// 添加线程信息
	if h.config.ShowThread {
		r.Add("thread", fmt.Sprintf("goroutine-%d", runtime.NumGoroutine()))
	}

	return h.handler.Handle(ctx, r)
}

// WithAttrs 实现 slog.Handler 接口
func (h *LogbackHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	return &LogbackHandler{
		handler: h.handler.WithAttrs(attrs),
		config:  h.config,
	}
}

// WithGroup 实现 slog.Handler 接口
func (h *LogbackHandler) WithGroup(name string) slog.Handler {
	return &LogbackHandler{
		handler: h.handler.WithGroup(name),
		config:  h.config,
	}
}

// LogWithStack 记录带堆栈信息的错误日志
func (l *Logger) LogWithStack(level LogLevel, msg string, err error, args ...any) {
	// 获取堆栈信息
	stack := make([]byte, 4096)
	n := runtime.Stack(stack, false)
	stackTrace := string(stack[:n])

	// 添加堆栈信息到参数
	allArgs := append(args, "error", err.Error(), "stack_trace", stackTrace)

	switch level {
	case LevelError:
		l.Error(msg, allArgs...)
	case LevelWarn:
		l.Warn(msg, allArgs...)
	case LevelInfo:
		l.Info(msg, allArgs...)
	case LevelDebug:
		l.Debug(msg, allArgs...)
	}
}

// LogPanic 记录 panic 信息
func (l *Logger) LogPanic(recovered interface{}) {
	stack := make([]byte, 4096)
	n := runtime.Stack(stack, false)
	stackTrace := string(stack[:n])

	l.Error("Panic recovered",
		"panic", recovered,
		"stack_trace", stackTrace,
		"goroutine_count", runtime.NumGoroutine(),
	)
}
