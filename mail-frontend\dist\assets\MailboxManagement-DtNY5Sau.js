import{r as F,_ as ae}from"./index-BVF_NGWt.js";import{u as oe,H as de,R as pe,S as Ie,Q as fe,j as K,k as ne,T as Be,l as me,i as ge,B as L,J as q,I as re,U as Ue,V as Ae,O as je,w as te,v as se,h as E,F as Z,K as ve,W as Re,X as ie,Y as Ve,L as Fe,Z as Ee,_ as Se,$ as W,a0 as X,a1 as qe,a2 as Je,a3 as He,a4 as Ke,a5 as Ge,a6 as he}from"./ui-B428bLoF.js";import{k as R,W as C,Q as f,X as n,a1 as Qe,r as N,e as ee,c as Y,w as le,O as Q,R as s,K as e,S as t,a4 as j,j as _,$ as I,F as _e,a2 as Me,H as We,m as D,o as ye}from"./vendor-RHijBMdK.js";const Xe={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Ye=n("circle",{cx:"256",cy:"256",r:"208",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),Ze=n("path",{fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32",d:"M108.92 108.92l294.16 294.16"},null,-1),et=[Ye,Ze],tt=R({name:"BanOutline",render:function(v,S){return f(),C("svg",Xe,et)}}),st={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},lt=n("path",{d:"M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192s192-86 192-192z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),at=n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M352 176L217.6 336L160 272"},null,-1),ot=[lt,at],ke=R({name:"CheckmarkCircleOutline",render:function(v,S){return f(),C("svg",st,ot)}}),nt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},rt=n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M416 128L192 384l-96-96"},null,-1),it=[rt],ut=R({name:"CheckmarkOutline",render:function(v,S){return f(),C("svg",nt,it)}}),dt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},ct=n("path",{d:"M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192s192-86 192-192z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),pt=n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M320 320L192 192"},null,-1),vt=n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M192 320l128-128"},null,-1),ft=[ct,pt,vt],Te=R({name:"CloseCircleOutline",render:function(v,S){return f(),C("svg",dt,ft)}}),mt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},gt=n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M368 368L144 144"},null,-1),_t=n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M368 144L144 368"},null,-1),yt=[gt,_t],kt=R({name:"CloseOutline",render:function(v,S){return f(),C("svg",mt,yt)}}),ht={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},wt=n("path",{d:"M320 367.79h76c55 0 100-29.21 100-83.6s-53-81.47-96-83.6c-8.89-85.06-71-136.8-144-136.8c-69 0-113.44 45.79-128 91.2c-60 5.7-112 43.88-112 106.4s54 106.4 120 106.4h56",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),xt=n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M320 255.79l-64-64l-64 64"},null,-1),bt=n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M256 448.21V207.79"},null,-1),Ct=[wt,xt,bt],Pe=R({name:"CloudUploadOutline",render:function(v,S){return f(),C("svg",ht,Ct)}}),$t={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},St=n("path",{d:"M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184s184-82.39 184-184S349.61 64 248 64z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),Mt=n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M220 220h32v116"},null,-1),Tt=n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32",d:"M208 340h88"},null,-1),Pt=n("path",{d:"M248 130a26 26 0 1 0 26 26a26 26 0 0 0-26-26z",fill:"currentColor"},null,-1),Ot=[St,Mt,Tt,Pt],we=R({name:"InformationCircleOutline",render:function(v,S){return f(),C("svg",$t,Ot)}}),Lt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Dt=n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M176 96h16v320h-16z"},null,-1),Nt=n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M320 96h16v320h-16z"},null,-1),zt=[Dt,Nt],It=R({name:"PauseOutline",render:function(v,S){return f(),C("svg",Lt,zt)}}),Bt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Ut=n("path",{d:"M112 111v290c0 17.44 17 28.52 31 20.16l247.9-148.37c12.12-7.25 12.12-26.33 0-33.58L143 90.84c-14-8.36-31 2.72-31 20.16z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),At=[Ut],xe=R({name:"PlayOutline",render:function(v,S){return f(),C("svg",Bt,At)}}),jt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Rt=n("path",{d:"M320 146s24.36-12-64-12a160 160 0 1 0 160 160",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32"},null,-1),Vt=n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M256 58l80 80l-80 80"},null,-1),Ft=[Rt,Vt],ue=R({name:"RefreshOutline",render:function(v,S){return f(),C("svg",jt,Ft)}}),Et={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},qt=n("ellipse",{cx:"256",cy:"128",rx:"192",ry:"80",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32"},null,-1),Jt=n("path",{d:"M448 214c0 44.18-86 80-192 80S64 258.18 64 214",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32"},null,-1),Ht=n("path",{d:"M448 300c0 44.18-86 80-192 80S64 344.18 64 300",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32"},null,-1),Kt=n("path",{d:"M64 127.24v257.52C64 428.52 150 464 256 464s192-35.48 192-79.24V127.24",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32"},null,-1),Gt=[qt,Jt,Ht,Kt],Qt=R({name:"ServerOutline",render:function(v,S){return f(),C("svg",Et,Gt)}}),Wt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Xt=n("rect",{x:"96",y:"96",width:"320",height:"320",rx:"24",ry:"24",fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"32"},null,-1),Yt=[Xt],be=R({name:"StopOutline",render:function(v,S){return f(),C("svg",Wt,Yt)}}),Zt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},es=n("path",{d:"M256 64C150 64 64 150 64 256s86 192 192 192s192-86 192-192S362 64 256 64z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),ts=n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M256 128v144h96"},null,-1),ss=[es,ts],Ce=R({name:"TimeOutline",render:function(v,S){return f(),C("svg",Zt,ss)}}),ls={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},as=Qe('<path d="M112 112l20 320c.95 18.49 14.4 32 32 32h184c17.67 0 30.87-13.51 32-32l20-320" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32"></path><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="32" d="M80 112h352" fill="currentColor"></path><path d="M192 112V72h0a23.93 23.93 0 0 1 24-24h80a23.93 23.93 0 0 1 24 24h0v40" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32"></path><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M256 176v224"></path><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M184 176l8 224"></path><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M328 176l-8 224"></path>',6),os=[as],ns=R({name:"TrashOutline",render:function(v,S){return f(),C("svg",ls,os)}}),U={batchImportAccounts(u){return F.post("/mailbox/batch-import",u)},getAccountsList(u){return F.get("/mailbox/accounts",{params:u})},startVerificationTask(u){return F.post("/mailbox/verify",u)},getBatchOperationStatus(u){return F.get(`/mailbox/batch-operation/${u}`)},controlTask(u){return F.post("/mailbox/task-control",u)},getTaskSchedulerStatus(){return F.get("/mailbox/scheduler-status")},getMailboxStatistics(){return F.get("/mailbox/statistics")},deleteAccount(u){return F.delete(`/mailbox/accounts/${u}`)},batchDeleteAccounts(u){return F.post("/mailbox/batch-delete",{account_ids:u})},toggleAccountStatus(u,v){return F.put(`/mailbox/accounts/${u}/status`,{is_disabled:v})},batchDisableAccounts(u){return F.post("/mailbox/batch-disable",{account_ids:u})},parseBatchImportText(u){const v=u.split(`
`).filter(T=>T.trim()),S=[];for(const T of v){const b=T.trim();if(!b)continue;const h=["----","---","--","	"," "];let r="",i="";for(const d of h)if(b.includes(d)){const $=b.split(d);if($.length>=2){r=$[0].trim(),i=$[1].trim();break}}r&&i&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r)&&S.push({email:r,password:i})}return S},validateEmail(u){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(u)},formatImportPreview(u){const v=[],S=[];return u.forEach(T=>{this.validateEmail(T.email)&&T.password?v.push(T):S.push(`${T.email} - 格式错误`)}),{valid:v,invalid:S,summary:{total:u.length,valid:v.length,invalid:S.length}}},getStatusOptions(){return[{label:"全部状态",value:""},{label:"登录成功",value:"success"},{label:"登录失败",value:"failed"},{label:"未登录",value:"pending"}]},getVerificationStatusOptions(){return[{label:"全部状态",value:""},{label:"未验证",value:"unverified"},{label:"验证成功",value:"verified"},{label:"验证失败",value:"failed"}]},getImportSourceOptions(){return[{label:"全部来源",value:""},{label:"手动添加",value:"manual"},{label:"批量导入",value:"batch_import"},{label:"API导入",value:"api"}]},getProxyConfig(){return F.get("/mailbox/proxy-config")},setProxyConfig(u){return F.post("/mailbox/proxy-config",u)},deleteProxyConfig(){return F.delete("/mailbox/proxy-config")},testProxyConfig(u){return F.post("/mailbox/proxy-config/test",u)},formatStatus(u){return{success:{text:"登录成功",type:"success"},failed:{text:"登录失败",type:"error"},pending:{text:"未登录",type:"warning"},verified:{text:"验证成功",type:"success"},unverified:{text:"未验证",type:"info"},active:{text:"活跃",type:"success"},inactive:{text:"非活跃",type:"warning"},disabled:{text:"已禁用",type:"error"}}[u]||{text:u,type:"info"}}},rs={class:"batch-import-modal"},is={key:0,class:"step-content"},us={class:"step-actions"},ds={key:1,class:"step-content"},cs={key:0,class:"text-red-500"},ps={key:0,class:"mb-4"},vs={key:0,class:"text-gray-500 mt-2"},fs={key:1,class:"mb-4"},ms={class:"step-actions"},gs={key:2,class:"step-content"},_s={key:0,class:"text-center"},ys={key:0},ks={key:1},hs=R({__name:"BatchImportModal",props:{visible:{type:Boolean}},emits:["update:visible","success"],setup(u,{emit:v}){const S=u,T=v,b=oe(),h=N(1),r=N("process"),i=N(!1),d=N(!1),$=ee({importText:"",source:"批量导入",tags:[],description:"",autoVerify:!0}),M=N({valid:[],invalid:[],summary:{total:0,valid:0,invalid:0}}),y=N(null),B=Y({get:()=>S.visible,set:a=>T("update:visible",a)}),G={importText:{required:!0,message:"请输入导入数据",trigger:"blur"},source:{required:!0,message:"请输入导入来源",trigger:"blur"}},J=[{title:"序号",key:"index",width:60,render:(a,o)=>o+1},{title:"邮箱地址",key:"email",ellipsis:{tooltip:!0}},{title:"密码",key:"password",width:120,render:a=>"●".repeat(a.password.length)}],z=async()=>{if(!$.importText.trim()){b.error("请输入导入数据");return}i.value=!0;try{const a=U.parseBatchImportText($.importText);if(M.value=U.formatImportPreview(a),M.value.valid.length===0){b.error("没有解析到有效的账户数据，请检查格式");return}h.value=2}catch(a){b.error("解析数据失败"),console.error(a)}finally{i.value=!1}},m=async()=>{d.value=!0;try{const a={accounts:M.value.valid,source:$.source,tags:$.tags,auto_verify:$.autoVerify,description:$.description},o=await U.batchImportAccounts(a);console.log("导入响应:",JSON.stringify(o));const l=o.data||o;y.value={success:l.success,message:l.message,data:l.data},console.log("导入结果:",JSON.stringify(y.value)),h.value=3,r.value=l.success?"finish":"error",l.success&&T("success",l.data)}catch(a){console.log("导入错误:",JSON.stringify(a)),y.value={success:!1,message:a.message||"导入失败"},h.value=3,r.value="error"}finally{d.value=!1}},P=()=>{b.info("功能开发中...")},H=()=>{p(),B.value=!1},p=()=>{h.value=1,r.value="process",$.importText="",$.source="批量导入",$.tags=[],$.description="",$.autoVerify=!0,M.value={valid:[],invalid:[],summary:{total:0,valid:0,invalid:0}},y.value=null};return le(B,a=>{a||p()}),(a,o)=>(f(),Q(e(de),{show:B.value,"onUpdate:show":o[9]||(o[9]=l=>B.value=l),preset:"dialog",title:"批量导入邮箱",style:{width:"800px"}},{default:s(()=>[n("div",rs,[t(e(Ie),{current:h.value,status:r.value},{default:s(()=>[t(e(pe),{title:"输入数据"}),t(e(pe),{title:"预览确认"}),t(e(pe),{title:"导入结果"})]),_:1},8,["current","status"]),h.value===1?(f(),C("div",is,[t(e(fe),{type:"info",class:"mb-4"},{header:s(()=>o[10]||(o[10]=[_("导入格式说明")])),default:s(()=>[o[11]||(o[11]=n("div",null,[n("p",null,[_("每行一个邮箱账户，格式："),n("code",null,"邮箱地址----密码")]),n("p",null,[_("支持的分隔符："),n("code",null,"----"),_("、"),n("code",null,"---"),_("、"),n("code",null,"--"),_("、制表符、空格")]),n("p",null,"示例："),n("pre",null,`<EMAIL>----password123
<EMAIL>----password456`)],-1))]),_:1,__:[11]}),t(e(ge),{ref:"formRef",model:$,rules:G},{default:s(()=>[t(e(K),{label:"导入数据",path:"importText"},{default:s(()=>[t(e(ne),{value:$.importText,"onUpdate:value":o[0]||(o[0]=l=>$.importText=l),type:"textarea",placeholder:"请输入邮箱账户数据，每行一个...",rows:10,"show-count":""},null,8,["value"])]),_:1}),t(e(K),{label:"导入来源",path:"source"},{default:s(()=>[t(e(ne),{value:$.source,"onUpdate:value":o[1]||(o[1]=l=>$.source=l),placeholder:"例如：客户提供、爬虫获取等"},null,8,["value"])]),_:1}),t(e(K),{label:"标签"},{default:s(()=>[t(e(Be),{value:$.tags,"onUpdate:value":o[2]||(o[2]=l=>$.tags=l)},null,8,["value"])]),_:1}),t(e(K),{label:"描述"},{default:s(()=>[t(e(ne),{value:$.description,"onUpdate:value":o[3]||(o[3]=l=>$.description=l),placeholder:"可选，描述本次导入的目的或备注"},null,8,["value"])]),_:1}),t(e(K),null,{default:s(()=>[t(e(me),{checked:$.autoVerify,"onUpdate:checked":o[4]||(o[4]=l=>$.autoVerify=l)},{default:s(()=>o[12]||(o[12]=[_(" 导入后自动验证邮箱 ")])),_:1,__:[12]},8,["checked"])]),_:1})]),_:1},8,["model"]),n("div",us,[t(e(q),{justify:"end"},{default:s(()=>[t(e(L),{onClick:o[5]||(o[5]=l=>B.value=!1)},{default:s(()=>o[13]||(o[13]=[_("取消")])),_:1,__:[13]}),t(e(L),{type:"primary",onClick:z,loading:i.value},{default:s(()=>o[14]||(o[14]=[_(" 解析预览 ")])),_:1,__:[14]},8,["loading"])]),_:1})])])):j("",!0),h.value===2?(f(),C("div",ds,[t(e(fe),{type:M.value.summary.invalid>0?"warning":"success",class:"mb-4"},{header:s(()=>o[15]||(o[15]=[_("解析结果")])),default:s(()=>[n("div",null,[n("p",null,"总计："+I(M.value.summary.total)+" 行",1),n("p",null,"有效："+I(M.value.summary.valid)+" 个账户",1),M.value.summary.invalid>0?(f(),C("p",cs," 无效："+I(M.value.summary.invalid)+" 行（格式错误） ",1)):j("",!0)])]),_:1},8,["type"]),M.value.valid.length>0?(f(),C("div",ps,[o[16]||(o[16]=n("h4",null,"有效账户预览（前10个）：",-1)),t(e(re),{columns:J,data:M.value.valid.slice(0,10),pagination:!1,size:"small"},null,8,["data"]),M.value.valid.length>10?(f(),C("p",vs," 还有 "+I(M.value.valid.length-10)+" 个账户未显示... ",1)):j("",!0)])):j("",!0),M.value.invalid.length>0?(f(),C("div",fs,[o[17]||(o[17]=n("h4",{class:"text-red-500"},"无效数据：",-1)),t(e(Ue),{style:{"max-height":"200px"}},{default:s(()=>[(f(!0),C(_e,null,Me(M.value.invalid,(l,w)=>(f(),C("div",{key:w,class:"text-red-500 text-sm"},I(l),1))),128))]),_:1})])):j("",!0),n("div",ms,[t(e(q),{justify:"end"},{default:s(()=>[t(e(L),{onClick:o[6]||(o[6]=l=>h.value=1)},{default:s(()=>o[18]||(o[18]=[_("返回修改")])),_:1,__:[18]}),t(e(L),{type:"primary",onClick:m,disabled:M.value.valid.length===0,loading:d.value},{default:s(()=>[_(" 确认导入 ("+I(M.value.valid.length)+" 个账户) ",1)]),_:1},8,["disabled","loading"])]),_:1})])])):j("",!0),h.value===3?(f(),C("div",gs,[y.value?(f(),C("div",_s,[t(e(Ae),{status:y.value.success?"success":"error",title:y.value.success?"导入成功":"导入失败",description:y.value.message},{footer:s(()=>[y.value.success&&y.value.data?(f(),C("div",ys,[n("p",null,"操作ID："+I(y.value.data.operation_id),1),n("p",null,"总数量："+I(y.value.data.total_count),1),n("p",null,"状态："+I(y.value.data.status),1),t(e(q),{justify:"center",class:"mt-4"},{default:s(()=>[t(e(L),{onClick:P},{default:s(()=>o[19]||(o[19]=[_("查看进度")])),_:1,__:[19]}),t(e(L),{type:"primary",onClick:H},{default:s(()=>o[20]||(o[20]=[_("完成")])),_:1,__:[20]})]),_:1})])):(f(),C("div",ks,[t(e(q),{justify:"center",class:"mt-4"},{default:s(()=>[t(e(L),{onClick:o[7]||(o[7]=l=>h.value=1)},{default:s(()=>o[21]||(o[21]=[_("重新导入")])),_:1,__:[21]}),t(e(L),{onClick:o[8]||(o[8]=l=>B.value=!1)},{default:s(()=>o[22]||(o[22]=[_("关闭")])),_:1,__:[22]})]),_:1})]))]),_:1},8,["status","title","description"])])):j("",!0)])):j("",!0)])]),_:1},8,["show"]))}}),ws=ae(hs,[["__scopeId","data-v-bcaf745d"]]),xs={class:"space-y-2"},bs={class:"flex items-center"},Cs={class:"ml-2 font-medium"},$s={key:0,class:"text-sm text-gray-600 space-y-1"},Ss={key:0},Ms={key:1},Ts={key:2},Ps={key:3},Os={key:1,class:"text-sm text-red-600"},Ls=R({__name:"ProxyConfigModal",props:{visible:{type:Boolean}},emits:["update:visible","success"],setup(u,{emit:v}){const S=u,T=v,b=N(!1),h=N(),r=oe(),i=ee({enabled:!1,proxy_url:"",proxy_type:"http"}),d=N(null),$=N(!1),M=N(!1),y=N(!1),B=N(!1),G=[{label:"HTTP/HTTPS",value:"http"},{label:"SOCKS5",value:"socks5"}],J={proxy_url:[{required:!0,trigger:["blur","input"],validator:(p,a)=>i.enabled?a?/^(https?|socks5):\/\/.+/.test(a)?!0:new Error("代理地址格式错误，应为: protocol://[username:password@]host:port"):new Error("请输入代理地址"):!0}]};le(()=>S.visible,p=>{b.value=p,p&&(z(),d.value=null)}),le(b,p=>{T("update:visible",p)}),le(()=>i.enabled,()=>{d.value=null}),le(()=>i.proxy_url,()=>{d.value=null});const z=async()=>{try{console.log("[ProxyConfigModal] 开始加载代理配置...");const p=await U.getProxyConfig();console.log("[ProxyConfigModal] API响应:",p),console.log("[ProxyConfigModal] response.data:",p.data);let a=p.data;p.data&&typeof p.data.data<"u"?(a=p.data.data,console.log("[ProxyConfigModal] 检测到嵌套结构，使用response.data.data:",a)):console.log("[ProxyConfigModal] 使用直接结构，response.data:",a),a&&typeof a=="object"?(console.log("[ProxyConfigModal] 更新前的formData:",{...i}),i.enabled=!!a.enabled,i.proxy_url=String(a.proxy_url||""),i.proxy_type=String(a.proxy_type||"http"),console.log("[ProxyConfigModal] 更新后的formData:",{...i}),B.value=!!a.enabled||!!a.proxy_url):console.warn("[ProxyConfigModal] 未找到有效的代理配置数据，proxyData:",a)}catch(p){console.error("加载代理配置失败:",p),r.error("加载代理配置失败")}},m=async()=>{console.log("[ProxyConfigModal] 开始测试连接..."),$.value=!0,d.value=null;try{console.log("[ProxyConfigModal] 发送测试请求，配置:",i);const p=await U.testProxyConfig(i);console.log("[ProxyConfigModal] 测试响应原始数据:",p),console.log("[ProxyConfigModal] response.data:",p.data);let a=p.data;p.data&&typeof p.data.data<"u"?(a=p.data.data,console.log("[ProxyConfigModal] 使用嵌套结构 response.data.data:",a)):console.log("[ProxyConfigModal] 使用直接结构 response.data:",a),a?(console.log("[ProxyConfigModal] 设置测试结果:",a),console.log("[ProxyConfigModal] 测试结果详情:",{success:a.success,ip:a.ip,country:a.country,city:a.city,region:a.region,isp:a.isp,latency_ms:a.latency_ms,error:a.error}),d.value=a,a.success?r.success("代理连接测试成功"):r.warning("代理连接测试失败")):console.warn("[ProxyConfigModal] 测试数据为空")}catch(p){console.error("测试代理连接失败:",p),r.error("测试代理连接失败"),d.value={success:!1,error:"网络请求失败"}}finally{$.value=!1}},P=async()=>{if(h.value)try{await h.value.validate(),M.value=!0,await U.setProxyConfig(i),r.success("代理配置保存成功"),T("success"),b.value=!1}catch(p){console.error("保存代理配置失败:",p),p instanceof Error?r.error(`保存失败: ${p.message}`):r.error("保存代理配置失败")}finally{M.value=!1}},H=async()=>{y.value=!0;try{await U.deleteProxyConfig(),Object.assign(i,{enabled:!1,proxy_url:"",proxy_type:"http"}),d.value=null,B.value=!1,r.success("代理配置已重置"),T("success")}catch(p){console.error("重置代理配置失败:",p),r.error("重置代理配置失败")}finally{y.value=!1}};return(p,a)=>(f(),Q(e(de),{show:b.value,"onUpdate:show":a[4]||(a[4]=o=>b.value=o),preset:"dialog",title:"代理配置",style:{width:"600px"}},{action:s(()=>[t(e(q),null,{default:s(()=>[t(e(L),{onClick:a[3]||(a[3]=o=>b.value=!1)},{default:s(()=>a[7]||(a[7]=[_("取消")])),_:1,__:[7]}),i.enabled||B.value?(f(),Q(e(L),{key:0,type:"error",ghost:"",onClick:H,loading:y.value},{default:s(()=>a[8]||(a[8]=[_(" 重置 ")])),_:1,__:[8]},8,["loading"])):j("",!0),t(e(L),{type:"primary",onClick:P,loading:M.value},{default:s(()=>a[9]||(a[9]=[_(" 保存 ")])),_:1,__:[9]},8,["loading"])]),_:1})]),default:s(()=>[t(e(ge),{ref_key:"formRef",ref:h,model:i,rules:J,"label-placement":"left","label-width":"120px"},{default:s(()=>[t(e(K),{label:"启用代理",path:"enabled"},{default:s(()=>[t(e(je),{value:i.enabled,"onUpdate:value":a[0]||(a[0]=o=>i.enabled=o)},null,8,["value"]),a[5]||(a[5]=n("span",{class:"ml-2 text-gray-500"},"启用后所有网络请求将通过代理服务器",-1))]),_:1,__:[5]}),i.enabled?(f(),C(_e,{key:0},[t(e(K),{label:"代理类型",path:"proxy_type"},{default:s(()=>[t(e(te),{value:i.proxy_type,"onUpdate:value":a[1]||(a[1]=o=>i.proxy_type=o),options:G,placeholder:"选择代理类型"},null,8,["value"])]),_:1}),t(e(K),{label:"代理地址",path:"proxy_url"},{suffix:s(()=>[t(e(L),{text:"",type:"primary",loading:$.value,onClick:m,disabled:!1},{default:s(()=>a[6]||(a[6]=[_(" 测试连接 ")])),_:1,__:[6]},8,["loading"])]),default:s(()=>[t(e(ne),{value:i.proxy_url,"onUpdate:value":a[2]||(a[2]=o=>i.proxy_url=o),placeholder:"例如: http://username:<EMAIL>:8080",clearable:""},null,8,["value"])]),_:1}),d.value?(f(),Q(e(K),{key:0,label:"测试结果"},{default:s(()=>[t(e(se),{size:"small",class:We(d.value.success?"border-green-200":"border-red-200")},{default:s(()=>[n("div",xs,[n("div",bs,[t(e(E),{color:d.value.success?"#10b981":"#ef4444",size:"16"},{default:s(()=>[d.value.success?(f(),Q(e(ke),{key:0})):(f(),Q(e(Te),{key:1}))]),_:1},8,["color"]),n("span",Cs,I(d.value.success?"连接成功":"连接失败"),1)]),d.value.success?(f(),C("div",$s,[d.value.ip?(f(),C("div",Ss,"IP地址: "+I(d.value.ip),1)):j("",!0),d.value.country?(f(),C("div",Ms," 位置: "+I(d.value.country)+" "+I(d.value.region)+" "+I(d.value.city),1)):j("",!0),d.value.isp?(f(),C("div",Ts,"ISP: "+I(d.value.isp),1)):j("",!0),d.value.latency_ms?(f(),C("div",Ps,"延迟: "+I(d.value.latency_ms)+"ms",1)):j("",!0)])):j("",!0),!d.value.success&&d.value.error?(f(),C("div",Os," 错误: "+I(d.value.error),1)):j("",!0)])]),_:1},8,["class"])]),_:1})):j("",!0)],64)):j("",!0)]),_:1},8,["model"])]),_:1},8,["show"]))}}),Ds=ae(Ls,[["__scopeId","data-v-7112bc34"]]),Ns={class:"verification-task-modal"},zs={class:"modal-actions"},Is=R({__name:"VerificationTaskModal",props:{visible:{type:Boolean},selectedAccounts:{}},emits:["update:visible","success"],setup(u,{emit:v}){const S=u,T=v,b=oe(),h=N(!1),r=ee({verificationType:"login",concurrentLimit:10,retryLimit:3,timeoutSeconds:30,skipRecentVerified:!0,updateAccountStatus:!0}),i=Y({get:()=>S.visible,set:z=>T("update:visible",z)}),d=Y(()=>S.selectedAccounts.length),$=Y(()=>{const z=d.value,m=r.concurrentLimit,P=r.timeoutSeconds+5;if(z===0)return"0分钟";const H=Math.ceil(z/m)*P,p=Math.ceil(H/60);if(p<60)return`约 ${p} 分钟`;{const a=Math.floor(p/60),o=p%60;return`约 ${a} 小时 ${o} 分钟`}}),M=[{label:"登录验证",value:"login"},{label:"健康检查",value:"health_check"},{label:"会话测试",value:"session_test"}],y={verificationType:{required:!0,message:"请选择验证类型",trigger:"change"},concurrentLimit:{required:!0,type:"number",min:1,max:50,message:"并发限制必须在1-50之间",trigger:"blur"},retryLimit:{required:!0,type:"number",min:0,max:10,message:"重试次数必须在0-10之间",trigger:"blur"}},B=z=>{const m=M.find(P=>P.value===z);return(m==null?void 0:m.label)||z},G=async()=>{if(d.value===0){b.error("请先选择要验证的账户");return}h.value=!0;try{const m={account_emails:S.selectedAccounts.map(p=>p.email),verification_type:r.verificationType,concurrent_limit:r.concurrentLimit,retry_limit:r.retryLimit},P=await U.startVerificationTask(m);console.log("验证任务响应:",JSON.stringify(P));const H=P.data||P;H.success?(b.success("验证任务已创建，正在后台执行"),T("success",H.data),i.value=!1):b.error(H.message||"创建验证任务失败")}catch(z){b.error(z.message||"创建验证任务失败"),console.error(z)}finally{h.value=!1}},J=()=>{r.verificationType="login",r.concurrentLimit=10,r.retryLimit=3,r.timeoutSeconds=30,r.skipRecentVerified=!0,r.updateAccountStatus=!0};return le(i,z=>{z||J()}),(z,m)=>(f(),Q(e(de),{show:i.value,"onUpdate:show":m[7]||(m[7]=P=>i.value=P),preset:"dialog",title:"启动验证任务",style:{width:"600px"}},{default:s(()=>[n("div",Ns,[t(e(fe),{type:"info",class:"mb-4"},{header:s(()=>m[8]||(m[8]=[_("验证任务说明")])),default:s(()=>[m[9]||(m[9]=n("div",null,[n("p",null,"将对选中的邮箱账户进行登录验证，检查账户的有效性。"),n("p",null,"验证过程将在后台异步执行，您可以在任务控制面板中查看进度。")],-1))]),_:1,__:[9]}),t(e(ge),{ref:"formRef",model:r,rules:y,"label-placement":"left","label-width":"120px"},{default:s(()=>[t(e(K),{label:"选中账户数"},{default:s(()=>[t(e(Z),{type:"info"},{default:s(()=>[_(I(d.value)+" 个账户",1)]),_:1})]),_:1}),t(e(K),{label:"验证类型",path:"verificationType"},{default:s(()=>[t(e(te),{value:r.verificationType,"onUpdate:value":m[0]||(m[0]=P=>r.verificationType=P),options:M,placeholder:"请选择验证类型"},null,8,["value"])]),_:1}),t(e(K),{label:"并发限制",path:"concurrentLimit"},{feedback:s(()=>m[10]||(m[10]=[n("span",{class:"text-gray-500"},"建议设置为 5-20，过高可能导致IP被封",-1)])),default:s(()=>[t(e(ve),{value:r.concurrentLimit,"onUpdate:value":m[1]||(m[1]=P=>r.concurrentLimit=P),min:1,max:50,placeholder:"同时验证的账户数量"},null,8,["value"])]),_:1}),t(e(K),{label:"重试次数",path:"retryLimit"},{default:s(()=>[t(e(ve),{value:r.retryLimit,"onUpdate:value":m[2]||(m[2]=P=>r.retryLimit=P),min:0,max:10,placeholder:"验证失败时的重试次数"},null,8,["value"])]),_:1}),t(e(K),{label:"超时设置"},{default:s(()=>[t(e(ve),{value:r.timeoutSeconds,"onUpdate:value":m[3]||(m[3]=P=>r.timeoutSeconds=P),min:10,max:300,placeholder:"单个账户验证超时时间（秒）"},null,8,["value"])]),_:1}),t(e(K),null,{default:s(()=>[t(e(me),{checked:r.skipRecentVerified,"onUpdate:checked":m[4]||(m[4]=P=>r.skipRecentVerified=P)},{default:s(()=>m[11]||(m[11]=[_(" 跳过最近24小时内已验证的账户 ")])),_:1,__:[11]},8,["checked"])]),_:1}),t(e(K),null,{default:s(()=>[t(e(me),{checked:r.updateAccountStatus,"onUpdate:checked":m[5]||(m[5]=P=>r.updateAccountStatus=P)},{default:s(()=>m[12]||(m[12]=[_(" 根据验证结果自动更新账户状态 ")])),_:1,__:[12]},8,["checked"])]),_:1})]),_:1},8,["model"]),t(e(se),{title:"预估信息",size:"small",class:"mb-4"},{default:s(()=>[t(e(Re),{column:2,size:"small"},{default:s(()=>[t(e(ie),{label:"预计耗时"},{default:s(()=>[_(I($.value),1)]),_:1}),t(e(ie),{label:"并发数"},{default:s(()=>[_(I(r.concurrentLimit),1)]),_:1}),t(e(ie),{label:"总账户数"},{default:s(()=>[_(I(d.value),1)]),_:1}),t(e(ie),{label:"验证类型"},{default:s(()=>[_(I(B(r.verificationType)),1)]),_:1})]),_:1})]),_:1}),n("div",zs,[t(e(q),{justify:"end"},{default:s(()=>[t(e(L),{onClick:m[6]||(m[6]=P=>i.value=!1)},{default:s(()=>m[13]||(m[13]=[_("取消")])),_:1,__:[13]}),t(e(L),{type:"primary",onClick:G,loading:h.value,disabled:d.value===0},{default:s(()=>m[14]||(m[14]=[_(" 启动验证任务 ")])),_:1,__:[14]},8,["loading","disabled"])]),_:1})])])]),_:1},8,["show"]))}}),Bs=ae(Is,[["__scopeId","data-v-c84d343d"]]),Us={class:"task-control-panel"},As=R({__name:"TaskControlPanel",props:{visible:{type:Boolean}},emits:["update:visible","refresh"],setup(u,{emit:v}){const S=u,T=v,b=oe(),h=Ve(),r=N(!1),i=N(!1),d=N([]),$=N([]),M=Y({get:()=>S.visible,set:l=>T("update:visible",l)}),y=Y(()=>{const l={running:0,stopped:0,paused:0,error:0};return d.value.forEach(w=>{switch(w.status){case"running":l.running++;break;case"stopped":l.stopped++;break;case"paused":l.paused++;break;case"error":l.error++;break}}),l}),B=ee({page:1,pageSize:10,itemCount:0,showSizePicker:!0,pageSizes:[10,20,50]}),G=Y(()=>[{title:"任务名称",key:"task_name",width:200,ellipsis:{tooltip:!0}},{title:"任务类型",key:"task_type",width:120,render:l=>({batch_verification:"批量验证",health_check:"健康检查",cleanup:"清理任务"})[l.task_type]||l.task_type},{title:"状态",key:"status",width:100,render:l=>{const V={running:{text:"运行中",type:"success"},stopped:{text:"已停止",type:"info"},paused:{text:"已暂停",type:"warning"},error:{text:"错误",type:"error"}}[l.status]||{text:l.status,type:"info"};return D(Z,{type:V.type},{default:()=>V.text})}},{title:"运行次数",key:"run_count",width:100},{title:"错误次数",key:"error_count",width:100,render:l=>l.error_count>0?D(Z,{type:"error"},{default:()=>l.error_count}):l.error_count},{title:"最后运行",key:"last_run_at",width:150,render:l=>l.last_run_at?new Date(l.last_run_at).toLocaleString():"-"},{title:"操作",key:"actions",width:200,render:l=>D(q,[D(L,{size:"small",type:l.status==="running"?"warning":"primary",onClick:()=>P(l)},{default:()=>l.status==="running"?"暂停":"启动",icon:()=>D(E,null,{default:()=>l.status==="running"?D(It):D(xe)})}),D(L,{size:"small",type:"error",onClick:()=>H(l),disabled:l.status==="stopped"},{default:()=>"停止",icon:()=>D(E,null,{default:()=>D(be)})}),D(L,{size:"small",onClick:()=>p(l)},{default:()=>"重置"})])}]),J=Y(()=>[{title:"操作ID",key:"operation_id",width:120,ellipsis:{tooltip:!0}},{title:"操作类型",key:"operation_type",width:100,render:l=>({import:"批量导入",verify:"批量验证",disable:"批量禁用",enable:"批量启用"})[l.operation_type]||l.operation_type},{title:"状态",key:"status",width:100,render:l=>{const V={pending:{text:"等待中",type:"info"},running:{text:"运行中",type:"warning"},completed:{text:"已完成",type:"success"},failed:{text:"失败",type:"error"},cancelled:{text:"已取消",type:"info"}}[l.status]||{text:l.status,type:"info"};return D(Z,{type:V.type},{default:()=>V.text})}},{title:"进度",key:"progress",width:150,render:l=>{const w=l.total_count>0?Math.round(l.processed_count/l.total_count*100):0;return D("div",[D(Fe,{type:"line",percentage:w,showIndicator:!1,height:8}),D("div",{class:"text-xs text-gray-500 mt-1"},`${l.processed_count}/${l.total_count}`)])}},{title:"成功/失败",key:"result",width:100,render:l=>D("div",{class:"text-xs"},[D("div",{class:"text-green-600"},`成功: ${l.success_count}`),D("div",{class:"text-red-600"},`失败: ${l.failed_count}`)])},{title:"创建时间",key:"created_at",width:150,render:l=>new Date(l.created_at).toLocaleString()}]),z=async()=>{r.value=!0;try{const l=await U.getTaskSchedulerStatus();l.success&&l.data&&(d.value=l.data)}catch(l){b.error("加载任务状态失败"),console.error(l)}finally{r.value=!1}},m=async()=>{i.value=!0,i.value=!1},P=async l=>{try{const w=l.status==="running"?"pause":"start";await U.controlTask({action:w,task_id:l.task_name}),b.success(`任务${w==="start"?"启动":"暂停"}成功`),z(),T("refresh")}catch(w){b.error(w.message||"操作失败")}},H=async l=>{try{await U.controlTask({action:"stop",task_id:l.task_name}),b.success("任务停止成功"),z(),T("refresh")}catch(w){b.error(w.message||"停止任务失败")}},p=async l=>{h.warning({title:"确认重置",content:`确定要重置任务 "${l.task_name}" 吗？这将清除运行计数和错误记录。`,positiveText:"确认",negativeText:"取消",onPositiveClick:async()=>{try{await U.controlTask({action:"reset",task_id:l.task_name}),b.success("任务重置成功"),z(),T("refresh")}catch(w){b.error(w.message||"重置任务失败")}}})},a=async()=>{h.info({title:"确认启动",content:"确定要启动所有已停止的任务吗？",positiveText:"确认",negativeText:"取消",onPositiveClick:async()=>{const l=d.value.filter(w=>w.status==="stopped");for(const w of l)try{await U.controlTask({action:"start",task_id:w.task_name})}catch(V){console.error(`启动任务 ${w.task_name} 失败:`,V)}b.success("批量启动完成"),z(),T("refresh")}})},o=async()=>{h.warning({title:"确认停止",content:"确定要停止所有运行中的任务吗？",positiveText:"确认",negativeText:"取消",onPositiveClick:async()=>{const l=d.value.filter(w=>w.status==="running");for(const w of l)try{await U.controlTask({action:"stop",task_id:w.task_name})}catch(V){console.error(`停止任务 ${w.task_name} 失败:`,V)}b.success("批量停止完成"),z(),T("refresh")}})};return ye(()=>{z(),m()}),(l,w)=>(f(),Q(e(Ee),{show:M.value,"onUpdate:show":w[0]||(w[0]=V=>M.value=V),width:800,placement:"right"},{default:s(()=>[t(e(qe),{title:"定时任务控制面板"},{default:s(()=>[n("div",Us,[t(e(se),{title:"任务状态概览",class:"mb-4"},{default:s(()=>[t(e(Se),{cols:4,"x-gap":12},{default:s(()=>[t(e(W),null,{default:s(()=>[t(e(X),{label:"运行中",value:y.value.running},null,8,["value"])]),_:1}),t(e(W),null,{default:s(()=>[t(e(X),{label:"已停止",value:y.value.stopped},null,8,["value"])]),_:1}),t(e(W),null,{default:s(()=>[t(e(X),{label:"已暂停",value:y.value.paused},null,8,["value"])]),_:1}),t(e(W),null,{default:s(()=>[t(e(X),{label:"错误",value:y.value.error},null,8,["value"])]),_:1})]),_:1})]),_:1}),t(e(se),{title:"任务列表"},{"header-extra":s(()=>[t(e(q),null,{default:s(()=>[t(e(L),{onClick:z,loading:r.value},{icon:s(()=>[t(e(E),null,{default:s(()=>[t(e(ue))]),_:1})]),default:s(()=>[w[1]||(w[1]=_(" 刷新 "))]),_:1,__:[1]},8,["loading"]),t(e(L),{type:"primary",onClick:a},{icon:s(()=>[t(e(E),null,{default:s(()=>[t(e(xe))]),_:1})]),default:s(()=>[w[2]||(w[2]=_(" 全部启动 "))]),_:1,__:[2]}),t(e(L),{onClick:o},{icon:s(()=>[t(e(E),null,{default:s(()=>[t(e(be))]),_:1})]),default:s(()=>[w[3]||(w[3]=_(" 全部停止 "))]),_:1,__:[3]})]),_:1})]),default:s(()=>[t(e(re),{columns:G.value,data:d.value,loading:r.value,pagination:!1,"row-key":V=>V.id},null,8,["columns","data","loading","row-key"])]),_:1}),t(e(se),{title:"批量操作历史",class:"mt-4"},{"header-extra":s(()=>[t(e(L),{onClick:m,loading:i.value},{icon:s(()=>[t(e(E),null,{default:s(()=>[t(e(ue))]),_:1})]),default:s(()=>[w[4]||(w[4]=_(" 刷新 "))]),_:1,__:[4]},8,["loading"])]),default:s(()=>[t(e(re),{columns:J.value,data:$.value,loading:i.value,pagination:B,"row-key":V=>V.id},null,8,["columns","data","loading","pagination","row-key"])]),_:1})])]),_:1})]),_:1},8,["show"]))}}),js=ae(As,[["__scopeId","data-v-252d8a66"]]),$e={getTaskLogs(u){return F.get("/mailbox/task-logs",{params:u})},getTaskLogDetail(u){return F.get(`/mailbox/task-logs/${u}/details`)},createTaskLog(u){return F.post("/mailbox/task-logs",u)},updateTaskLog(u,v){return F.put(`/mailbox/task-logs/${u}`,v)}},Rs={class:"filter-section mb-4"},Vs={key:0,class:"detail-log-container"},Fs={class:"log-header mb-4"},Es={class:"text-gray-600"},qs={class:"text-gray-500"},Js={class:"step-content"},Hs={class:"mb-2"},Ks={key:0,class:"text-sm text-gray-500 mb-2"},Gs={key:1,class:"text-red-500 mb-2"},Qs={key:2,class:"response-data"},Ws={key:1,class:"no-steps"},Xs={key:0,class:"mt-4"},Ys=R({__name:"TaskLogPanel",setup(u){const v=oe(),S=N(!1),T=N([]),b=N(!1),h=N(!1),r=N(null),i=ee({dateRange:[Date.now()-4320*60*1e3,Date.now()],operationType:null,status:null,email:""}),d=ee({page:1,pageSize:20,itemCount:0,showSizePicker:!0,pageSizes:[10,20,50,100]}),$=[{label:"批量导入",value:"import"},{label:"邮箱验证",value:"verify"}],M=[{label:"成功",value:"success"},{label:"失败",value:"failed"},{label:"进行中",value:"running"},{label:"等待中",value:"pending"}],y=g=>({success:{type:"success",text:"成功",icon:ke},failed:{type:"error",text:"失败",icon:Te},running:{type:"info",text:"进行中",icon:Ce},pending:{type:"warning",text:"等待中",icon:Ce}})[g]||{type:"default",text:g,icon:we},B=g=>({import:{text:"批量导入",icon:Pe,color:"#2080f0"},verify:{text:"邮箱验证",icon:ut,color:"#18a058"}})[g]||{text:g,icon:we,color:"#666"},G=Y(()=>[{title:"时间",key:"start_time",width:160,render:g=>new Date(g.start_time).toLocaleString()},{title:"操作类型",key:"operation_type",width:120,render:g=>{const k=B(g.operation_type);return D(q,{align:"center"},{default:()=>[D(E,{color:k.color},{default:()=>D(k.icon)}),D("span",k.text)]})}},{title:"邮箱地址",key:"email",width:200,ellipsis:{tooltip:!0}},{title:"状态",key:"status",width:100,render:g=>{const k=y(g.status);return D(q,{align:"center"},{default:()=>[D(E,{},{default:()=>D(k.icon)}),D(Z,{type:k.type},{default:()=>k.text})]})}},{title:"耗时",key:"duration_ms",width:100,render:g=>g.duration_ms?g.duration_ms<1e3?`${g.duration_ms}ms`:`${(g.duration_ms/1e3).toFixed(1)}s`:"-"},{title:"详细日志",key:"detail_log",width:200,render:g=>D(L,{size:"small",type:"primary",text:!0,onClick:()=>a(g)},{default:()=>"查看详细日志"})},{title:"批次ID",key:"batch_id",width:120,render:g=>g.batch_id||"-"}]),J=async()=>{try{S.value=!0;const g={page:d.page,page_size:d.pageSize};i.operationType&&(g.operation_type=i.operationType),i.status&&(g.status=i.status),i.email&&(g.email=i.email),i.dateRange&&i.dateRange.length===2&&(g.start_time=new Date(i.dateRange[0]).toISOString(),g.end_time=new Date(i.dateRange[1]).toISOString());const k=await $e.getTaskLogs(g),O=k.data||k;O.success&&O.data?(T.value=O.data.items||[],d.itemCount=O.data.total||0):(T.value=[],d.itemCount=0)}catch(g){v.error("加载任务日志失败"),console.error(g),T.value=[],d.itemCount=0}finally{S.value=!1}},z=()=>{J()},m=()=>{d.page=1,J()},P=()=>{i.dateRange=[Date.now()-4320*60*1e3,Date.now()],i.operationType=null,i.status=null,i.email="",d.page=1,J()},H=g=>{d.page=g,J()},p=g=>{d.pageSize=g,d.page=1,J()},a=async g=>{b.value=!0,h.value=!0;try{const k=await $e.getTaskLogDetail(g.id),O=k.data||k;O.success&&O.data?r.value=O.data:(v.error("获取详细日志失败："+(O.message||"未知错误")),r.value=null)}catch(k){v.error("获取详细日志失败"),console.error(k),r.value=null}finally{h.value=!1}},o=g=>new Date(g).toLocaleString(),l=g=>({success:"success",failed:"error",running:"info",pending:"warning"})[g]||"default",w=g=>({success:"成功",failed:"失败",running:"进行中",pending:"等待中"})[g]||g,V=g=>({success:"success",failed:"error",running:"info"})[g]||"default";return ye(()=>{J()}),(g,k)=>(f(),Q(e(se),{title:"任务日志",class:"task-log-panel"},{"header-extra":s(()=>[t(e(q),null,{default:s(()=>[t(e(L),{onClick:z,size:"small"},{icon:s(()=>[t(e(E),null,{default:s(()=>[t(e(ue))]),_:1})]),default:s(()=>[k[6]||(k[6]=_(" 刷新 "))]),_:1,__:[6]})]),_:1})]),default:s(()=>[n("div",Rs,[t(e(q),null,{default:s(()=>[t(e(Je),{value:i.dateRange,"onUpdate:value":k[0]||(k[0]=O=>i.dateRange=O),type:"daterange",placeholder:"选择时间范围","default-value":[Date.now()-4320*60*1e3,Date.now()],style:{width:"240px"}},null,8,["value","default-value"]),t(e(te),{value:i.operationType,"onUpdate:value":k[1]||(k[1]=O=>i.operationType=O),options:$,placeholder:"操作类型",clearable:"",style:{width:"120px"}},null,8,["value"]),t(e(te),{value:i.status,"onUpdate:value":k[2]||(k[2]=O=>i.status=O),options:M,placeholder:"状态",clearable:"",style:{width:"120px"}},null,8,["value"]),t(e(ne),{value:i.email,"onUpdate:value":k[3]||(k[3]=O=>i.email=O),placeholder:"邮箱地址",clearable:"",style:{width:"200px"}},null,8,["value"]),t(e(L),{onClick:m,type:"primary",size:"small"},{default:s(()=>k[7]||(k[7]=[_("筛选")])),_:1,__:[7]}),t(e(L),{onClick:P,size:"small"},{default:s(()=>k[8]||(k[8]=[_("重置")])),_:1,__:[8]})]),_:1})]),t(e(re),{columns:G.value,data:T.value,loading:S.value,pagination:d,"row-key":O=>O.id,"onUpdate:page":H,"onUpdate:pageSize":p},null,8,["columns","data","loading","pagination","row-key"]),t(e(de),{show:b.value,"onUpdate:show":k[5]||(k[5]=O=>b.value=O),preset:"card",title:"详细日志",style:{width:"80%","max-width":"1200px"},"mask-closable":!1},{"header-extra":s(()=>[t(e(L),{onClick:k[4]||(k[4]=O=>b.value=!1),quaternary:"",circle:""},{icon:s(()=>[t(e(E),null,{default:s(()=>[t(e(kt))]),_:1})]),_:1})]),default:s(()=>[t(e(He),{show:h.value},{default:s(()=>[r.value?(f(),C("div",Vs,[n("div",Fs,[t(e(q),null,{default:s(()=>[t(e(Z),{type:l(r.value.taskLog.status)},{default:s(()=>[_(I(w(r.value.taskLog.status)),1)]),_:1},8,["type"]),n("span",Es,I(r.value.taskLog.email),1),n("span",qs,I(o(r.value.taskLog.start_time)),1)]),_:1})]),r.value.detail.steps&&r.value.detail.steps.length>0?(f(),Q(e(Ke),{key:0},{default:s(()=>[(f(!0),C(_e,null,Me(r.value.detail.steps,(O,ce)=>(f(),Q(e(Ge),{key:ce,type:V(O.status),title:O.step,time:o(O.timestamp)},{default:s(()=>[n("div",Js,[n("p",Hs,I(O.details),1),O.duration?(f(),C("div",Ks," 耗时: "+I(O.duration)+"ms ",1)):j("",!0),O.error_message?(f(),C("div",Gs," 错误: "+I(O.error_message),1)):j("",!0),O.response_data&&Object.keys(O.response_data).length>0?(f(),C("div",Qs,[t(e(he),{code:JSON.stringify(O.response_data,null,2),language:"json","show-line-numbers":""},null,8,["code"])])):j("",!0)])]),_:2},1032,["type","title","time"]))),128))]),_:1})):(f(),C("div",Ws,[k[10]||(k[10]=n("p",{class:"text-gray-500"},"暂无详细执行步骤记录",-1)),r.value.taskLog.error_message?(f(),C("div",Xs,[k[9]||(k[9]=n("h4",{class:"mb-2"},"错误信息:",-1)),t(e(he),{code:r.value.taskLog.error_message,language:"text"},null,8,["code"])])):j("",!0)]))])):j("",!0)]),_:1},8,["show"])]),_:1},8,["show"])]),_:1}))}}),Zs=ae(Ys,[["__scopeId","data-v-5b404f43"]]),el={class:"mailbox-management"},tl={class:"filter-section mb-4"},sl={class:"statistics-section mb-4"},ll={class:"batch-operations mb-4"},al=R({__name:"MailboxManagement",setup(u){const v=oe(),S=N(!1),T=N([]),b=N([]),h=N([]),r=N(!1),i=N(!1),d=N(!1),$=N(!1),M=N({id:0,stat_date:"",total_accounts:0,active_accounts:0,verified_accounts:0,failed_accounts:0,disabled_accounts:0,new_imports_today:0,verification_success_rate:0,avg_verification_time_ms:0,created_at:""}),y=ee({status:[],verification_status:[],import_source:[],tags:[]}),B=ee({page:1,pageSize:20,itemCount:0,showSizePicker:!0,pageSizes:[10,20,50,100]}),G=U.getStatusOptions(),J=U.getVerificationStatusOptions(),z=U.getImportSourceOptions(),m=Y(()=>[{type:"selection",multiple:!0},{title:"邮箱地址",key:"email",width:200,ellipsis:{tooltip:!0}},{title:"登录状态",key:"login_status",width:100,render:x=>{const c=U.formatStatus(x.login_status);return D(Z,{type:c.type},{default:()=>c.text})}},{title:"验证状态",key:"verification_status",width:100,render:x=>{const c=U.formatStatus(x.verification_status);return D(Z,{type:c.type},{default:()=>c.text})}},{title:"导入来源",key:"import_source",width:100},{title:"创建时间",key:"created_at",width:150,render:x=>new Date(x.created_at).toLocaleString()},{title:"最后验证",key:"last_verification_time",width:150,render:x=>x.last_verification_time?new Date(x.last_verification_time).toLocaleString():"-"},{title:"操作",key:"actions",width:150,render:x=>D(q,{},{default:()=>[D(L,{size:"small",type:x.is_disabled?"success":"warning",onClick:()=>Ne(x)},{default:()=>x.is_disabled?"启用":"禁用"}),D(L,{size:"small",type:"error",onClick:()=>De(x)},{default:()=>"删除"})]})}]),P=async()=>{S.value=!0;try{const x={page:B.page,page_size:B.pageSize,status:y.status.length>0?y.status:void 0,verification_status:y.verification_status.length>0?y.verification_status:void 0,import_source:y.import_source.length>0?y.import_source:void 0,tags:y.tags.length>0?y.tags:void 0},c=await U.getAccountsList(x);console.log("加载账户列表响应:",JSON.stringify(c));const A=c.data||c;A.success&&A.data?(T.value=A.data.Items||A.data.items||[],B.itemCount=A.data.Total||A.data.total||0):(T.value=[],B.itemCount=0)}catch(x){v.error("加载账户列表失败"),console.error(x),T.value=[],B.itemCount=0}finally{S.value=!1}},H=async()=>{try{const x=await U.getMailboxStatistics(),c=x.data||x;c.success&&c.data&&(M.value=c.data)}catch(x){console.error("加载统计信息失败:",x)}},p=()=>{P(),H()},a=()=>{B.page=1,P()},o=()=>{y.status=[],y.verification_status=[],y.import_source=[],y.tags=[],B.page=1,P()},l=x=>{B.page=x,P()},w=x=>{B.pageSize=x,B.page=1,P()},V=()=>{v.success("批量导入任务已创建"),p()},g=()=>{v.success("代理配置已保存")},k=()=>{v.success("验证任务已创建"),p()},O=x=>{h.value=x,b.value=(T.value||[]).filter(c=>x.includes(c.id))},ce=()=>{if(b.value.length===0){v.warning("请先选择要验证的账户");return}d.value=!0},Oe=async()=>{if(h.value.length===0){v.warning("请先选择要禁用的账户");return}try{await U.batchDisableAccounts(h.value),v.success(`成功禁用 ${h.value.length} 个账户`),h.value=[],b.value=[],p()}catch(x){v.error("批量禁用失败"),console.error(x)}},Le=async()=>{if(h.value.length===0){v.warning("请先选择要删除的账户");return}try{await U.batchDeleteAccounts(h.value),v.success(`成功删除 ${h.value.length} 个账户`),h.value=[],b.value=[],p()}catch(x){v.error("批量删除失败"),console.error(x)}},De=async x=>{try{await U.deleteAccount(x.id),v.success(`成功删除账户 ${x.email}`),p()}catch(c){v.error(`删除账户 ${x.email} 失败`),console.error(c)}},Ne=async x=>{try{const c=!x.is_disabled;await U.toggleAccountStatus(x.id,c),v.success(`成功${c?"禁用":"启用"}账户 ${x.email}`),p()}catch(c){v.error(`${x.is_disabled?"启用":"禁用"}账户失败`),console.error(c)}},ze=()=>{H()};return ye(()=>{p()}),(x,c)=>(f(),C("div",el,[t(e(se),{title:"邮箱管理",class:"mb-4"},{"header-extra":s(()=>[t(e(q),null,{default:s(()=>[t(e(L),{type:"primary",onClick:c[0]||(c[0]=A=>r.value=!0)},{icon:s(()=>[t(e(E),null,{default:s(()=>[t(e(Pe))]),_:1})]),default:s(()=>[c[9]||(c[9]=_(" 批量导入 "))]),_:1,__:[9]}),t(e(L),{onClick:c[1]||(c[1]=A=>i.value=!0)},{icon:s(()=>[t(e(E),null,{default:s(()=>[t(e(Qt))]),_:1})]),default:s(()=>[c[10]||(c[10]=_(" 代理配置 "))]),_:1,__:[10]}),t(e(L),{onClick:p},{icon:s(()=>[t(e(E),null,{default:s(()=>[t(e(ue))]),_:1})]),default:s(()=>[c[11]||(c[11]=_(" 刷新 "))]),_:1,__:[11]})]),_:1})]),default:s(()=>[n("div",tl,[t(e(q),null,{default:s(()=>[t(e(te),{value:y.status,"onUpdate:value":c[2]||(c[2]=A=>y.status=A),options:e(G),placeholder:"登录状态",clearable:"",multiple:"",style:{width:"150px"}},null,8,["value","options"]),t(e(te),{value:y.verification_status,"onUpdate:value":c[3]||(c[3]=A=>y.verification_status=A),options:e(J),placeholder:"验证状态",clearable:"",multiple:"",style:{width:"150px"}},null,8,["value","options"]),t(e(te),{value:y.import_source,"onUpdate:value":c[4]||(c[4]=A=>y.import_source=A),options:e(z),placeholder:"导入来源",clearable:"",multiple:"",style:{width:"150px"}},null,8,["value","options"]),t(e(L),{onClick:a,type:"primary"},{default:s(()=>c[12]||(c[12]=[_("筛选")])),_:1,__:[12]}),t(e(L),{onClick:o},{default:s(()=>c[13]||(c[13]=[_("重置")])),_:1,__:[13]})]),_:1})]),n("div",sl,[t(e(Se),{cols:6,"x-gap":12},{default:s(()=>[t(e(W),null,{default:s(()=>[t(e(X),{label:"总账户数",value:M.value.total_accounts},null,8,["value"])]),_:1}),t(e(W),null,{default:s(()=>[t(e(X),{label:"活跃账户",value:M.value.active_accounts},null,8,["value"])]),_:1}),t(e(W),null,{default:s(()=>[t(e(X),{label:"已验证",value:M.value.verified_accounts},null,8,["value"])]),_:1}),t(e(W),null,{default:s(()=>[t(e(X),{label:"验证失败",value:M.value.failed_accounts},null,8,["value"])]),_:1}),t(e(W),null,{default:s(()=>[t(e(X),{label:"已禁用",value:M.value.disabled_accounts},null,8,["value"])]),_:1}),t(e(W),null,{default:s(()=>[t(e(X),{label:"验证成功率",value:`${M.value.verification_success_rate.toFixed(1)}%`},null,8,["value"])]),_:1})]),_:1})]),n("div",ll,[t(e(q),null,{default:s(()=>[t(e(L),{type:"primary",disabled:h.value.length===0,onClick:ce},{icon:s(()=>[t(e(E),null,{default:s(()=>[t(e(ke))]),_:1})]),default:s(()=>[_(" 批量验证 ("+I(h.value.length)+") ",1)]),_:1},8,["disabled"]),t(e(L),{type:"warning",disabled:h.value.length===0,onClick:Oe},{icon:s(()=>[t(e(E),null,{default:s(()=>[t(e(tt))]),_:1})]),default:s(()=>[c[14]||(c[14]=_(" 批量禁用 "))]),_:1,__:[14]},8,["disabled"]),t(e(L),{type:"error",disabled:h.value.length===0,onClick:Le},{icon:s(()=>[t(e(E),null,{default:s(()=>[t(e(ns))]),_:1})]),default:s(()=>[c[15]||(c[15]=_(" 批量删除 "))]),_:1,__:[15]},8,["disabled"])]),_:1})]),t(e(re),{columns:m.value,data:T.value,loading:S.value,pagination:B,"row-key":A=>A.id,"checked-row-keys":h.value,"onUpdate:checkedRowKeys":O,"onUpdate:page":l,"onUpdate:pageSize":w},null,8,["columns","data","loading","pagination","row-key","checked-row-keys"])]),_:1}),t(js,{visible:$.value,"onUpdate:visible":c[5]||(c[5]=A=>$.value=A),onRefresh:ze},null,8,["visible"]),t(ws,{visible:r.value,"onUpdate:visible":c[6]||(c[6]=A=>r.value=A),onSuccess:V},null,8,["visible"]),t(Ds,{visible:i.value,"onUpdate:visible":c[7]||(c[7]=A=>i.value=A),onSuccess:g},null,8,["visible"]),t(Bs,{visible:d.value,"onUpdate:visible":c[8]||(c[8]=A=>d.value=A),"selected-accounts":b.value,onSuccess:k},null,8,["visible","selected-accounts"]),t(Zs,{class:"mt-4"})]))}}),il=ae(al,[["__scopeId","data-v-d69435f4"]]);export{il as default};
