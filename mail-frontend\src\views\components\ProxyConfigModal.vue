<template>
  <n-modal v-model:show="modalVisible" preset="dialog" title="代理配置" style="width: 600px">
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="120px"
    >
      <n-form-item label="启用代理" path="enabled">
        <n-switch v-model:value="formData.enabled" />
        <span class="ml-2 text-gray-500">启用后所有网络请求将通过代理服务器</span>
      </n-form-item>

      <template v-if="formData.enabled">
        <n-form-item label="代理类型" path="proxy_type">
          <n-select
            v-model:value="formData.proxy_type"
            :options="proxyTypeOptions"
            placeholder="选择代理类型"
          />
        </n-form-item>

        <n-form-item label="代理地址" path="proxy_url">
          <n-input
            v-model:value="formData.proxy_url"
            placeholder="例如: http://username:<EMAIL>:8080"
            clearable
          />
        </n-form-item>
      </template>

      <!-- 测试连接区域 - 始终显示 -->
      <n-form-item label="连接测试">
        <n-space>
          <n-button type="primary" :loading="testing" @click="testConnection" :disabled="false">
            {{ formData.enabled ? '测试代理连接' : '测试直连' }}
          </n-button>
          <span class="text-gray-500 text-sm">
            {{ formData.enabled ? '测试通过代理服务器的连接' : '测试当前网络的直接连接' }}
          </span>
        </n-space>
      </n-form-item>

      <!-- 测试结果显示 -->
      <n-form-item v-if="testResult" label="测试结果">
        <n-card size="small" :class="testResult.success ? 'border-green-200' : 'border-red-200'">
          <div class="space-y-2">
            <div class="flex items-center">
              <n-icon :color="testResult.success ? '#10b981' : '#ef4444'" size="16">
                <CheckmarkCircleOutline v-if="testResult.success" />
                <CloseCircleOutline v-else />
              </n-icon>
              <span class="ml-2 font-medium">
                {{ testResult.success ? '连接成功' : '连接失败' }}
              </span>
            </div>

            <div v-if="testResult.success" class="text-sm text-gray-600 space-y-1">
              <div v-if="testResult.ip">IP地址: {{ testResult.ip }}</div>
              <div v-if="testResult.country">
                位置: {{ testResult.country }} {{ testResult.region }} {{ testResult.city }}
              </div>
              <div v-if="testResult.isp">ISP: {{ testResult.isp }}</div>
              <div v-if="testResult.latency_ms">延迟: {{ testResult.latency_ms }}ms</div>
            </div>

            <div v-if="!testResult.success && testResult.error" class="text-sm text-red-600">
              错误: {{ testResult.error }}
            </div>
          </div>
        </n-card>
      </n-form-item>
    </n-form>

    <template #action>
      <n-space>
        <n-button @click="modalVisible = false">取消</n-button>
        <n-button
          v-if="formData.enabled || hasExistingConfig"
          type="error"
          ghost
          @click="resetConfig"
          :loading="resetting"
        >
          重置
        </n-button>
        <n-button type="primary" @click="handleSave" :loading="saving"> 保存 </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import {
  NModal,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NSwitch,
  NButton,
  NSpace,
  NCard,
  NIcon,
  useMessage,
  type FormInst,
  type FormRules,
} from 'naive-ui'
import { CheckmarkCircleOutline, CloseCircleOutline } from '@vicons/ionicons5'
import { mailboxApi, type ProxyConfigData, type ProxyTestResult } from '@/api/mailbox'

// Props & Emits
interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Reactive data
const modalVisible = ref(false)
const formRef = ref<FormInst>()
const message = useMessage()

const formData = reactive<ProxyConfigData>({
  enabled: false,
  proxy_url: '',
  proxy_type: 'http',
})

const testResult = ref<ProxyTestResult | null>(null)
const testing = ref(false)
const saving = ref(false)
const resetting = ref(false)
const hasExistingConfig = ref(false)

// Options
const proxyTypeOptions = [
  { label: 'HTTP/HTTPS', value: 'http' },
  { label: 'SOCKS5', value: 'socks5' },
]

// Form rules
const rules: FormRules = {
  proxy_url: [
    {
      required: true,
      trigger: ['blur', 'input'],
      validator: (_rule, value) => {
        if (!formData.enabled) return true
        if (!value) return new Error('请输入代理地址')

        // 简单的URL格式验证
        const urlPattern = /^(https?|socks5):\/\/.+/
        if (!urlPattern.test(value)) {
          return new Error('代理地址格式错误，应为: protocol://[username:password@]host:port')
        }
        return true
      },
    },
  ],
}

// Watch props
watch(
  () => props.visible,
  newVal => {
    modalVisible.value = newVal
    if (newVal) {
      loadConfig()
      testResult.value = null
    }
  }
)

watch(modalVisible, newVal => {
  emit('update:visible', newVal)
})

// Watch form data changes
watch(
  () => formData.enabled,
  () => {
    testResult.value = null
  }
)

watch(
  () => formData.proxy_url,
  () => {
    testResult.value = null
  }
)

// Methods
const loadConfig = async () => {
  try {
    console.log('[ProxyConfigModal] 开始加载代理配置...')
    const response = await mailboxApi.getProxyConfig()
    console.log('[ProxyConfigModal] API响应:', response)
    console.log('[ProxyConfigModal] response.data:', response.data)

    // 检查响应数据结构 - 处理可能的嵌套结构
    let proxyData: any = response.data

    // 如果response.data有data属性，说明是嵌套结构
    if (response.data && typeof (response.data as any).data !== 'undefined') {
      proxyData = (response.data as any).data
      console.log('[ProxyConfigModal] 检测到嵌套结构，使用response.data.data:', proxyData)
    } else {
      console.log('[ProxyConfigModal] 使用直接结构，response.data:', proxyData)
    }

    if (proxyData && typeof proxyData === 'object') {
      console.log('[ProxyConfigModal] 更新前的formData:', { ...formData })

      // 明确设置每个字段，确保响应式更新
      formData.enabled = Boolean(proxyData.enabled)
      formData.proxy_url = String(proxyData.proxy_url || '')
      formData.proxy_type = String(proxyData.proxy_type || 'http')

      console.log('[ProxyConfigModal] 更新后的formData:', { ...formData })
      hasExistingConfig.value = Boolean(proxyData.enabled) || Boolean(proxyData.proxy_url)
    } else {
      console.warn('[ProxyConfigModal] 未找到有效的代理配置数据，proxyData:', proxyData)
    }
  } catch (error) {
    console.error('加载代理配置失败:', error)
    message.error('加载代理配置失败')
  }
}

const testConnection = async () => {
  // 允许测试连接，即使代理URL为空（测试直连）
  console.log('[ProxyConfigModal] 开始测试连接...')

  testing.value = true
  testResult.value = null

  try {
    console.log('[ProxyConfigModal] 发送测试请求，配置:', formData)
    const response = await mailboxApi.testProxyConfig(formData)
    console.log('[ProxyConfigModal] 测试响应原始数据:', response)
    console.log('[ProxyConfigModal] response.data:', response.data)

    // 处理可能的嵌套结构
    let testData: any = response.data
    if (response.data && typeof (response.data as any).data !== 'undefined') {
      testData = (response.data as any).data
      console.log('[ProxyConfigModal] 使用嵌套结构 response.data.data:', testData)
    } else {
      console.log('[ProxyConfigModal] 使用直接结构 response.data:', testData)
    }

    if (testData) {
      console.log('[ProxyConfigModal] 设置测试结果:', testData)
      console.log('[ProxyConfigModal] 测试结果详情:', {
        success: testData.success,
        ip: testData.ip,
        country: testData.country,
        city: testData.city,
        region: testData.region,
        isp: testData.isp,
        latency_ms: testData.latency_ms,
        error: testData.error,
      })

      testResult.value = testData

      if (testData.success) {
        message.success('代理连接测试成功')
      } else {
        message.warning('代理连接测试失败')
      }
    } else {
      console.warn('[ProxyConfigModal] 测试数据为空')
    }
  } catch (error) {
    console.error('测试代理连接失败:', error)
    message.error('测试代理连接失败')
    testResult.value = {
      success: false,
      error: '网络请求失败',
    }
  } finally {
    testing.value = false
  }
}

const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    saving.value = true
    await mailboxApi.setProxyConfig(formData)

    message.success('代理配置保存成功')
    emit('success')
    modalVisible.value = false
  } catch (error) {
    console.error('保存代理配置失败:', error)
    if (error instanceof Error) {
      message.error(`保存失败: ${error.message}`)
    } else {
      message.error('保存代理配置失败')
    }
  } finally {
    saving.value = false
  }
}

const resetConfig = async () => {
  resetting.value = true

  try {
    await mailboxApi.deleteProxyConfig()

    // 重置表单数据
    Object.assign(formData, {
      enabled: false,
      proxy_url: '',
      proxy_type: 'http',
    })

    testResult.value = null
    hasExistingConfig.value = false

    message.success('代理配置已重置')
    emit('success')
  } catch (error) {
    console.error('重置代理配置失败:', error)
    message.error('重置代理配置失败')
  } finally {
    resetting.value = false
  }
}
</script>

<style scoped>
.ml-2 {
  margin-left: 0.5rem;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}

.text-red-600 {
  color: #dc2626;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.border-green-200 {
  border-color: #bbf7d0;
}

.border-red-200 {
  border-color: #fecaca;
}

.font-medium {
  font-weight: 500;
}

.text-sm {
  font-size: 0.875rem;
}
</style>
