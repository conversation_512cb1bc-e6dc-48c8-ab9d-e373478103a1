import{u as V,E as $,i as T,a as F,b as K,c as P,d as U,e as G,f as X}from"./charts-C1YvGDjw.js";import{s as H}from"./system-D5OTSIk8.js";import{K as M}from"./Key-DZgkvFqF.js";import{k as h,W as r,Q as l,X as e,r as k,e as Q,c as g,G as c,o as W,S as s,F as I,a2 as D,R as a,K as o,a4 as z,j as C,O as _,a5 as A,a3 as N,$ as p}from"./vendor-RHijBMdK.js";import{M as q}from"./Mail-DFX8DOvB.js";import{A as J}from"./Add-DKKcgvRl.js";import{S as Y}from"./Settings-CsD-lPn5.js";import{v as w,w as Z,x as tt,y as et,B as ot,h as x,A as at,C as st,D as lt,E as nt}from"./ui-B428bLoF.js";import{R as it}from"./Refresh-BNivSboa.js";import{_ as rt}from"./index-BVF_NGWt.js";const ct={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},dt=e("path",{d:"M456 128a40 40 0 0 0-37.23 54.6l-84.17 84.17a39.86 39.86 0 0 0-29.2 0l-60.17-60.17a40 40 0 1 0-74.46 0L70.6 306.77a40 40 0 1 0 22.63 22.63L193.4 229.23a39.86 39.86 0 0 0 29.2 0l60.17 60.17a40 40 0 1 0 74.46 0l84.17-84.17A40 40 0 1 0 456 128z",fill:"currentColor"},null,-1),ut=[dt],_t=h({name:"Analytics",render:function(d,u){return l(),r("svg",ct,ut)}}),mt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},pt=e("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"48",d:"M112 268l144 144l144-144"},null,-1),ht=e("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"48",d:"M256 392V100"},null,-1),vt=[pt,ht],ft=h({name:"ArrowDown",render:function(d,u){return l(),r("svg",mt,vt)}}),wt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},xt=e("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"48",d:"M112 244l144-144l144 144"},null,-1),yt=e("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"48",d:"M256 120v292"},null,-1),kt=[xt,yt],gt=h({name:"ArrowUp",render:function(d,u){return l(),r("svg",wt,kt)}}),Ct={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},At=e("path",{d:"M336 256c-20.56 0-40.44-9.18-56-25.84c-15.13-16.25-24.37-37.92-26-61c-1.74-24.62 5.77-47.26 21.14-63.76S312 80 336 80c23.83 0 45.38 9.06 60.7 25.52c15.47 16.62 23 39.22 21.26 63.63c-1.67 23.11-10.9 44.77-26 61C376.44 246.82 356.57 256 336 256zm66-88z",fill:"currentColor"},null,-1),bt=e("path",{d:"M467.83 432H204.18a27.71 27.71 0 0 1-22-10.67a30.22 30.22 0 0 1-5.26-25.79c8.42-33.81 29.28-61.85 60.32-81.08C264.79 297.4 299.86 288 336 288c36.85 0 71 9 98.71 26.05c31.11 19.13 52 47.33 60.38 81.55a30.27 30.27 0 0 1-5.32 25.78A27.68 27.68 0 0 1 467.83 432z",fill:"currentColor"},null,-1),St=e("path",{d:"M147 260c-35.19 0-66.13-32.72-69-72.93c-1.42-20.6 5-39.65 18-53.62c12.86-13.83 31-21.45 51-21.45s38 7.66 50.93 21.57c13.1 14.08 19.5 33.09 18 53.52c-2.87 40.2-33.8 72.91-68.93 72.91z",fill:"currentColor"},null,-1),$t=e("path",{d:"M212.66 291.45c-17.59-8.6-40.42-12.9-65.65-12.9c-29.46 0-58.07 7.68-80.57 21.62c-25.51 15.83-42.67 38.88-49.6 66.71a27.39 27.39 0 0 0 4.79 23.36A25.32 25.32 0 0 0 41.72 400h111a8 8 0 0 0 7.87-6.57c.11-.63.25-1.26.41-1.88c8.48-34.06 28.35-62.84 57.71-83.82a8 8 0 0 0-.63-13.39c-1.57-.92-3.37-1.89-5.42-2.89z",fill:"currentColor"},null,-1),Mt=[At,bt,St,$t],It=h({name:"People",render:function(d,u){return l(),r("svg",Ct,Mt)}}),Dt={class:"dashboard"},zt={class:"stats-grid"},Nt={class:"stat-content"},Bt={class:"stat-info"},Ot={class:"stat-value"},Rt={class:"stat-label"},jt={key:0,class:"stat-trend"},Et={class:"charts-grid"},Lt={class:"chart-container"},Vt={class:"chart-container"},Tt={key:0,class:"empty-state"},Ft=h({__name:"Dashboard",setup(v){V([T,F,K,P,U,G,X]);const d=k("7d"),u=k(!1),B=[{label:"最近7天",value:"7d"},{label:"最近30天",value:"30d"},{label:"最近90天",value:"90d"}],n=Q({totalActivationCodes:0,usedActivationCodes:0,activeMailboxes:0,totalMails:0}),O=g(()=>[{key:"activation-codes",label:"激活码总数",value:n.totalActivationCodes,icon:c(M),color:"#18a058",trend:5.2},{key:"used-codes",label:"已使用激活码",value:n.usedActivationCodes,icon:c(It),color:"#2080f0",trend:12.5},{key:"active-mailboxes",label:"活跃邮箱",value:n.activeMailboxes,icon:c(q),color:"#f0a020",trend:-2.1},{key:"total-mails",label:"邮件总数",value:n.totalMails,icon:c(_t),color:"#d03050",trend:8.7}]),R=g(()=>({tooltip:{trigger:"axis"},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"]},yAxis:{type:"value"},series:[{name:"新增邮箱",type:"line",data:[12,19,15,22,18,25,20],smooth:!0,itemStyle:{color:"#18a058"}},{name:"活跃邮箱",type:"line",data:[8,15,12,18,14,20,16],smooth:!0,itemStyle:{color:"#2080f0"}}]})),j=g(()=>({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"激活码状态",type:"pie",radius:"50%",data:[{value:n.totalActivationCodes-n.usedActivationCodes,name:"未使用",itemStyle:{color:"#18a058"}},{value:n.usedActivationCodes,name:"已使用",itemStyle:{color:"#2080f0"}},{value:5,name:"已过期",itemStyle:{color:"#d03050"}}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]})),b=k([{id:"1",title:"创建了新的激活码",description:"批量创建了50个激活码",timestamp:new Date(Date.now()-1e3*60*30).toISOString(),icon:c(J),color:"#18a058"},{id:"2",title:"用户使用激活码",description:"激活码 AC123456 被使用",timestamp:new Date(Date.now()-1e3*60*60*2).toISOString(),icon:c(M),color:"#2080f0"},{id:"3",title:"系统配置更新",description:"邮箱过期时间设置为30分钟",timestamp:new Date(Date.now()-1e3*60*60*4).toISOString(),icon:c(Y),color:"#f0a020"}]),E=async()=>{var y,i,t,S;try{u.value=!0;const m=await H.getSystemStats();if(m.success&&m.data){const f=m.data;n.totalActivationCodes=((y=f.activation_codes)==null?void 0:y.total_generated)||0,n.usedActivationCodes=((i=f.activation_codes)==null?void 0:i.used)||0,n.activeMailboxes=((t=f.mailboxes)==null?void 0:t.currently_active)||0,n.totalMails=((S=f.mailboxes)==null?void 0:S.total_allocated)||0}}catch(m){console.error("Failed to fetch system stats:",m)}finally{u.value=!1}},L=()=>{console.log("Refreshing activities...")};return W(()=>{E()}),(y,i)=>(l(),r("div",Dt,[i[2]||(i[2]=e("div",{class:"dashboard-header"},[e("h1",null,"系统概览"),e("p",null,"Go-Mail临时邮箱服务管理后台")],-1)),e("div",zt,[(l(!0),r(I,null,D(O.value,t=>(l(),_(o(w),{class:"stat-card",key:t.key},{default:a(()=>[e("div",Nt,[e("div",{class:"stat-icon",style:A({backgroundColor:t.color+"20"})},[s(o(x),{size:"24",color:t.color},{default:a(()=>[(l(),_(N(t.icon)))]),_:2},1032,["color"])],4),e("div",Bt,[e("div",Ot,p(t.value),1),e("div",Rt,p(t.label),1)])]),t.trend?(l(),r("div",jt,[s(o(x),{size:"14",color:t.trend>0?"#18a058":"#d03050"},{default:a(()=>[t.trend>0?(l(),_(o(gt),{key:0})):(l(),_(o(ft),{key:1}))]),_:2},1032,["color"]),e("span",{style:A({color:t.trend>0?"#18a058":"#d03050"})},p(Math.abs(t.trend))+"% ",5)])):z("",!0)]),_:2},1024))),128))]),e("div",Et,[s(o(w),{title:"邮箱使用趋势",class:"chart-card"},{"header-extra":a(()=>[s(o(Z),{value:d.value,"onUpdate:value":i[0]||(i[0]=t=>d.value=t),options:B,size:"small",style:{width:"120px"}},null,8,["value"])]),default:a(()=>[e("div",Lt,[s(o($),{option:R.value,autoresize:""},null,8,["option"])])]),_:1}),s(o(w),{title:"激活码状态分布",class:"chart-card"},{default:a(()=>[e("div",Vt,[s(o($),{option:j.value,autoresize:""},null,8,["option"])])]),_:1})]),s(o(w),{title:"最近活动",class:"activity-card"},{"header-extra":a(()=>[s(o(ot),{text:"",onClick:L},{icon:a(()=>[s(o(x),null,{default:a(()=>[s(o(it))]),_:1})]),default:a(()=>[i[1]||(i[1]=C(" 刷新 "))]),_:1,__:[1]})]),default:a(()=>[s(o(tt),null,{default:a(()=>[(l(!0),r(I,null,D(b.value,t=>(l(),_(o(at),{key:t.id},{prefix:a(()=>[s(o(nt),{size:"small",style:A({backgroundColor:t.color})},{default:a(()=>[s(o(x),null,{default:a(()=>[(l(),_(N(t.icon)))]),_:2},1024)]),_:2},1032,["style"])]),default:a(()=>[s(o(st),null,{header:a(()=>[C(p(t.title),1)]),description:a(()=>[C(p(t.description),1)]),footer:a(()=>[s(o(lt),{time:new Date(t.timestamp),type:"relative"},null,8,["time"])]),_:2},1024)]),_:2},1024))),128))]),_:1}),b.value.length===0?(l(),r("div",Tt,[s(o(et),{description:"暂无活动记录"})])):z("",!0)]),_:1})]))}}),Yt=rt(Ft,[["__scopeId","data-v-8eeeaad8"]]);export{Yt as default};
